import 'package:flutter_test/flutter_test.dart';
import 'package:foodcorp/models/user_monthlyrecord_model.dart';

// Import the helper functions from profile_page.dart
// Note: In a real scenario, these would be moved to a separate utility file
// Mock HomeCtrl for testing
class MockHomeCtrl {
  MockUser? users;
  MockHomeCtrl({this.users});
}

class MockUser {
  final num? obSubs;
  final num? obShares;
  MockUser({this.obSubs, this.obShares});
}

Map<String, num> calculateOpeningBalances(
    List<UserMonthlyRecordModel> records, MockHomeCtrl ctrl) {
  num obLtLoan = 0;
  num obStLoan = 0;
  num obSubs = ctrl.users?.obSubs ?? 0;
  num obShare = ctrl.users?.obShares ?? 0;

  // Find March record to get the closing balance for April opening
  UserMonthlyRecordModel? marchRecord;
  try {
    marchRecord = records.firstWhere(
      (record) =>
          record.selectedmonth == 3 &&
          record.selectedyear == DateTime.now().year,
    );
  } catch (e) {
    marchRecord = null;
  }

  // Find April record for subscription and share values
  UserMonthlyRecordModel? aprilRecord;
  try {
    aprilRecord = records.firstWhere(
      (record) =>
          record.selectedmonth == 4 &&
          record.selectedyear == DateTime.now().year,
    );
  } catch (e) {
    aprilRecord = null;
  }

  if (marchRecord != null) {
    // April opening balance should be March closing balance
    obLtLoan = marchRecord.ltCb ?? 0;
    obStLoan = marchRecord.stCb ?? 0;
  } else {
    // If no March record, use April's opening balance as fallback
    obLtLoan = aprilRecord?.obLt ?? 0;
    obStLoan = aprilRecord?.obSt ?? 0;
  }

  // For subscription and share, use April's values
  if (aprilRecord != null) {
    obSubs = aprilRecord.subscriptionPaid;
    obShare = aprilRecord.shareValue;
  }

  return {
    "obLtLoan": obLtLoan,
    "obStLoan": obStLoan,
    "obSubs": obSubs,
    "obShare": obShare,
  };
}

void main() {
  group('Balance Calculation Tests', () {
    test('Should calculate correct opening balance from March closing balance',
        () {
      // Create test data with March closing balance = 83500
      final marchRecord = UserMonthlyRecordModel(
        docId: 'march_doc',
        selectedyear: DateTime.now().year,
        selectedmonth: 3,
        cpfNo: 12345,
        name: 'Test User',
        districtoffice: 'Test Office',
        obLt: 80000, // March opening
        obSt: 5000,
        ltCb: 83500, // March closing - this should be April opening
        stCb: 5200,
        subscriptionPaid: 1000,
        longTermInstalmentPaid: 2000,
        shortTermInstalmentPaid: 500,
        longTermInterestPaid: 100,
        shortTermInterestPaid: 50,
        isPaid: true,
        dues: 0,
        penalty: 0,
        shareValue: 1000,
        societySubsPayout: 0,
        societySharesPayout: 0,
        penaltyPaid: 0,
      );

      final aprilRecord = UserMonthlyRecordModel(
        docId: 'april_doc',
        selectedyear: DateTime.now().year,
        selectedmonth: 4,
        cpfNo: 12345,
        name: 'Test User',
        districtoffice: 'Test Office',
        obLt: 83500, // This is wrong - should be calculated from March closing
        obSt: 5200,
        ltCb: 85000,
        stCb: 5300,
        subscriptionPaid: 1200,
        longTermInstalmentPaid: 2100,
        shortTermInstalmentPaid: 600,
        longTermInterestPaid: 120,
        shortTermInterestPaid: 60,
        isPaid: true,
        dues: 0,
        penalty: 0,
        shareValue: 1200,
        societySubsPayout: 0,
        societySharesPayout: 0,
        penaltyPaid: 0,
      );

      final records = [marchRecord, aprilRecord];
      final mockCtrl =
          MockHomeCtrl(users: MockUser(obSubs: 1200, obShares: 1200));
      final openingBalances = calculateOpeningBalances(records, mockCtrl);

      // April opening balance should be March closing balance
      expect(openingBalances['obLtLoan'], equals(83500));
      expect(openingBalances['obStLoan'], equals(5200));
      expect(openingBalances['obSubs'], equals(1200)); // April subscription
      expect(openingBalances['obShare'], equals(1200)); // April share value
    });

    test(
        'Should fallback to April opening balance when March record is missing',
        () {
      final aprilRecord = UserMonthlyRecordModel(
        docId: 'april_doc',
        selectedyear: DateTime.now().year,
        selectedmonth: 4,
        cpfNo: 12345,
        name: 'Test User',
        districtoffice: 'Test Office',
        obLt: 80000,
        obSt: 5000,
        ltCb: 82000,
        stCb: 5100,
        subscriptionPaid: 1000,
        longTermInstalmentPaid: 2000,
        shortTermInstalmentPaid: 500,
        longTermInterestPaid: 100,
        shortTermInterestPaid: 50,
        isPaid: true,
        dues: 0,
        penalty: 0,
        shareValue: 1000,
        societySubsPayout: 0,
        societySharesPayout: 0,
        penaltyPaid: 0,
      );

      final records = [aprilRecord]; // No March record
      final mockCtrl =
          MockHomeCtrl(users: MockUser(obSubs: 1000, obShares: 1000));
      final openingBalances = calculateOpeningBalances(records, mockCtrl);

      // Should use April's opening balance as fallback
      expect(openingBalances['obLtLoan'], equals(80000));
      expect(openingBalances['obStLoan'], equals(5000));
      expect(openingBalances['obSubs'], equals(1000));
      expect(openingBalances['obShare'], equals(1000));
    });

    test('Should handle empty records gracefully', () {
      final records = <UserMonthlyRecordModel>[];
      final mockCtrl = MockHomeCtrl(users: MockUser(obSubs: 0, obShares: 0));
      final openingBalances = calculateOpeningBalances(records, mockCtrl);

      expect(openingBalances['obLtLoan'], equals(0));
      expect(openingBalances['obStLoan'], equals(0));
      expect(openingBalances['obSubs'], equals(0));
      expect(openingBalances['obShare'], equals(0));
    });
  });
}
