import 'package:flutter_test/flutter_test.dart';
import 'package:foodcorp/models/notifications_model.dart';
import 'package:foodcorp/models/custom_notifications_model.dart';
import 'package:foodcorp/models/poll_model.dart';

void main() {
  group('Notifications Page Tests', () {
    test('Should sort notifications and polls by date (latest first)', () {
      // Create test data with different dates
      final now = DateTime.now();
      final yesterday = now.subtract(const Duration(days: 1));
      final twoDaysAgo = now.subtract(const Duration(days: 2));

      // Create test notifications
      final notification1 = NotificationsModel(
        docId: 'notif1',
        uId: 'user1',
        title: 'Old Notification',
        desc: 'This is an old notification',
        type: 'info',
        districtOffice: 'Test District',
        createdAt: twoDaysAgo,
        attachment: null,
      );

      final notification2 = NotificationsModel(
        docId: 'notif2',
        uId: 'user1',
        title: 'Recent Notification',
        desc: 'This is a recent notification',
        type: 'info',
        districtOffice: 'Test District',
        createdAt: now,
        attachment: null,
      );

      // Create test poll
      final poll = PollModel(
        id: 'poll1',
        question: 'Test Poll Question?',
        options: ['Option 1', 'Option 2'],
        votes: {'Option 1': 5, 'Option 2': 3},
        userVotes: {},
        isActive: true,
        allowsMultipleAnswers: false,
        expiryDate: yesterday,
      );

      // Simulate the sorting logic from the notifications page
      final allItems = <Map<String, dynamic>>[
        {
          'type': 'notification',
          'id': notification1.docId,
          'title': notification1.title,
          'desc': notification1.desc,
          'createdAt': notification1.createdAt,
          'source': 'regular',
        },
        {
          'type': 'notification',
          'id': notification2.docId,
          'title': notification2.title,
          'desc': notification2.desc,
          'createdAt': notification2.createdAt,
          'source': 'regular',
        },
        {
          'type': 'poll',
          'id': poll.id,
          'poll': poll,
          'createdAt': poll.expiryDate!,
          'source': 'poll',
        },
      ];

      // Sort by creation date (latest first)
      allItems.sort((a, b) => (b['createdAt'] as DateTime).compareTo(a['createdAt'] as DateTime));

      // Verify sorting order
      expect(allItems[0]['id'], equals('notif2')); // Most recent
      expect(allItems[1]['id'], equals('poll1'));  // Yesterday
      expect(allItems[2]['id'], equals('notif1')); // Oldest
    });

    test('Should handle empty notifications and polls list', () {
      final allItems = <Map<String, dynamic>>[];
      
      // Should not throw error when empty
      allItems.sort((a, b) => (b['createdAt'] as DateTime).compareTo(a['createdAt'] as DateTime));
      
      expect(allItems.isEmpty, isTrue);
    });

    test('Should correctly identify notification sources', () {
      // Test helper functions logic
      String getSourceLabel(String source) {
        switch (source) {
          case 'regular':
            return 'PERSONAL';
          case 'district':
            return 'DISTRICT';
          case 'global':
            return 'GLOBAL';
          case 'poll':
            return 'POLL';
          default:
            return 'NOTIFICATION';
        }
      }

      expect(getSourceLabel('regular'), equals('PERSONAL'));
      expect(getSourceLabel('district'), equals('DISTRICT'));
      expect(getSourceLabel('global'), equals('GLOBAL'));
      expect(getSourceLabel('poll'), equals('POLL'));
      expect(getSourceLabel('unknown'), equals('NOTIFICATION'));
    });

    test('Should handle poll voting logic', () {
      final poll = PollModel(
        id: 'poll1',
        question: 'Test Poll Question?',
        options: ['Option 1', 'Option 2', 'Option 3'],
        votes: {'Option 1': 5, 'Option 2': 3, 'Option 3': 1},
        userVotes: {'user123': 'Option 1'},
        isActive: true,
        allowsMultipleAnswers: false,
        expiryDate: DateTime.now().add(const Duration(days: 7)),
      );

      // Test if user has already voted
      final currentUserId = 'user123';
      final hasVotedInFirestore = poll.userVotes?.containsKey(currentUserId) ?? false;
      
      expect(hasVotedInFirestore, isTrue);
      expect(poll.userVotes?[currentUserId], equals('Option 1'));
    });

    test('Should handle multiple answer polls', () {
      final poll = PollModel(
        id: 'poll2',
        question: 'Select all that apply',
        options: ['Option A', 'Option B', 'Option C'],
        votes: {'Option A': 3, 'Option B': 5, 'Option C': 2},
        userVotes: {'user456': ['Option A', 'Option C']},
        isActive: true,
        allowsMultipleAnswers: true,
        expiryDate: DateTime.now().add(const Duration(days: 5)),
      );

      final currentUserId = 'user456';
      final userVote = poll.userVotes?[currentUserId];
      
      expect(poll.allowsMultipleAnswers, isTrue);
      expect(userVote, isA<List>());
      expect((userVote as List).length, equals(2));
      expect(userVote.contains('Option A'), isTrue);
      expect(userVote.contains('Option C'), isTrue);
    });
  });
}
