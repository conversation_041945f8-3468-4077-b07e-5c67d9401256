import 'package:flutter_test/flutter_test.dart';
import 'package:foodcorp/models/user_monthlyrecord_model.dart';

/// Test function to generate dynamic balance date
String getBalanceAsOnDate(List<UserMonthlyRecordModel> userMonthlyRecords) {
  if (userMonthlyRecords.isEmpty) {
    return 'BALANCE AS ON 31-3-${DateTime.now().year}';
  }
  
  // Find the last month with data
  final lastRecord = userMonthlyRecords.last;
  final lastMonth = lastRecord.selectedmonth ?? 3;
  final year = lastRecord.selectedyear ?? DateTime.now().year;
  
  // Get last day of the month
  final lastDayOfMonth = DateTime(year, lastMonth + 1, 0).day;
  
  return 'BALANCE AS ON $lastDayOfMonth-$lastMonth-$year';
}

void main() {
  group('Dynamic Date Generation Tests', () {
    test('Should generate correct date for March (31 days)', () {
      final marchRecord = UserMonthlyRecordModel(
        docId: 'march_doc',
        selectedyear: 2025,
        selectedmonth: 3,
        cpfNo: 12345,
        name: 'Test User',
        districtoffice: 'Test Office',
        subscriptionPaid: 1000,
        longTermInstalmentPaid: 2000,
        shortTermInstalmentPaid: 500,
        longTermInterestPaid: 100,
        shortTermInterestPaid: 50,
        isPaid: true,
        dues: 0,
        penalty: 0,
        shareValue: 1000,
        societySubsPayout: 0,
        societySharesPayout: 0,
        penaltyPaid: 0,
      );

      final records = [marchRecord];
      final result = getBalanceAsOnDate(records);
      
      expect(result, equals('BALANCE AS ON 31-3-2025'));
    });

    test('Should generate correct date for February (28 days in non-leap year)', () {
      final febRecord = UserMonthlyRecordModel(
        docId: 'feb_doc',
        selectedyear: 2025,
        selectedmonth: 2,
        cpfNo: 12345,
        name: 'Test User',
        districtoffice: 'Test Office',
        subscriptionPaid: 1000,
        longTermInstalmentPaid: 2000,
        shortTermInstalmentPaid: 500,
        longTermInterestPaid: 100,
        shortTermInterestPaid: 50,
        isPaid: true,
        dues: 0,
        penalty: 0,
        shareValue: 1000,
        societySubsPayout: 0,
        societySharesPayout: 0,
        penaltyPaid: 0,
      );

      final records = [febRecord];
      final result = getBalanceAsOnDate(records);
      
      expect(result, equals('BALANCE AS ON 28-2-2025'));
    });

    test('Should generate correct date for April (30 days)', () {
      final aprilRecord = UserMonthlyRecordModel(
        docId: 'april_doc',
        selectedyear: 2025,
        selectedmonth: 4,
        cpfNo: 12345,
        name: 'Test User',
        districtoffice: 'Test Office',
        subscriptionPaid: 1000,
        longTermInstalmentPaid: 2000,
        shortTermInstalmentPaid: 500,
        longTermInterestPaid: 100,
        shortTermInterestPaid: 50,
        isPaid: true,
        dues: 0,
        penalty: 0,
        shareValue: 1000,
        societySubsPayout: 0,
        societySharesPayout: 0,
        penaltyPaid: 0,
      );

      final records = [aprilRecord];
      final result = getBalanceAsOnDate(records);
      
      expect(result, equals('BALANCE AS ON 30-4-2025'));
    });

    test('Should use last record when multiple months exist', () {
      final marchRecord = UserMonthlyRecordModel(
        docId: 'march_doc',
        selectedyear: 2025,
        selectedmonth: 3,
        cpfNo: 12345,
        name: 'Test User',
        districtoffice: 'Test Office',
        subscriptionPaid: 1000,
        longTermInstalmentPaid: 2000,
        shortTermInstalmentPaid: 500,
        longTermInterestPaid: 100,
        shortTermInterestPaid: 50,
        isPaid: true,
        dues: 0,
        penalty: 0,
        shareValue: 1000,
        societySubsPayout: 0,
        societySharesPayout: 0,
        penaltyPaid: 0,
      );

      final juneRecord = UserMonthlyRecordModel(
        docId: 'june_doc',
        selectedyear: 2025,
        selectedmonth: 6,
        cpfNo: 12345,
        name: 'Test User',
        districtoffice: 'Test Office',
        subscriptionPaid: 1000,
        longTermInstalmentPaid: 2000,
        shortTermInstalmentPaid: 500,
        longTermInterestPaid: 100,
        shortTermInterestPaid: 50,
        isPaid: true,
        dues: 0,
        penalty: 0,
        shareValue: 1000,
        societySubsPayout: 0,
        societySharesPayout: 0,
        penaltyPaid: 0,
      );

      final records = [marchRecord, juneRecord];
      final result = getBalanceAsOnDate(records);
      
      // Should use June (last record)
      expect(result, equals('BALANCE AS ON 30-6-2025'));
    });

    test('Should handle empty records gracefully', () {
      final records = <UserMonthlyRecordModel>[];
      final result = getBalanceAsOnDate(records);
      
      expect(result, equals('BALANCE AS ON 31-3-${DateTime.now().year}'));
    });
  });
}
