// web/firebase-messaging-sw.js

importScripts('https://www.gstatic.com/firebasejs/10.8.1/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/10.8.1/firebase-messaging-compat.js');

firebase.initializeApp({
  apiKey: 'AIzaSyCul81IM_AwbYg9WW6QTh2z2SUVBKj4y30',
  appId: '1:63752906252:web:1920522ad17d1b70713029',
  messagingSenderId: '63752906252',
  projectId: 'food-corp-india',
  authDomain: 'food-corp-india.firebaseapp.com',
  storageBucket: 'food-corp-india.appspot.com',
}); 

const messaging = firebase.messaging();

// Optional: Handle background messages
messaging.onBackgroundMessage(function(payload) {
  console.log('[firebase-messaging-sw.js] Received background message ', payload);
  const notificationTitle = payload.notification.title;
  const notificationOptions = {
    body: payload.notification.body,
    // icon: '/icons/icon-192.png', // optional
  };

  self.registration.showNotification(notificationTitle, notificationOptions);
});
