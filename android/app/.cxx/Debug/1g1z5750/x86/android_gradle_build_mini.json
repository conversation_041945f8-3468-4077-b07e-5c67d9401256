{"buildFiles": ["C:\\Users\\<USER>\\Documents\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "F:\\GitProjects\\food_corp_user\\android\\app\\.cxx\\Debug\\1g1z5750\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "F:\\GitProjects\\food_corp_user\\android\\app\\.cxx\\Debug\\1g1z5750\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}