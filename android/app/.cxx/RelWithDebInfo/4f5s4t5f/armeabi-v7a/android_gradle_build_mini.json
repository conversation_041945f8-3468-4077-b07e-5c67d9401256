{"buildFiles": ["C:\\Users\\<USER>\\Documents\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "F:\\GitProjects\\food_corp_user\\android\\app\\.cxx\\RelWithDebInfo\\4f5s4t5f\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "F:\\GitProjects\\food_corp_user\\android\\app\\.cxx\\RelWithDebInfo\\4f5s4t5f\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}