                        -HC:\Users\<USER>\Documents\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=23
-<PERSON><PERSON><PERSON>OID_PLATFORM=android-23
-DANDROID_ABI=x86
-DCMAKE_ANDROID_ARCH_ABI=x86
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\26.3.11579264
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\26.3.11579264
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\26.3.11579264\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=F:\GitProjects\food_corp_user\build\app\intermediates\cxx\RelWithDebInfo\4f5s4t5f\obj\x86
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=F:\GitProjects\food_corp_user\build\app\intermediates\cxx\RelWithDebInfo\4f5s4t5f\obj\x86
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-BF:\GitProjects\food_corp_user\android\app\.cxx\RelWithDebInfo\4f5s4t5f\x86
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2