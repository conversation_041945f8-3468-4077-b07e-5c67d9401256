import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:url_launcher/url_launcher_string.dart';
import 'firebase.dart';

bool isLoggedIn() =>
// true;
    FBAuth.auth.currentUser != null;

_onTap() async {
  try {
    launchUrlString(
      "https://www.diwizon.com",
      mode: LaunchMode.externalApplication,
    );
  } catch (e) {
    debugPrint(e.toString());
  }
}

TextSpan developedByText(double? fontsize) {
  return TextSpan(
    text: "Diwizon",
    style: TextStyle(
        shadows: const [
          Shadow(color: Colors.white, offset: Offset(0, -2)),
        ],
        color: Colors.transparent,
        fontSize: fontsize,
        decorationColor: Colors.white,
        decoration: TextDecoration.underline
        // color: Colors.redAccent.shade400,
        ),
    recognizer: TapGestureRecognizer()..onTap = _onTap,
  );
}

class CustomCont extends StatelessWidget {
  const CustomCont(
      {super.key, required this.titletext, this.amount, this.inkwellOnTap});

  final String? titletext;
  final num? amount;
  final void Function()? inkwellOnTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      // focusColor: Colors.transparent,
      hoverColor: Colors.transparent,
      overlayColor: WidgetStatePropertyAll(Colors.transparent),
      onTap: inkwellOnTap,
      child: Container(
        // width: 184.w,
        // height: 80.h,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(10)),
            color: Colors.grey.shade300),
        child: Padding(
          padding: const EdgeInsets.all(12).w,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text("$titletext",
                  style: GoogleFonts.sourceCodePro(
                    wordSpacing: -1.1,
                    fontWeight: FontWeight.w600,
                    fontSize: 14.sp,
                    color: const Color.fromRGBO(106, 106, 106, 1),
                  )),
              // SizedBox(height: 6.h),
              Text(
                "₹$amount",
                style: TextStyle(fontSize: 22.sp, fontWeight: FontWeight.w600),
              )
            ],
          ),
        ),
      ),
    );
  }
}

final GlobalKey<ScaffoldMessengerState> snackbarKey =
    GlobalKey<ScaffoldMessengerState>();
showAppSnackBar(String message,
        {SnackBarAction? action,
        Duration duration = const Duration(milliseconds: 1800)}) =>
    snackbarKey.currentState?.showSnackBar(SnackBar(
      content: Text(
        message,
        style: const TextStyle(color: Colors.white),
      ),
      action: action,
      duration: duration,
    ));

class CustomSetTextField extends StatelessWidget {
  final String labelText;
  final TextEditingController controller;
  final Function(String)? onFieldSubmitted;
  // final VoidCallback onPressed;

  const CustomSetTextField({
    super.key,
    required this.labelText,
    required this.controller,
    this.onFieldSubmitted,
    // required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Text(
          labelText,
          style: const TextStyle(fontSize: 20),
        ),
        const SizedBox(width: 18),
        SizedBox(
          width: 500,
          child: TextFormField(
            onFieldSubmitted: onFieldSubmitted,
            controller: controller,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              hintText: 'Enter text here',
            ),
          ),
        ),
        const SizedBox(width: 12),
        // ElevatedButton(
        //   style: ButtonStyle(
        //       fixedSize:
        //       backgroundColor: WidgetStatePropertyAll(Colors.blueGrey.shade50),
        //       padding: const WidgetStatePropertyAll(
        //           EdgeInsetsDirectional.symmetric(
        //               horizontal: 30, vertical: 23)),
        //       elevation: const WidgetStatePropertyAll(0),
        //       shape: const WidgetStatePropertyAll(ContinuousRectangleBorder(
        //           borderRadius: BorderRadius.all(Radius.circular(5))))),
        //   onPressed: onPressed,
        //   child: const Text(
        //     'Submit',
        //     style: TextStyle(
        //         fontSize: 15, fontWeight: FontWeight.w400, color: Colors.black),
        //   ),
        // ),
      ],
    );
  }
}

class HeaderTxt extends StatelessWidget {
  const HeaderTxt({
    super.key,
    required this.txt,
  });
  final String txt;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Text(
        txt,
        style: const TextStyle(fontWeight: FontWeight.w600),
      ),
    );
  }
}

class DeleteButton extends StatelessWidget {
  const DeleteButton({
    super.key,
    required this.onDelete,
  });

  final Function onDelete;

  @override
  Widget build(BuildContext context) {
    return IconButton(
        onPressed: () async {
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              backgroundColor: Colors.white,
              title: const Text("Delete!!"),
              content: const Text("Are you sure you want to delete?"),
              actions: [
                TextButton(
                    onPressed: () async {
                      onDelete();
                    },
                    child: const Text("Yes")),
                TextButton(
                    onPressed: () async {
                      if (context.mounted) {
                        context.pop();
                      }
                    },
                    child: const Text("No")),
              ],
            ),
          );
        },
        icon: const Icon(Icons.delete));
  }
}

class CustomSetTextfield2 extends StatelessWidget {
  final String text;
  final TextEditingController controller;
  final Function(String)? onFieldSubmitted;
  final bool enabled;
  final TextInputType? keyboardType;

  const CustomSetTextfield2(
      {super.key,
      required this.text,
      required this.controller,
      this.onFieldSubmitted,
      required this.enabled,
      this.keyboardType});
  @override
  Widget build(BuildContext context) {
    return Flex(
      direction: Axis.horizontal,
      children: [
        Expanded(
          // flex: 5,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    "$text *",
                    style: const TextStyle(fontSize: 15),
                  ),
                ],
              ),
              TextFormField(
                keyboardType: keyboardType,
                enabled: enabled,
                decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                    hintText: 'Enter text here',
                    enabled: false),
                controller: controller,
                // onSubmitted: onFieldSubmitted,
                onFieldSubmitted: onFieldSubmitted,
              ),
              const SizedBox(height: 20)
            ],
          ),
        ),
      ],
    );
  }
}

class CustomListTile extends StatelessWidget {
  const CustomListTile({
    super.key,
    required this.title,
    required this.onPressed,
    this.subtitle,
  });

  final String title;
  final Widget? subtitle;
  final Function onPressed;

  @override
  Widget build(BuildContext context) {
    return ListTile(
      subtitle: subtitle,
      title: Text(
        "$title *",
        style: const TextStyle(fontSize: 17, fontWeight: FontWeight.w500),
      ),
      trailing: ElevatedButton(
          style: ElevatedButton.styleFrom(
              backgroundColor: Colors.transparent,
              // Colors.blue[200],
              elevation: 0,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                  side: const BorderSide(color: Colors.black),
                  borderRadius: BorderRadius.circular(4))),
          onPressed: () {
            onPressed();
          },
          child: const Text(
            "Upload File",
            style: TextStyle(color: Colors.black),
          )),
    );
  }
}

class CustomProfileRow extends StatefulWidget {
  const CustomProfileRow({super.key, required this.data, this.title});

  final String? data;
  final String? title;

  @override
  State<CustomProfileRow> createState() => _CustomProfileRowState();
}

class _CustomProfileRowState extends State<CustomProfileRow> {
  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Text("${widget.title}",
              style: TextStyle(
                  fontWeight: FontWeight.w800,
                  overflow: TextOverflow.ellipsis)),
        ),
        // SizedBox(width: 10.w),
        Expanded(
          child: Text("${widget.data}",
              style: GoogleFonts.poppins(
                  fontSize: 15.sp,
                  fontWeight: FontWeight.w500,
                  // textStyle: TextStyle(overflow: TextOverflow.ellipsis),
                  color: Colors.black)),
        ),
      ],
    );
  }
}

String monthShortName(int month) {
  const names = [
    '',
    'JAN',
    'FEB',
    'MAR',
    'APR',
    'MAY',
    'JUN',
    'JUL',
    'AUG',
    'SEP',
    'OCT',
    'NOV',
    'DEC'
  ];

  return (month >= 1 && month <= 12) ? names[month] : 'Invalid';
}

String getMonthName(dynamic month) {
  int monthNumber;
  if (month is String) {
    monthNumber = int.tryParse(month) ?? 1;
  } else if (month is int) {
    monthNumber = month;
  } else {
    monthNumber = 1;
  }

  if (monthNumber >= 1 && monthNumber <= 12) {
    return monthShortName(monthNumber);
  } else {
    return 'Unknown';
  }
}
