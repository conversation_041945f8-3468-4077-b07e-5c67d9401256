import 'dart:async';
import 'package:flutter/material.dart';
import 'package:foodcorp/controller/homectrl.dart';
import 'package:foodcorp/shared/methods.dart';
import 'package:foodcorp/views/dashboard/dashboard_page.dart';
import 'package:foodcorp/views/dashboard/loan_Details_page.dart';
import 'package:foodcorp/views/dashboard/more_loan_details.dart';
import 'package:foodcorp/views/loan_selection_page.dart';
import 'package:foodcorp/views/notifications/notifications_page.dart';
import 'package:foodcorp/views/profile/edit_history_page.dart';
import 'package:foodcorp/views/profile/edit_page.dart';
import 'package:foodcorp/views/profile/loan_calculator_page.dart';
import 'package:foodcorp/views/profile/loan_history_page.dart';
import 'package:foodcorp/views/profile/profile_page.dart';
import 'package:foodcorp/views/registration/payment_page.dart';
import 'package:foodcorp/views/registration/registration_page.dart';
import 'package:foodcorp/views/splash_screen.dart';
import 'package:foodcorp/views/terms%20and%20condition/ltform_tc.dart';
import 'package:foodcorp/views/terms%20and%20condition/stform_tc.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import '../views/Loan/ltloanform.dart';
import '../views/Loan/stloanform.dart';
import '../views/login/login_page.dart';

const homeRoute = Routes.dashboard;

final GoRouter appRouter = GoRouter(
  debugLogDiagnostics: true,
  initialLocation: isLoggedIn() ? Routes.dashboard : Routes.login,
  routes: _routes,
  redirect: redirector,
  refreshListenable: Get.find<HomeCtrl>(),
);

FutureOr<String?> redirector(BuildContext context, GoRouterState state) {
  if (isLoggedIn()) {
    if (state.fullPath == Routes.login || state.fullPath == Routes.register) {
      if (Get.isRegistered<HomeCtrl>()) {
        // Get.find<HomeCtrl>().update();
      }
      return Routes.dashboard;
    }
    return null; // Continue navigation
  } else {
    // If not logged in, ensure correct routing
    if (state.fullPath == Routes.register ||
        state.fullPath == Routes.paymentpage) {
      return null;
    }
    return Routes.login; // Redirect to login if not logged in
  }
}

List<RouteBase> get _routes {
  return <RouteBase>[
    // ShellRoute(
    //     builder: (context, state, child) {
    //       if (!Get.isRegistered<HomeCtrl>()) Get.put(HomeCtrl());
    //       return const Wrapper();
    //     },
    //     routes: []),
    GoRoute(
      path: Routes.dashboard,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: DashboardPage()),
    ),
    GoRoute(
      path: Routes.ltloanform,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: Ltloanform()),
    ),
    GoRoute(
      path: Routes.stloanform,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: Stloanform()),
    ),
    GoRoute(
      path: Routes.login,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: Loginpage()),
    ),
    GoRoute(
      path: Routes.register,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: RegistrationPage()),
    ),
    GoRoute(
      path: Routes.loanselection,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: LoanSelectionPage()),
    ),
    GoRoute(
      path: Routes.notifications,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: NotificationsPage()),
    ),
    GoRoute(
      path: Routes.profile,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: ProfilePage()),
    ),
    GoRoute(
      path: Routes.edit,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: EditPage()),
    ),
    GoRoute(
      path: Routes.splashscreen,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: SplashScreen()),
    ),
    GoRoute(
      path: Routes.ltloantc,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: LtLoanTc()),
    ),
    GoRoute(
      path: Routes.stloantc,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: StLoanTc()),
    ),
    GoRoute(
      path: Routes.loanhistory,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: LoanHistoryPage()),
    ),
    GoRoute(
      path: Routes.loancalc,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: LoanCalculatorPage()),
    ),
    GoRoute(
      path: Routes.paymentpage,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: PaymentPage()),
    ),
    GoRoute(
      path: Routes.edithistory,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: EditHistoryPage()),
    ),
    GoRoute(
      path: Routes.loandetails,
      pageBuilder: (BuildContext context, GoRouterState state) {
        final loanType = state.extra as String?;
        return NoTransitionPage(child: LoanDetailsPage(loanType: loanType));
      },
    ),
    GoRoute(
      path: '${Routes.moreloandetails}/:id',
      pageBuilder: (BuildContext context, GoRouterState state) {
        final id = state.pathParameters['id'];
        final loanType = state.extra as String?;

        // print("Navigated to Loan ID: $id, Type: $loanType");

        return NoTransitionPage(
          child: MoreLoanDetails(
            loanId: id,
          ),
        );
      },
    ),
  ];
}

class Routes {
  static const login = '/login';
  static const register = '/register';
  static const dashboard = '/dashboard';
  static const ltloanform = '/ltloanform';
  static const stloanform = '/stloanform';
  static const loanselection = '/loanselection';
  static const notifications = '/notifications';
  static const profile = '/profile';
  static const splashscreen = '/splashscreen';
  static const ltloantc = '/termsandconditions';
  static const stloantc = '/termsandconditions';
  static const loandetails = '/loandetails';
  static const moreloandetails = '/moreloandetails';
  static const loanhistory = '/loanhistory';
  static const loancalc = '/loancalc';
  static const paymentpage = '/paymentpage';
  static const edit = '/edit';
  static const edithistory = '/edithistory';
}
