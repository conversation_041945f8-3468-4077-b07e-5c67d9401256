import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:foodcorp/shared/const.dart';

class FBAuth {
  static final auth = FirebaseAuth.instance;
}

class FBFireStore {
  static final fb = FirebaseFirestore.instance;
  static final settings =
      fb.collection(testMode ? 'testSettings' : 'settings').doc('sets');
  static final districtoffice =
      fb.collection(testMode ? 'testDistrictOffice' : 'DistrictOffice');
  static final records = fb.collection(testMode ? 'testRecords' : 'Records');
  static final requests = fb.collection(testMode ? 'testRequests' : 'requests');
  static final expense = fb.collection(testMode ? 'testExpense' : 'Expense');
  static final recovery = fb.collection(testMode ? 'testRecovery' : 'Recovery');
  static final loan = fb.collection(testMode ? 'testLoan' : 'Loan');
  static final users = fb.collection(testMode ? 'testUsers' : 'Users');
  static final newuserapplication =
      fb.collection(testMode ? 'testNewUserApplication' : 'newuserapplication');
  static final transactions =
      fb.collection(testMode ? 'testTransactions' : 'transactions');
  static final usermonthly =
      fb.collection(testMode ? 'testUserMonthly' : 'UserMonthly');
  static final subsidiaryledger =
      fb.collection(testMode ? 'testSubsidiaryLedger' : 'subsidiaryledger');
  static final notifications =
      fb.collection(testMode ? 'testNotifications' : 'notifications');
  static final customnotifications = fb
      .collection(testMode ? 'testCustomNotifications' : 'customnotifications');
  static final recoverymonthly =
      fb.collection(testMode ? 'testRecoveryMonthly' : 'RecoveryMonthly');
}

class FBStorage {
  static final fbstore = FirebaseStorage.instance;
}

class FBFunctions {
  static final ff = FirebaseFunctions.instance;
}
