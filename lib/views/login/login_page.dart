import 'dart:async';
import 'package:flutter/material.dart';
import 'package:foodcorp/controller/authctrl.dart';
import 'package:foodcorp/controller/homectrl.dart';
import 'package:foodcorp/shared/methods.dart';
import 'package:foodcorp/shared/router.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';

class Loginpage extends StatefulWidget {
  const Loginpage({super.key});

  @override
  State<Loginpage> createState() => _LoginpageState();
}

class _LoginpageState extends State<Loginpage> {
  @override
  void initState() {
    super.initState();
    final Authctrl ctrl = Get.find<Authctrl>();
    ctrl.resetFields();
  }

  bool isButtonVisible = true; // Add this variable at the class level
  Timer? _timer;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: Stack(
        fit: StackFit.expand,
        children: [
          Container(
              decoration: const BoxDecoration(
                  image: DecorationImage(
            image: AssetImage(
                "assets/images/field-durum-wheat-naturally-grown-wheat.jpg"),
            fit: BoxFit.fill,
          ))),
          GetBuilder<Authctrl>(
            builder: (ctrl) {
              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 15),
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Spacer(flex: 2),

                    //BACKGROUND IMAGE
                    Expanded(
                      flex: 6,
                      child: Center(
                          child: true
                              ? Image.asset(
                                  height: 200,
                                  'assets/images/foodcorpimage4_removebg.png')
                              : Image(
                                  // color: Colors.green.shade300,
                                  fit: BoxFit.contain,
                                  image: const AssetImage(
                                      'assets/images/foodcorpimage4_removebg.png'))),
                    ),
                    const Spacer(flex: 2),
                    Column(
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        //EMAIL TEXTFIELD
                        SizedBox(
                          height: 50,
                          child: TextFormField(
                            decoration: const InputDecoration(
                                focusedBorder: OutlineInputBorder(
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(6)),
                                  borderSide: BorderSide(color: Colors.black),
                                ),
                                filled: true,
                                fillColor: Colors.white,
                                enabledBorder: OutlineInputBorder(
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(6)),
                                  borderSide: BorderSide(color: Colors.white),
                                ),
                                focusColor: Colors.black,
                                border: OutlineInputBorder(
                                    borderRadius:
                                        BorderRadius.all(Radius.circular(6)),
                                    borderSide:
                                        BorderSide(color: Colors.white)),
                                hintText: 'Enter Email',
                                hintStyle: TextStyle(
                                    color: Colors.black,
                                    fontWeight: FontWeight.normal)),
                            style: const TextStyle(
                                color: Colors.black,
                                fontWeight: FontWeight.normal),
                            controller: ctrl.emailctrl,
                          ),
                        ),
                        ctrl.otpsent
                            ? const SizedBox(height: 15)
                            : const Column(),

                        //ENTER OTP TEXTFIELD
                        ctrl.otpsent
                            ? SizedBox(
                                height: 50,
                                child: TextFormField(
                                  keyboardType: TextInputType.number,
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return "Valid email is required!";
                                    }
                                    return null;
                                  },
                                  decoration: const InputDecoration(
                                      focusedBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.all(
                                            Radius.circular(6)),
                                        borderSide:
                                            BorderSide(color: Colors.black),
                                      ),
                                      filled: true,
                                      fillColor: Colors.white,
                                      enabledBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.all(
                                            Radius.circular(6)),
                                        borderSide:
                                            BorderSide(color: Colors.white),
                                      ),
                                      focusColor: Colors.black,
                                      border: OutlineInputBorder(
                                          borderRadius: BorderRadius.all(
                                              Radius.circular(6)),
                                          borderSide:
                                              BorderSide(color: Colors.black)),
                                      hintText: 'Enter OTP',
                                      hintStyle:
                                          TextStyle(color: Colors.black)),
                                  style: const TextStyle(
                                      color: Colors.black,
                                      fontWeight: FontWeight.normal),
                                  controller: ctrl.otpctrl,
                                ),
                              )
                            : const SizedBox(),
                        !ctrl.otpsent
                            ? const SizedBox(height: 15)
                            : const SizedBox(),

                        //SEND OTP BUTTON
                        !ctrl.otpsent
                            ? ctrl.sendingOtp == true
                                ? const CircularProgressIndicator(
                                    color: Colors.black,
                                  )
                                : SizedBox(
                                    width: MediaQuery.sizeOf(context).width,
                                    height: 50,
                                    child: ElevatedButton.icon(
                                      style: ElevatedButton.styleFrom(
                                          shadowColor: Colors.transparent,
                                          backgroundColor: Colors.black,
                                          elevation: 1,
                                          foregroundColor: Colors.white,
                                          shape: RoundedRectangleBorder(
                                              side: const BorderSide(
                                                  color: Colors.black,
                                                  width: 0.25),
                                              borderRadius:
                                                  BorderRadius.circular(6))),
                                      onPressed: () async {
                                        await ctrl.requestOTPEmail(
                                            context: context);
                                      },
                                      label: const Padding(
                                        padding:
                                            EdgeInsets.symmetric(vertical: 12),
                                        child: Text("SEND OTP",
                                            style: TextStyle(
                                                fontSize: 16,
                                                color: Colors.white)),
                                      ),
                                    ),
                                  )
                            : const SizedBox(),
                        const SizedBox(height: 20),

                        //LOGIN BUTTON
                        ctrl.otpsent
                            ? ctrl.lploader == true || ctrl.resendloader == true
                                ? const CircularProgressIndicator(
                                    color: Colors.black,
                                  )
                                : SizedBox(
                                    height: 50,
                                    width: MediaQuery.sizeOf(context).width,
                                    child: ElevatedButton.icon(
                                      style: ElevatedButton.styleFrom(
                                          shadowColor: Colors.transparent,
                                          backgroundColor: Colors.black,
                                          elevation: 1,
                                          foregroundColor: Colors.white,
                                          shape: RoundedRectangleBorder(
                                              side: const BorderSide(
                                                  color: Colors.black,
                                                  width: 0.25),
                                              borderRadius:
                                                  BorderRadius.circular(6))),
                                      onPressed: () async {
                                        if (ctrl.emailctrl.text.isEmpty ||
                                            ctrl.otpctrl.text.isEmpty) {
                                          showAppSnackBar(
                                              "Please fill the required fields");
                                        }
                                        await ctrl.confirmEmailOTP();
                                      },
                                      label: const Padding(
                                        padding: EdgeInsets.symmetric(
                                            vertical: 12, horizontal: 6),
                                        child: Text("LOGIN",
                                            style: TextStyle(
                                                fontSize: 16,
                                                color: Colors.white)),
                                      ),
                                    ),
                                  )
                            : const Column(),
                        // const SizedBox(height: 0),
                        ctrl.otpsent
                            ?
                            // ctrl.resendloader == true
                            //     ? const CircularProgressIndicator(
                            //         color: Colors.black,
                            //       )
                            Align(
                                alignment: Alignment.centerRight,
                                child: isButtonVisible
                                    ? TextButton(
                                        style: const ButtonStyle(
                                            backgroundColor:
                                                WidgetStatePropertyAll(
                                                    Colors.transparent)),
                                        onPressed: () async {
                                          await ctrl.requestResendOTPEmail(
                                              context: context);

                                          setState(() {
                                            isButtonVisible = false;
                                          });

                                          _timer = Timer(
                                              const Duration(minutes: 5), () {
                                            setState(() {
                                              isButtonVisible = true;
                                            });
                                          });
                                        },
                                        child: const Text(
                                          "RESEND OTP",
                                          style: TextStyle(
                                              color: Colors.white,
                                              fontSize: 15),
                                        ),
                                      )
                                    : const SizedBox.shrink(),
                              )
                            : const SizedBox.shrink()
                      ],
                    ),
                    const Spacer(),

                    //BOTTOM ROW

                    Get.find<HomeCtrl>().settings?.applyButton == false
                        ? SizedBox.shrink()
                        : Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              const Text(
                                "Don't have an Account ?",
                                style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 14.5,
                                    fontStyle: FontStyle.italic,
                                    fontWeight: FontWeight.bold),
                              ),
                              TextButton(
                                  style: const ButtonStyle(
                                    overlayColor: WidgetStatePropertyAll(
                                        Colors.transparent),
                                  ),
                                  onPressed: () {
                                    // print("object7");
                                    context.push(Routes.register);
                                    // print("object8");
                                  },
                                  child: const Text(
                                    "REGISTER HERE",
                                    style: TextStyle(
                                        decorationColor: Colors.white,
                                        decoration: TextDecoration.underline,
                                        decorationStyle:
                                            TextDecorationStyle.solid,
                                        fontStyle: FontStyle.italic,
                                        fontSize: 15,
                                        color: Colors.white),
                                  )),
                            ],
                          ),
                    //DIwizon
                    Center(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 5),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text.rich(
                              TextSpan(
                                text: "Developed by ",
                                style: const TextStyle(
                                  shadows: [
                                    Shadow(
                                        color: Colors.white,
                                        offset: Offset(0, -2)),
                                  ],
                                  fontSize: 15,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.transparent,
                                ),
                                children: [
                                  developedByText(15),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(height: 15)
                  ],
                ),
              );
            },
          ),
        ],
      ),
    ));
  }
}
