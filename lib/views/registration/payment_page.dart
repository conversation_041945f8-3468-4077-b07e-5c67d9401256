import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:foodcorp/shared/firebase.dart';
import 'package:qr_flutter/qr_flutter.dart';

class PaymentPage extends StatefulWidget {
  const PaymentPage({super.key});

  @override
  State<PaymentPage> createState() => _PaymentPageState();
}

class _PaymentPageState extends State<PaymentPage> {
  String upiString = "";
  String upiId = "";
  num upiAmt = 0;
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    fetchUpiLink();
  }

  void fetchUpiLink() async {
    final doc = await FBFireStore.settings.get();

    if (doc.exists) {
      setState(() {
        upiString = doc.data()?['upiString'] ?? "";
        upiId = doc.data()?['upiId'] ?? "";
        upiAmt = doc.data()?['upiAmt'] ?? 0;
        isLoading = false;
      });
    } else {
      setState(() {
        upiString = "";
        upiAmt = 0;
        upiId = "";
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text("Payment Page")),
      body: Padding(
        padding: const EdgeInsets.only(bottom: 200.0),
        child: Center(
          child: isLoading
              ? CircularProgressIndicator()
              : upiString.isEmpty
                  ? Text("No UPI QR code available.")
                  : Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text("Scan to Pay",
                            style: TextStyle(
                                fontSize: 18, fontWeight: FontWeight.bold)),
                        SizedBox(height: 20),
                        QrImageView(
                          data: upiString,
                          version: QrVersions.auto,
                          size: 250.h,
                        ),
                        SizedBox(height: 60),
                        Text(
                          "UPI ID : $upiId",
                          style: TextStyle(fontSize: 16),
                        ),
                        SizedBox(height: 20),
                        Text(
                          "AMOUNT: ₹$upiAmt",
                          style: TextStyle(fontSize: 16),
                        ),
                      ],
                    ),
        ),
      ),
    );
  }
}
