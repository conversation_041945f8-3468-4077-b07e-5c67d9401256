import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:foodcorp/controller/homectrl.dart';
import 'package:foodcorp/shared/firebase.dart';
import 'package:foodcorp/shared/methods.dart';
import 'package:foodcorp/shared/router.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import '../../models/districtoffice_model.dart';

class RegistrationPage extends StatefulWidget {
  const RegistrationPage({super.key});

  @override
  State<RegistrationPage> createState() => _RegistrationPageState();
}

class _RegistrationPageState extends State<RegistrationPage> {
  String? selectedoffice;
  List<DistrictOfficeModel> doffice = [];

  TextEditingController namectrl = TextEditingController();
  TextEditingController emailctrl = TextEditingController();
  TextEditingController addresssctrl = TextEditingController();
  TextEditingController paddressctrl = TextEditingController();
  TextEditingController empnoctrl = TextEditingController();
  TextEditingController cpfnoctrl = TextEditingController();
  TextEditingController phonenoctrl = TextEditingController();
  TextEditingController bankacnamectrl = TextEditingController();
  TextEditingController banknamectrl = TextEditingController();
  TextEditingController ifsccodectrl = TextEditingController();
  TextEditingController bankacnoctrl = TextEditingController();
  TextEditingController nomineeNamectrl = TextEditingController();
  TextEditingController nomineeRelationctrl = TextEditingController();

  bool? isChecked = false;
  String? pdfDoc;

  final picker = ImagePicker();
  UploadTask? uploadTask;

  Future<String> uploadFile() async {
    String docUrl = "";

    try {
      if (pdfDoc != null) {
        File file = File(pdfDoc!);

        // Debugging the path
        // print("File path: $pdfDoc");

        if (!await file.exists()) {
          // print("The file does not exist at the path: $pdfDoc");
          return docUrl;
        }

        final fileName = file.path.split("/").last;
        final path = 'files/$fileName';
        final ref = FirebaseStorage.instance.ref().child(path);
        uploadTask = ref.putFile(file);

        final snapshot = await uploadTask!.whenComplete(() => {});
        final urlDownload = await snapshot.ref.getDownloadURL();
        // print('File uploaded: $urlDownload');

        docUrl = urlDownload;
      }

      return docUrl;
    } catch (e) {
      debugPrint("Error uploading file: ${e.toString()}");
      return docUrl;
    }
  }

  // Future<void> getImageFromCamera(String type) async {
  //   final pickedFile = await picker.pickImage(source: ImageSource.camera);
  //   setState(() {
  //     if (pickedFile != null) {
  //       File file = File(pickedFile.path);
  //       switch (type) {
  //         case 'aadharName':
  //           aadharName = file.path.split("/").last;
  //           aadharCard = file;
  //           break;
  //         case 'salarySlipName':
  //           salarySlipName = file.path.split("/").last;
  //           ltSalarySLip = file;
  //           break;
  //         case 'officeidentityName':
  //           officeidentityName = file.path.split("/").last;
  //           officeCard = file;
  //           break;
  //         case 'bankacName':
  //           bankacName = file.path.split("/").last;
  //           bankAccountDetails = file;
  //           break;
  //       }
  //     }
  //   });
  // }

  Future<void> pickFiles() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowMultiple: false,
      allowedExtensions: ['pdf'],
    );

    if (result != null && result.files.isNotEmpty) {
      String filePath = result.files.single.path!; // Keep full file path

      File file = File(filePath);

      int fileSizeInBytes = await file.length();
      int maxSizeInBytes = 3 * 1024 * 1024; // 3MB

      if (fileSizeInBytes <= maxSizeInBytes) {
        setState(() {
          // Storing full file path here, you can split it during upload if needed
          pdfDoc = filePath;
        });
        // print("File selected: $pdfDoc");
      } else {
        // print("File is too large. Please select a file smaller than 3MB.");
      }
    } else {
      // print("No file selected.");
    }
  }

  void removeFile() {
    setState(() {
      pdfDoc = null;
    });
  }

  @override
  void initState() {
    super.initState();
  }

  bool rloading = false;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        toolbarHeight: 60,
        shadowColor: Colors.white,
        backgroundColor: Colors.white,
        title: const Text("FCI EMPLOYEES CO-OP CREDIT SOCIETY BARODA"),
        centerTitle: true,
        titleTextStyle: const TextStyle(
            fontWeight: FontWeight.bold, color: Colors.black, fontSize: 14),
      ),
      backgroundColor: const Color.fromRGBO(255, 255, 255, 1),
      body: GetBuilder<HomeCtrl>(builder: (hctrl) {
        print("--------------------${hctrl.settings?.applyButton}");
        return Stack(
          children: [
            Center(
              child: Opacity(
                opacity: 0.20,
                child: Container(
                  width: 200,
                  decoration: const BoxDecoration(
                      image: DecorationImage(
                          image: AssetImage(
                              'assets/images/foodcorpimage4_removebg.png'),
                          fit: BoxFit.fitWidth)),
                ),
              ),
            ),
            SingleChildScrollView(
              padding: const EdgeInsets.only(
                  top: 0, right: 25, left: 25, bottom: 25),
              child: Column(
                children: [
                  const Text(
                    "REGISTRATION FORM",
                    // "APPLICATION FOR ADMISSION AS A MEMBER",
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 15),
                  TextFormField(
                      decoration: const InputDecoration(
                          border: OutlineInputBorder(),
                          label: Text('Enter Full Name *'),
                          hintStyle: TextStyle(
                              fontSize: 17, fontWeight: FontWeight.w500)),
                      controller: namectrl),
                  const SizedBox(height: 30),
                  TextFormField(
                    decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        label: Text('Enter Email *'),
                        hintStyle: TextStyle(
                            fontSize: 17, fontWeight: FontWeight.w500)),
                    controller: emailctrl,
                  ),
                  const SizedBox(height: 30),
                  TextFormField(
                    keyboardType: TextInputType.number,
                    decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        label: Text('Enter Mobile Number *'),
                        hintStyle: TextStyle(
                            fontSize: 17, fontWeight: FontWeight.w500)),
                    controller: phonenoctrl,
                  ),
                  const SizedBox(height: 30),
                  TextFormField(
                    keyboardType: TextInputType.number,
                    decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        label: Text('Enter Employee Number *'),
                        hintStyle: TextStyle(
                            fontSize: 17, fontWeight: FontWeight.w500)),
                    controller: empnoctrl,
                  ),
                  const SizedBox(height: 30),
                  TextFormField(
                    keyboardType: TextInputType.number,
                    decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        label: Text('Enter CPF Number *'),
                        hintStyle: TextStyle(
                            fontSize: 17, fontWeight: FontWeight.w500)),
                    controller: cpfnoctrl,
                  ),
                  const SizedBox(height: 30),
                  DropdownButtonHideUnderline(
                      child: DropdownButtonFormField(
                    focusColor: Colors.transparent,
                    dropdownColor: Colors.white,
                    decoration: const InputDecoration(
                        hintText: "Select District Office *",
                        hintStyle: TextStyle(
                            fontSize: 19, fontWeight: FontWeight.bold),
                        constraints: BoxConstraints(maxWidth: 450),
                        border: OutlineInputBorder()),
                    value: selectedoffice,
                    items: [
                      ...List.generate(
                        hctrl.districtoffice.length,
                        (index) {
                          return DropdownMenuItem(
                              value: hctrl.districtoffice[index].docId,
                              child: Text(hctrl.districtoffice[index].name));
                        },
                      )
                    ],
                    onChanged: (value) {
                      setState(() {
                        selectedoffice = value;
                      });
                    },
                  )),
                  const SizedBox(height: 30),
                  TextFormField(
                    decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        label: Text('Enter Current Address *'),
                        hintStyle: TextStyle(
                            fontSize: 17, fontWeight: FontWeight.w500)),
                    controller: addresssctrl,
                  ),
                  const SizedBox(height: 30),
                  TextFormField(
                    decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        label: Text('Enter Permanent Address *'),
                        hintStyle: TextStyle(
                            fontSize: 17, fontWeight: FontWeight.w500)),
                    controller: paddressctrl,
                  ),

                  const Padding(
                    padding: EdgeInsets.all(25),
                    child: Text(
                      "NOMINEE DETAILS",
                      style: TextStyle(
                        fontSize: 17,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  // const SizedBox(height: 30),
                  TextFormField(
                    decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        label: Text('Nominee Name *'),
                        hintStyle: TextStyle(
                            fontSize: 17, fontWeight: FontWeight.w500)),
                    controller: nomineeNamectrl,
                  ),
                  const SizedBox(height: 30),

                  TextFormField(
                    decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        label: Text('Nominee Relation *'),
                        hintStyle: TextStyle(
                            fontSize: 17, fontWeight: FontWeight.w500)),
                    controller: nomineeRelationctrl,
                  ),
                  // const SizedBox(height: 30),
                  hctrl.settings?.applyButton == true
                      ? const Padding(
                          padding: EdgeInsets.all(25),
                          child: Text(
                            "BANK DETAILS",
                            style: TextStyle(
                              fontSize: 17,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        )
                      : SizedBox.shrink(),

                  hctrl.settings?.applyButton == true
                      ? TextFormField(
                          decoration: const InputDecoration(
                              border: OutlineInputBorder(),
                              label: Text('Bank Account Name'),
                              hintStyle: TextStyle(
                                  fontSize: 17, fontWeight: FontWeight.w500)),
                          controller: bankacnamectrl,
                        )
                      : SizedBox.shrink(),
                  hctrl.settings?.applyButton == true
                      ? SizedBox(height: 30)
                      : SizedBox.shrink(),
                  hctrl.settings?.applyButton == true
                      ? TextFormField(
                          decoration: const InputDecoration(
                              border: OutlineInputBorder(),
                              label: Text('Bank Name'),
                              hintStyle: TextStyle(
                                  fontSize: 17, fontWeight: FontWeight.w500)),
                          controller: banknamectrl,
                        )
                      : SizedBox.shrink(),
                  hctrl.settings?.applyButton == true
                      ? SizedBox(height: 30)
                      : SizedBox.shrink(),
                  hctrl.settings?.applyButton == true
                      ? TextFormField(
                          keyboardType: TextInputType.number,
                          decoration: const InputDecoration(
                              border: OutlineInputBorder(),
                              label: Text('Bank Account No.'),
                              hintStyle: TextStyle(
                                  fontSize: 17, fontWeight: FontWeight.w500)),
                          controller: bankacnoctrl,
                        )
                      : SizedBox.shrink(),
                  hctrl.settings?.applyButton == true
                      ? SizedBox(height: 30)
                      : SizedBox.shrink(),
                  hctrl.settings?.applyButton == true
                      ? TextFormField(
                          decoration: const InputDecoration(
                              border: OutlineInputBorder(),
                              label: Text('Bank IFSC'),
                              hintStyle: TextStyle(
                                  fontSize: 17, fontWeight: FontWeight.w500)),
                          controller: ifsccodectrl,
                        )
                      : SizedBox.shrink(),
                  const SizedBox(height: 20),

                  if (pdfDoc != null)
                    Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(pdfDoc!,
                                style: const TextStyle(
                                    fontSize: 16,
                                    overflow: TextOverflow.ellipsis)),
                          ),
                          IconButton(
                            icon: const Icon(Icons.cancel, color: Colors.red),
                            onPressed: removeFile, // Remove the selected file
                          )
                        ]),
                  if (pdfDoc == null)
                    ElevatedButton(
                      style: ElevatedButton.styleFrom(
                          padding: EdgeInsets.all(10),
                          fixedSize:
                              Size.fromWidth(MediaQuery.sizeOf(context).width),
                          backgroundColor: Colors.green.shade300,
                          elevation: 0,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                              // side: const BorderSide(color: Colors.black),
                              borderRadius: BorderRadius.circular(4))),
                      onPressed: () async {
                        await pickFiles();

                        if (pdfDoc != null) {
                          String urls = await uploadFile();
                          // print('Uploaded file URLs: $urls');
                        } else {
                          // print("No PDF file selected.");
                        }
                      },
                      child: const Text(
                        textAlign: TextAlign.center,
                        "UPLOAD PDF",
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 15,
                        ),
                      ),
                    ),
                  SizedBox(height: 30),
                  Text.rich(
                    TextSpan(
                      children: [
                        TextSpan(
                          text: "NOTE:\n",
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        WidgetSpan(
                          child: Padding(
                            padding: const EdgeInsets.only(left: 8.0),
                            child: Text(
                                "• Pdf size should not be more than 3 MB.   \n"),
                          ),
                        ),
                        TextSpan(
                          text: "DOCUMENTS:\n",
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        WidgetSpan(
                          child: Padding(
                              padding: const EdgeInsets.only(left: 8.0),
                              child: hctrl.settings?.applyButton == true
                                  ? Text(
                                      "• Aadhar Card\n• Office Identity Card\n• Bank Account Details with IFSC\n• Latest Salary Slip\n")
                                  : SizedBox.shrink()),
                        ),
                        WidgetSpan(
                          child: Padding(
                            padding: const EdgeInsets.only(left: 8.0),
                            child: Text(
                                "• The application form costs ₹11 (₹10 for the share value and ₹1 as form fee).\n"),
                          ),
                        ),
                      ],
                    ),
                  ),

                  Padding(
                    padding:
                        const EdgeInsets.symmetric(vertical: 10, horizontal: 8),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        Checkbox(
                          value: isChecked,
                          onChanged: (bool? value) {
                            setState(() {
                              isChecked = value;
                            });
                          },
                        ),
                        Flexible(
                          child: Text(
                            softWrap: true,
                            "Please check the box to acknowledge.",
                            style: TextStyle(
                                fontSize: 16.sp, fontWeight: FontWeight.normal),
                          ),
                        )
                      ],
                    ),
                  ),
                  isChecked == true
                      ? ElevatedButton(
                          style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green.shade300,
                              elevation: 0,
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(4))),
                          onPressed: () => context.push(Routes.paymentpage),
                          child: Text("Pay Now"))
                      : SizedBox(),

                  isChecked == true ? SizedBox(height: 20) : SizedBox(),

                  rloading
                      ? const CircularProgressIndicator(
                          color: Colors.black,
                        )
                      : ElevatedButton(
                          style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green.shade300,
                              elevation: 0,
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(4))),
                          onPressed: () async {
                            if (namectrl.text.isEmpty ||
                                emailctrl.text.isEmpty ||
                                empnoctrl.text.isEmpty ||
                                addresssctrl.text.isEmpty ||
                                paddressctrl.text.isEmpty ||
                                cpfnoctrl.text.isEmpty ||
                                phonenoctrl.text.isEmpty ||
                                // bankacnamectrl.text.isEmpty ||
                                // banknamectrl.text.isEmpty ||
                                // ifsccodectrl.text.isEmpty ||
                                // bankacnoctrl.text.isEmpty ||
                                nomineeNamectrl.text.isEmpty ||
                                nomineeRelationctrl.text.isEmpty ||
                                selectedoffice == null ||
                                pdfDoc == null) {
                              return showAppSnackBar(
                                  "PLEASE FILL ALL THE REQUIRED DETAILS");
                            }
                            int countCpf = (await FBFireStore.users
                                        .where('cpfNo',
                                            isEqualTo:
                                                num.tryParse(cpfnoctrl.text))
                                        .count()
                                        .get())
                                    .count ??
                                0;
                            int countEmp = (await FBFireStore.users
                                        .where('employeeNo',
                                            isEqualTo:
                                                num.tryParse(empnoctrl.text))
                                        .count()
                                        .get())
                                    .count ??
                                0;
                            if (countEmp > 0 || countCpf > 0) {
                              return countEmp > 0
                                  ? showAppSnackBar("Employee Number Exists!!")
                                  : showAppSnackBar("CpfNo Number Exists!!");
                            }

                            if (isChecked == null || isChecked == false) {
                              return showAppSnackBar(
                                  "Please check the box to acknowledge");
                            }

                            if (phonenoctrl.text.length != 10) {
                              return showAppSnackBar("Invalid Number");
                            }

                            if (pdfDoc == null) {
                              return showAppSnackBar(
                                  "PLEASE PROVIDE ALL THE REQUIRED DOCUMENTS");
                            }
                            rloading = true;
                            setState(() {});
                            String documentUrls = await uploadFile();
                            try {
                              await FBFireStore.newuserapplication.add({
                                'createdAt': DateTime.now(),
                                'name': namectrl.text.trim().toUpperCase(),
                                'cpfNo': num.tryParse(cpfnoctrl.text.trim()),
                                'districtoffice': selectedoffice,
                                'email': emailctrl.text.toLowerCase().trim(),
                                'phonenumber':
                                    phonenoctrl.text.trim().toString(),
                                'registrationDate': null,
                                'employeeNo':
                                    num.tryParse(empnoctrl.text.trim()),
                                'currentAddress': addresssctrl.text.trim(),
                                'permanentAddress': paddressctrl.text.trim(),
                                'documents': documentUrls,
                                'rejectionDate': null,
                                'rejectionReason': null,
                                'approved': false,
                                'isNew': true,
                                'bankAcName': bankacnamectrl.text,
                                'bankName': banknamectrl.text,
                                'ifscCode': ifsccodectrl.text,
                                'bankAcNo': num.tryParse(bankacnoctrl.text),
                                'archived': false,
                                'momento': null,
                                'nomineeName': nomineeNamectrl.text.trim(),
                                'nomineeRelation':
                                    nomineeRelationctrl.text.trim(),
                              });

                              rloading = false;

                              context.pop(Routes.login);

                              setState(() {
                                namectrl.clear();
                                emailctrl.clear();
                                empnoctrl.clear();
                                cpfnoctrl.clear();
                                selectedoffice = null;
                                addresssctrl.clear();
                                paddressctrl.clear();
                                documentUrls == null;
                                phonenoctrl.clear();
                              });

                              showAppSnackBar("Application Successfully Sent");
                            } catch (e) {
                              debugPrint(e.toString());
                            }
                          },
                          child: const Text(
                            "Submit Application",
                            style: TextStyle(fontSize: 16),
                          ),
                        ),
                ],
              ),
            ),
          ],
        );
      }),
    );
  }

  Future<dynamic> iconsdialog(BuildContext context, Function() cameraonPressed,
      Function() fileonPressed) {
    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text("Upload File From"),
        content: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            IconButton(
                onPressed: cameraonPressed,
                icon: const Icon(Icons.camera_alt_outlined)),
            IconButton(
                onPressed: fileonPressed, icon: const Icon(Icons.attach_file))
          ],
        ),
      ),
    );
  }
}




 // CustomListTile(
                  //     title: "Aadhar Card",
                  //     subtitle: Column(
                  //       children: [
                  //         aadharName.isEmpty
                  //             ? const SizedBox()
                  //             : Text(aadharName,
                  //                 overflow: TextOverflow.ellipsis)
                  //       ],
                  //     ),
                  //     onPressed: () => iconsdialog(context, () {
                  //           print(
                  //               "....................................................$aadharName");
                  //           getImageFromCamera('aadharName');
                  //           context.pop();
                  //         }, () async {
                  //           FilePickerResult? result = await FilePicker.platform
                  //               .pickFiles(
                  //                   withData: true,
                  //                   onFileLoading: (p0) =>
                  //                       const CircularProgressIndicator(),
                  //                   type: FileType.custom,
                  //                   allowMultiple: false,
                  //                   allowedExtensions: ['jpg', 'pdf', 'doc']);

                  //           if (result != null) {
                  //             List<File> files = result.paths
                  //                 .map((path) => File(path ?? ""))
                  //                 .toList();
                  //             aadharCard = files[0];
                  //             aadharName = files[0].path.split("/").last;

                  //             context.pop();
                  //           } else {}
                  //           setState(() {});
                  //         })),
                  // CustomListTile(
                  //     subtitle: Column(
                  //       children: [
                  //         salarySlipName.isEmpty
                  //             ? const SizedBox()
                  //             : Text(salarySlipName,
                  //                 overflow: TextOverflow.ellipsis)
                  //       ],
                  //     ),
                  //     title: "Latest Salary Slip",
                  //     onPressed: () => iconsdialog(context, () {
                  //           getImageFromCamera('salarySlipName');
                  //           context.pop();
                  //         }, () async {
                  //           FilePickerResult? result = await FilePicker.platform
                  //               .pickFiles(
                  //                   type: FileType.custom,
                  //                   onFileLoading: (p0) =>
                  //                       const CircularProgressIndicator(),
                  //                   withData: true,
                  //                   allowMultiple: false,
                  //                   allowedExtensions: ['jpg', 'pdf', 'doc']);

                  //           if (result != null) {
                  //             List<File> files = result.paths
                  //                 .map((path) => File(path ?? ""))
                  //                 .toList();
                  //             setState(() {
                  //               ltSalarySLip = files[0];
                  //               salarySlipName = files[0].path.split("/").last;
                  //             });
                  //             docsList = docsList + files;
                  //             context.pop();
                  //           } else {}
                  //           if (salarySlipName.isEmpty) {
                  //             docsList.add(File(salarySlipName));
                  //           }
                  //           setState(() {});
                  //         })),
                  // CustomListTile(
                  //     subtitle: Column(
                  //       children: [
                  //         officeidentityName.isEmpty
                  //             ? const SizedBox()
                  //             : Text(officeidentityName,
                  //                 overflow: TextOverflow.ellipsis)
                  //       ],
                  //     ),
                  //     title: "Office Identity Card",
                  //     onPressed: () => iconsdialog(context, () {
                  //           getImageFromCamera('officeidentityName');
                  //           context.pop();
                  //         }, () async {
                  //           FilePickerResult? result = await FilePicker.platform
                  //               .pickFiles(
                  //                   type: FileType.custom,
                  //                   onFileLoading: (p0) =>
                  //                       const CircularProgressIndicator(),
                  //                   withData: true,
                  //                   allowMultiple: false,
                  //                   allowedExtensions: ['jpg', 'pdf', 'doc']);

                  //           if (result != null) {
                  //             List<File> files = result.paths
                  //                 .map((path) => File(path ?? ""))
                  //                 .toList();
                  //             setState(() {
                  //               officeCard = files[0];
                  //               officeidentityName =
                  //                   files[0].path.split("/").last;
                  //             });
                  //             docsList = docsList + files;
                  //             context.pop();
                  //           } else {}
                  //           if (officeidentityName.isEmpty) {
                  //             docsList.add(File(officeidentityName));
                  //           }
                  //           setState(() {});
                  //         })),
                  // CustomListTile(
                  //     subtitle: Column(
                  //       children: [
                  //         bankacName.isEmpty
                  //             ? const SizedBox()
                  //             : Text(bankacName,
                  //                 overflow: TextOverflow.ellipsis)
                  //       ],
                  //     ),
                  //     title: "Bank Account Details with IFSC",
                  //     onPressed: () => iconsdialog(context, () {
                  //           getImageFromCamera('bankacName');
                  //           context.pop();
                  //         }, () async {
                  //           FilePickerResult? result = await FilePicker.platform
                  //               .pickFiles(
                  //                   type: FileType.custom,
                  //                   onFileLoading: (p0) =>
                  //                       const CircularProgressIndicator(),
                  //                   withData: true,
                  //                   allowMultiple: false,
                  //                   allowedExtensions: ['jpg', 'pdf', 'doc']);

                  //           if (result != null) {
                  //             List<File> files = result.paths
                  //                 .map((path) => File(path ?? ""))
                  //                 .toList();
                  //             setState(() {
                  //               bankAccountDetails = files[0];
                  //               bankacName = files[0].path.split("/").last;
                  //             });
                  //             docsList = docsList + files;
                  //             context.pop();
                  //           } else {}
                  //           if (officeidentityName.isEmpty) {
                  //             docsList.add(File(officeidentityName));
                  //           }
                  //           setState(() {});
                  //         })),

                  // const SizedBox(height: 20),