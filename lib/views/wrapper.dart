import 'package:flutter/material.dart';
import 'package:foodcorp/controller/homectrl.dart';
import 'package:foodcorp/views/dashboard/dashboard_page.dart';
import 'package:foodcorp/views/login/login_page.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import '../shared/methods.dart';
import '../shared/router.dart';

class Wrapper extends StatefulWidget {
  const Wrapper({super.key});

  @override
  State<Wrapper> createState() => _WrapperState();
}

class _WrapperState extends State<Wrapper> {
  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(
      builder: (controller) {
        return isLoggedIn()
            ? controller.dataloaded
                ? controller.users == null
                    ? const UserNotfound()
                    : const DashboardPage()
                : const CircularProgressIndicator()
            : const Loginpage();
      },
    );
  }
}

class UserNotfound extends StatelessWidget {
  const UserNotfound({super.key});

  void _showUserNotFoundDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('User Not Found'),
          // content:
          //     const Text('The user you are looking for could not be found.'),
          actions: <Widget>[
            TextButton(
              child: const Text('Back to Login'),
              onPressed: () {
                Navigator.pop(context);
                context.pop(Routes.login);
              },
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('User Not Found Example'),
      ),
      body: Center(
        child: ElevatedButton(
          onPressed: () => _showUserNotFoundDialog(context),
          child: const Text('Show User Not Found Dialog'),
        ),
      ),
    );
  }
}

class UpdateVersionPage extends StatefulWidget {
  const UpdateVersionPage({super.key});

  @override
  State<UpdateVersionPage> createState() => _UpdateVersionPageState();
}

class _UpdateVersionPageState extends State<UpdateVersionPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Text("UPDATE APP",
            //     style: TextStyle(fontWeight: FontWeight.w600, fontSize: 30)),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 50),
              child: SizedBox(
                  height: 200,
                  child: Image.asset("assets/images/version_upgrade.png")),
            ),
            // SizedBox(height: 30),
            Text(
              style: TextStyle(fontSize: 16),
              softWrap: true,
              textAlign: TextAlign.center,
              "Please update the app to continue.",
            ),
          ],
        ),
      ),
    );
  }
}
