// ignore_for_file: avoid_print

import 'dart:io';
import 'dart:typed_data';
import 'dart:ui';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:file_picker/file_picker.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:foodcorp/controller/homectrl.dart';
import 'package:foodcorp/shared/const.dart';
import 'package:foodcorp/shared/firebase.dart';
import 'package:foodcorp/shared/methods.dart';
import 'package:foodcorp/shared/router.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:signature/signature.dart';

class Ltloanform extends StatefulWidget {
  const Ltloanform({super.key});

  @override
  State<Ltloanform> createState() => _LtloanformState();
}

class _LtloanformState extends State<Ltloanform> {
  bool checkboxValue1 = false;

  TextEditingController appnoctrl = TextEditingController();
  TextEditingController fullnamectrl = TextEditingController();
  TextEditingController desigctrl = TextEditingController();
  TextEditingController bacnoctrl = TextEditingController();
  TextEditingController addressctrl = TextEditingController();
  TextEditingController appliedamtctrl = TextEditingController();
  TextEditingController appliedamtwordsctrl = TextEditingController();
  TextEditingController loanreasonctrl = TextEditingController();
  TextEditingController surityname1ctrl = TextEditingController();
  TextEditingController surityname2ctrl = TextEditingController();
  TextEditingController acno1ctrl = TextEditingController();
  TextEditingController acno2ctrl = TextEditingController();

  Uint8List? borrowersignatureImage;

  String? ltLoanPdfDoc;

  final picker = ImagePicker();
  UploadTask? uploadTask;

  SignatureController bsignctrl = SignatureController(
    penStrokeWidth: 3,
    penColor: Colors.blue,
  );

  Uint8List? surity1signatureImage;

  SignatureController s1sctrl = SignatureController(
    penStrokeWidth: 3,
    penColor: Colors.blue,
  );

  Uint8List? surity2signatureImage;

  SignatureController s2sctrl = SignatureController(
    penStrokeWidth: 3,
    penColor: Colors.blue,
  );

  bool ltOnSubmitLoad = false;

  @override
  void initState() {
    super.initState();
    loadData(Get.find<HomeCtrl>());
  }

  void removeLtPDF() {
    setState(() {
      ltLoanPdfDoc = null;
    });
  }

  loadData(HomeCtrl ctrl) async {
    fullnamectrl.text = ctrl.users?.name ?? "";
  }

  Future<String?> uploadSignimages(Uint8List file) async {
    try {
      final path = "Images/${DateTime.now().millisecondsSinceEpoch}.png";
      final imageRef = FBStorage.fbstore.ref().child(path);

      final task = await imageRef.putData(file);
      var downloadurl = await task.ref.getDownloadURL();

      return await task.ref.getDownloadURL();
    } catch (e) {}
    return null;
  }

  Future<void> pickFiles() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowMultiple: false,
      allowedExtensions: ['pdf'],
    );

    if (result != null && result.files.isNotEmpty) {
      String filePath = result.files.single.path!; // Keep full file path

      File file = File(filePath);

      int fileSizeInBytes = await file.length();
      int maxSizeInBytes = 3 * 1024 * 1024; // 3MB

      if (fileSizeInBytes <= maxSizeInBytes) {
        setState(() {
          ltLoanPdfDoc = filePath;
        });
        // print("File selected: $ltLoanPdfDoc");
        showAppSnackBar("File Selected");
      } else {
        // print("File is too large. Please select a file smaller than 3MB.");
        showAppSnackBar(
            "File SelectedFile is too large. Please select a file smaller than 3MB.");
      }
    } else {
      // print("No file selected.");
    }
  }

  Future<String> uploadFile() async {
    String docUrl = "";

    try {
      if (ltLoanPdfDoc != null) {
        File file = File(ltLoanPdfDoc!);

        // print("File path: $ltLoanPdfDoc");

        if (!await file.exists()) {
          // print("The file does not exist at the path: $ltLoanPdfDoc");
          return docUrl;
        }

        final fileName = file.path.split("/").last;
        final path = 'files/$fileName';
        final ref = FirebaseStorage.instance.ref().child(path);
        uploadTask = ref.putFile(file);

        final snapshot = await uploadTask!.whenComplete(() => {});
        final urlDownload = await snapshot.ref.getDownloadURL();
        // print('File uploaded: $urlDownload');

        docUrl = urlDownload;
      }

      return docUrl;
    } catch (e) {
      debugPrint("Error uploading file: ${e.toString()}");
      return docUrl;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GetBuilder<HomeCtrl>(builder: (ctrl) {
        loadData(ctrl);
        return SingleChildScrollView(
          padding:
              const EdgeInsets.only(top: 20, right: 15, left: 15, bottom: 50),
          child: Column(
            children: [
              // ltFormHeader(),
              // const SizedBox(height: 15),
              Text(
                style: TextStyle(
                  fontSize: 12.9.sp,
                  fontWeight: FontWeight.w500,
                ),
                "FOOD CORPORATION OF INDIA EMPLOYEES' CO-OPERATIVE",
              ),
              Text(
                style: TextStyle(
                  fontSize: 13.sp,
                  fontWeight: FontWeight.w500,
                ),
                " CREDIT SOCIETY LTD. BARODA.",
              ),
              Text(
                style: TextStyle(
                  fontSize: 13.sp,
                ),
                "Alembic Road, Baroda - 390-003.",
              ),
              const SizedBox(height: 15),
              Text(
                style: TextStyle(
                  fontSize: 20.sp,
                  fontWeight: FontWeight.bold,
                ),
                "APPLICATION FOR LONG TERM LOAN",
              ),
              const SizedBox(height: 15),
              CustomSetTextfield2(
                text: 'Full Name',
                controller: fullnamectrl,
                enabled: false,
              ),
              CustomSetTextfield2(
                enabled: true,
                text: 'Designation',
                controller: desigctrl,
              ),
              CustomSetTextfield2(
                  keyboardType: TextInputType.number,
                  enabled: true,
                  text: 'Account No.',
                  controller: bacnoctrl),
              // CustomSetTextfield2(
              //     enabled: true, text: 'Full Address', controller: addressctrl),
              CustomSetTextfield2(
                  keyboardType: TextInputType.number,
                  enabled: true,
                  text: 'Loan Applied in Rs.',
                  controller: appliedamtctrl),
              CustomSetTextfield2(
                  enabled: true,
                  text: 'Loan Amount in Words',
                  controller: appliedamtwordsctrl),
              CustomSetTextfield2(
                  enabled: true,
                  text: 'Reason for Loan',
                  controller: loanreasonctrl),
              const SizedBox(height: 15),
              const Divider(
                color: Colors.black,
                thickness: 2,
              ),
              const SizedBox(height: 15),
              const Text(
                "We the undersigned hereby agreed and undertake to stamd surities to",
                style: TextStyle(fontSize: 13, fontWeight: FontWeight.w500),
              ),
              const SizedBox(height: 15),
              CustomSetTextfield2(
                  enabled: false,
                  text: 'Shri/Smt. (BORROWER)',
                  controller: fullnamectrl),
              const Text(" ( 1. ) "),
              const SizedBox(height: 15),
              CustomSetTextfield2(
                  enabled: true,
                  text: "Name of the first surety person",
                  controller: surityname1ctrl),
              CustomSetTextfield2(
                  keyboardType: TextInputType.number,
                  enabled: true,
                  text: "Account No. of the first surety person",
                  controller: acno1ctrl),
              // surity1SignRow(context),
              const Text(" ( 2. ) "),
              const SizedBox(height: 15),
              CustomSetTextfield2(
                  enabled: true,
                  text: "Name of the second surety person",
                  controller: surityname2ctrl),
              CustomSetTextfield2(
                  enabled: true,
                  keyboardType: TextInputType.number,
                  text: "Account No. of the second surety person",
                  controller: acno2ctrl),
              const SizedBox(height: 10),
              borrowerSignRow(context),
              const SizedBox(height: 20),

              Text.rich(
                TextSpan(
                  children: [
                    TextSpan(
                      text: "DOCUMENTS REQUIRED:\n\n",
                      style:
                          TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                    ),
                    WidgetSpan(
                      alignment: PlaceholderAlignment.baseline,
                      baseline: TextBaseline.alphabetic,
                      child: Padding(
                        padding: EdgeInsets.only(left: 12.0, bottom: 4.0),
                        child: Text("• Cancelled Cheque or Passbook\n"),
                      ),
                    ),
                    WidgetSpan(
                      alignment: PlaceholderAlignment.baseline,
                      baseline: TextBaseline.alphabetic,
                      child: Padding(
                        padding: EdgeInsets.only(left: 12.0, bottom: 4.0),
                        child: Text("• Loan Form\n"),
                      ),
                    ),
                    WidgetSpan(
                      alignment: PlaceholderAlignment.baseline,
                      baseline: TextBaseline.alphabetic,
                      child: Padding(
                        padding: EdgeInsets.only(left: 12.0, bottom: 4.0),
                        child: Text("• Stamp\n"),
                      ),
                    ),
                    WidgetSpan(
                      alignment: PlaceholderAlignment.baseline,
                      baseline: TextBaseline.alphabetic,
                      child: Padding(
                        padding: EdgeInsets.only(left: 12.0),
                        child: Text("• PDF size should not exceed 3 MB\n"),
                      ),
                    ),
                  ],
                ),
              ),

              Row(
                children: [
                  (ltLoanPdfDoc == null)
                      ? ElevatedButton(
                          style: const ButtonStyle(
                            shape: WidgetStatePropertyAll(
                                ContinuousRectangleBorder(
                                    borderRadius:
                                        BorderRadius.all(Radius.circular(10)))),
                            backgroundColor:
                                WidgetStatePropertyAll(Colors.black),
                            elevation: WidgetStatePropertyAll(0),
                          ),
                          onPressed: () async {
                            await pickFiles();

                            if (ltLoanPdfDoc != null) {
                              String urls = await uploadFile();
                              // print('Uploaded file URLs: $urls');
                            } else {
                              // print("No PDF file selected.");
                            }
                          },
                          child: const Text(
                            "Upload PDF",
                            style: TextStyle(color: Colors.white),
                          ),
                        )
                      : Expanded(
                          child: Text(
                            "$ltLoanPdfDoc",
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                  (ltLoanPdfDoc != null)
                      ? IconButton(
                          icon: const Icon(Icons.cancel, color: Colors.red),
                          onPressed: removeLtPDF, // Remove the selected file
                        )
                      : SizedBox.shrink()
                ],
              ),
              const SizedBox(height: 10),

              // surity2SignRow(context),
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 0, vertical: 20),
                child: Row(
                  children: [
                    Checkbox(
                      value: checkboxValue1,
                      onChanged: (value) {
                        setState(() {
                          checkboxValue1 = value!;
                        });
                      },
                    ),
                    Flexible(
                      child: RichText(
                        text: TextSpan(
                          style: const TextStyle(
                            fontSize: 15,
                            color: Colors.black,
                          ),
                          children: [
                            const TextSpan(
                              text: "I have read and agree to the ",
                            ),
                            TextSpan(
                              text: "Terms and Conditions",
                              style: const TextStyle(
                                color: Colors.blue,
                                decoration: TextDecoration.underline,
                              ),
                              recognizer: TapGestureRecognizer()
                                ..onTap = () {
                                  context.push(Routes.ltloantc);
                                },
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              ltOnSubmitLoad
                  ? const CircularProgressIndicator()
                  : ElevatedButton(
                      style: const ButtonStyle(
                        padding: WidgetStatePropertyAll(
                            EdgeInsetsDirectional.symmetric(
                                horizontal: 100, vertical: 15)),
                        shape: WidgetStatePropertyAll(ContinuousRectangleBorder(
                            borderRadius:
                                BorderRadius.all(Radius.circular(10)))),
                        backgroundColor: WidgetStatePropertyAll(Colors.green),
                        elevation: WidgetStatePropertyAll(0),
                      ),
                      onPressed: () async {
                        if (fullnamectrl.text.isEmpty ||
                            desigctrl.text.isEmpty ||
                            bacnoctrl.text.isEmpty ||
                            appliedamtctrl.text.isEmpty ||
                            appliedamtwordsctrl.text.isEmpty ||
                            loanreasonctrl.text.isEmpty ||
                            surityname1ctrl.text.isEmpty ||
                            surityname2ctrl.text.isEmpty ||
                            acno1ctrl.text.isEmpty ||
                            acno2ctrl.text.isEmpty ||
                            borrowersignatureImage == null) {
                          showAppSnackBar(
                              duration: const Duration(milliseconds: 2000),
                              "Please fill all the details");
                          return;
                        }

                        if (ltLoanPdfDoc == null) {
                          showAppSnackBar(
                              duration: const Duration(milliseconds: 2000),
                              "Please upload Pdf");
                          return;
                        }

                        final appliedAmt =
                            num.tryParse(appliedamtctrl.text.trim()) ?? 0;
                        final maxAmt = num.tryParse(
                                (await FBFireStore.settings.get())
                                        .data()?['maxLtLoanAmt']
                                        ?.toString() ??
                                    '0') ??
                            0;

                        // print("AppliedAmt: $appliedAmt, MaxAmt: $maxAmt");

                        if (appliedAmt > maxAmt) {
                          showAppSnackBar(
                              duration: const Duration(milliseconds: 2000),
                              "Applied amount exceeds maximum limit");
                          setState(() {
                            ltOnSubmitLoad = false;
                          });
                          return;
                        }

                        if (!checkboxValue1) {
                          showAppSnackBar(
                              "Please Accept the Terms and Conditions");
                          setState(() {
                            ltOnSubmitLoad = false;
                          });
                          return;
                        } else {
                          final ltloans = ctrl.activeLoans
                              .where((element) =>
                                  element.loanType == LoanTypes.longTerm)
                              .where(
                                (element) => element.isNew == true,
                              );

                          if (ltloans.isNotEmpty) {
                            return showAppSnackBar("Loan already exist.");
                          }
                          setState(() {
                            ltOnSubmitLoad = true;
                          });
                          try {
                            final borrowImageUrl =
                                await uploadSignimages(borrowersignatureImage!);
                            await FBFireStore.loan.add({
                              'uid': ctrl.users?.docId,
                              'applicationNo': Get.find<HomeCtrl>()
                                      .settings
                                      ?.applicationNo ??
                                  0 + 1,
                              // FBAuth.auth.currentUser?.uid,
                              'createdAt': Timestamp.now(),
                              // 'name': fullnamectrl.text,
                              'totalLoanAmt': 0,
                              'appliedLoanAmt':
                                  num.tryParse(appliedamtctrl.text.trim()) ?? 0,
                              'totalLoanPaid': 0,
                              'totalLoanDue': 0,
                              'loanType': LoanTypes.longTerm,
                              'isSettled': false,
                              'settledOn': null, // Nullable
                              'isNew': true,
                              'appliedOn': Timestamp.now(),
                              // 'applicationNo':
                              //     num.tryParse(appnoctrl.text) ?? 0,
                              'approvedOn': null, // Nullable
                              'processedOn': null, // Nullable
                              'share': 0,
                              'totalInterestPaid': 0,
                              'designation': desigctrl.text.trim(),
                              'bAcNo': num.tryParse(bacnoctrl.text.trim()) ?? 0,
                              'appliedLoanAmtinWords':
                                  appliedamtwordsctrl.text.trim(),
                              'loanReason': loanreasonctrl.text.trim(),
                              'bSign': borrowImageUrl,
                              'surityName1': surityname1ctrl.text,
                              'surityName2': surityname2ctrl.text,
                              'sAcNo1': num.tryParse(acno1ctrl.text.trim()),
                              'sAcNo2': num.tryParse(acno2ctrl.text.trim()),
                              // 'suritySign1': surity1ImageUrl,
                              // 'suritySign2': surity2ImageUrl,
                              // 'address': addressctrl.text,
                              'balance': null,
                              'rejectionDate': null,
                              'rejectionReason': null,
                              'monthlyInstallmentAmt': num.tryParse(
                                  ctrl.settings?.defaultLtinstallAmt ?? "0"),
                              'pdfString': ltLoanPdfDoc,
                            });

                            await FBFireStore.settings.update(
                                {'applicationNo': FieldValue.increment(1)});

                            context.go(Routes.dashboard);

                            appnoctrl.clear();
                            fullnamectrl.clear();
                            desigctrl.clear();
                            bacnoctrl.clear();
                            addressctrl.clear();
                            appliedamtctrl.clear();
                            appliedamtwordsctrl.clear();
                            loanreasonctrl.clear();
                            surityname1ctrl.clear();
                            surityname2ctrl.clear();
                            acno1ctrl.clear();
                            acno2ctrl.clear();
                            bsignctrl.clear();
                            // s1sctrl.clear();
                            // s2sctrl.clear();
                            borrowersignatureImage = null;
                            // surity1signatureImage = null;
                            // surity2signatureImage = null;

                            setState(() {
                              ltOnSubmitLoad = false;
                            });
                            showAppSnackBar(
                                duration: const Duration(milliseconds: 2000),
                                "Loan Applied Successfully");
                          } catch (e) {
                            debugPrint(e.toString());
                            setState(() {
                              ltOnSubmitLoad = false;
                            });
                          }
                          // }
                        }
                      },
                      child: const Text(
                        "APPLY",
                        style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            letterSpacing: 3,
                            fontWeight: FontWeight.bold),
                      ),
                    ),
            ],
          ),
        );
      }),
    );
  }

  // Row ltFormHeader() {
  //   return Row(
  //     // mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //     children: [
  //       // InkWell(
  //       //   hoverColor: Colors.transparent,
  //       //   overlayColor: WidgetStatePropertyAll(Colors.transparent),
  //       //   onTap: () async {
  //       //     // print("image inkwell pressed");
  //       //     context.pop(Routes.dashboard);
  //       //   },
  //       //   child: Image(
  //       //       height: 80,
  //       //       image: AssetImage('assets/images/foodcorpimage4_removebg.png')),
  //       // ),
  //       // Row(
  //       //   children: [
  //       //     const Column(
  //       //       children: [
  //       //         SizedBox(height: 8),
  //       //         Text("Application No."),
  //       //       ],
  //       //     ),
  //       //     const SizedBox(width: 5),
  //       //     SizedBox(
  //       //       width: 100,
  //       //       height: 28,
  //       //       child: TextFormField(
  //       //         style: const TextStyle(fontSize: 15),
  //       //         cursorHeight: 15,
  //       //         keyboardType: TextInputType.number,
  //       //         controller: appnoctrl,
  //       //         decoration: const InputDecoration(border: OutlineInputBorder()),
  //       //       ),
  //       //     ),
  //       //   ],
  //       // )
  //     ],
  //   );
  // }

  // Row surity2SignRow(BuildContext context) {
  //   return Row(
  //     children: [
  //       Align(
  //         alignment: Alignment.centerLeft,
  //         child: ElevatedButton(
  //           style: const ButtonStyle(
  //             shape: WidgetStatePropertyAll(ContinuousRectangleBorder(
  //                 borderRadius: BorderRadius.all(Radius.circular(10)))),
  //             backgroundColor: WidgetStatePropertyAll(Colors.black),
  //             elevation: WidgetStatePropertyAll(0),
  //           ),
  //           onPressed: () => showDialog(
  //             context: context,
  //             builder: (context) => Column(
  //               mainAxisAlignment: MainAxisAlignment.center,
  //               crossAxisAlignment: CrossAxisAlignment.center,
  //               children: [
  //                 Signature(
  //                   height: 400,
  //                   width: 300,
  //                   dynamicPressureSupported: true,
  //                   backgroundColor: Colors.white,
  //                   controller: s2sctrl,
  //                 ),
  //                 const SizedBox(height: 10),
  //                 Row(
  //                   mainAxisAlignment: MainAxisAlignment.center,
  //                   children: [
  //                     ElevatedButton(
  //                       style: const ButtonStyle(
  //                         shape: WidgetStatePropertyAll(
  //                             ContinuousRectangleBorder(
  //                                 borderRadius:
  //                                     BorderRadius.all(Radius.circular(10)))),
  //                         backgroundColor: WidgetStatePropertyAll(Colors.green),
  //                         elevation: WidgetStatePropertyAll(0),
  //                       ),
  //                       onPressed: () async {
  //                         final image = await s2sctrl.toImage();
  //                         final byteData = await image?.toByteData(
  //                             format: ImageByteFormat.png);

  //                         if (byteData != null) {
  //                           setState(() {
  //                             surity2signatureImage =
  //                                 byteData.buffer.asUint8List();
  //                           });
  //                         }
  //                         print("surety2 Signature captured!");
  //                         Navigator.pop(context);
  //                       },
  //                       child: const Text(
  //                         "Submit",
  //                         style: TextStyle(color: Colors.white),
  //                       ),
  //                     ),
  //                     const SizedBox(width: 10),
  //                     ElevatedButton(
  //                       style: const ButtonStyle(
  //                         shape: WidgetStatePropertyAll(
  //                             ContinuousRectangleBorder(
  //                                 borderRadius:
  //                                     BorderRadius.all(Radius.circular(10)))),
  //                         backgroundColor: WidgetStatePropertyAll(Colors.grey),
  //                         elevation: WidgetStatePropertyAll(0),
  //                       ),
  //                       onPressed: () {
  //                         surity2signatureImage = null;
  //                         s2sctrl.clear();
  //                       },
  //                       child: const Text(
  //                         "Clear Signature",
  //                         style: TextStyle(color: Colors.white),
  //                       ),
  //                     ),
  //                   ],
  //                 ),
  //               ],
  //             ),
  //           ),
  //           child: const Text(
  //             "Add Signature",
  //             style: TextStyle(color: Colors.white),
  //           ),
  //         ),
  //       ),
  //       const SizedBox(width: 25),
  //       surity2signatureImage != null
  //           ? SizedBox(
  //               height: 50,
  //               child: Image.memory(surity2signatureImage!),
  //             )
  //           : const SizedBox()
  //     ],
  //   );
  // }

  // Row surity1SignRow(BuildContext context) {
  //   return Row(
  //     children: [
  //       Align(
  //         alignment: Alignment.centerLeft,
  //         child: ElevatedButton(
  //           style: const ButtonStyle(
  //             shape: WidgetStatePropertyAll(ContinuousRectangleBorder(
  //                 borderRadius: BorderRadius.all(Radius.circular(10)))),
  //             backgroundColor: WidgetStatePropertyAll(Colors.black),
  //             elevation: WidgetStatePropertyAll(0),
  //           ),
  //           onPressed: () => showDialog(
  //             context: context,
  //             builder: (context) => Column(
  //               mainAxisAlignment: MainAxisAlignment.center,
  //               crossAxisAlignment: CrossAxisAlignment.center,
  //               children: [
  //                 Signature(
  //                   height: 400,
  //                   width: 300,
  //                   dynamicPressureSupported: true,
  //                   backgroundColor: Colors.white,
  //                   controller: s1sctrl,
  //                 ),
  //                 const SizedBox(height: 10),
  //                 Row(
  //                   mainAxisAlignment: MainAxisAlignment.center,
  //                   children: [
  //                     ElevatedButton(
  //                       style: const ButtonStyle(
  //                         shape: WidgetStatePropertyAll(
  //                             ContinuousRectangleBorder(
  //                                 borderRadius:
  //                                     BorderRadius.all(Radius.circular(10)))),
  //                         backgroundColor: WidgetStatePropertyAll(Colors.green),
  //                         elevation: WidgetStatePropertyAll(0),
  //                       ),
  //                       onPressed: () async {
  //                         final image = await s1sctrl.toImage();
  //                         final byteData = await image?.toByteData(
  //                             format: ImageByteFormat.png);

  //                         if (byteData != null) {
  //                           setState(() {
  //                             surity1signatureImage =
  //                                 byteData.buffer.asUint8List();
  //                           });
  //                         }

  //                         print("Surity1 Signature captured!");
  //                         Navigator.pop(context);
  //                       },
  //                       child: const Text(
  //                         "Submit",
  //                         style: TextStyle(color: Colors.white),
  //                       ),
  //                     ),
  //                     const SizedBox(width: 10),
  //                     ElevatedButton(
  //                       style: const ButtonStyle(
  //                         shape: WidgetStatePropertyAll(
  //                             ContinuousRectangleBorder(
  //                                 borderRadius:
  //                                     BorderRadius.all(Radius.circular(10)))),
  //                         backgroundColor: WidgetStatePropertyAll(Colors.grey),
  //                         elevation: WidgetStatePropertyAll(0),
  //                       ),
  //                       onPressed: () {
  //                         surity1signatureImage = null;
  //                         s1sctrl.clear();
  //                       },
  //                       child: const Text(
  //                         "Clear Signature",
  //                         style: TextStyle(color: Colors.white),
  //                       ),
  //                     ),
  //                   ],
  //                 ),
  //               ],
  //             ),
  //           ),
  //           child: const Text(
  //             "Add Signature",
  //             style: TextStyle(color: Colors.white),
  //           ),
  //         ),
  //       ),
  //       const SizedBox(width: 25),
  //       surity1signatureImage != null
  //           ? SizedBox(
  //               height: 50,
  //               child: Image.memory(surity1signatureImage!),
  //             )
  //           : const SizedBox()
  //     ],
  //   );
  // }

  Row borrowerSignRow(BuildContext context) {
    return Row(
      children: [
        ElevatedButton(
          style: const ButtonStyle(
            shape: WidgetStatePropertyAll(ContinuousRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(10)))),
            backgroundColor: WidgetStatePropertyAll(Colors.black),
            elevation: WidgetStatePropertyAll(0),
          ),
          onPressed: () => showDialog(
            context: context,
            builder: (context) => Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Signature(
                  height: 400,
                  width: 300,
                  dynamicPressureSupported: true,
                  backgroundColor: Colors.white,
                  controller: bsignctrl,
                ),
                const SizedBox(height: 10),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ElevatedButton(
                      style: const ButtonStyle(
                        shape: WidgetStatePropertyAll(ContinuousRectangleBorder(
                            borderRadius:
                                BorderRadius.all(Radius.circular(10)))),
                        backgroundColor: WidgetStatePropertyAll(Colors.green),
                        elevation: WidgetStatePropertyAll(0),
                      ),
                      onPressed: () async {
                        final image = await bsignctrl.toImage();
                        final byteData = await image?.toByteData(
                            format: ImageByteFormat.png);

                        if (byteData != null) {
                          setState(() {
                            borrowersignatureImage =
                                byteData.buffer.asUint8List();
                          });
                        }

                        // print("Signature captured!");
                        Navigator.pop(context);
                      },
                      child: const Text(
                        "Submit",
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                    const SizedBox(width: 10),
                    ElevatedButton(
                      style: const ButtonStyle(
                        shape: WidgetStatePropertyAll(ContinuousRectangleBorder(
                            borderRadius:
                                BorderRadius.all(Radius.circular(10)))),
                        backgroundColor: WidgetStatePropertyAll(Colors.grey),
                        elevation: WidgetStatePropertyAll(0),
                      ),
                      onPressed: () {
                        borrowersignatureImage = null;
                        bsignctrl.clear();
                      },
                      child: const Text(
                        "Clear Signature",
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          child: const Text(
            "Add Borrower's Signature",
            style: TextStyle(color: Colors.white),
          ),
        ),
        const SizedBox(width: 25),
        borrowersignatureImage != null
            ? SizedBox(
                height: 50,
                child: Image.memory(borrowersignatureImage!),
              )
            : const SizedBox()
      ],
    );
  }
}
