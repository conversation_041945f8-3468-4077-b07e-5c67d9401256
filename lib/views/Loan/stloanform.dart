// ignore_for_file: avoid_print

import 'dart:io';
import 'dart:typed_data';
import 'dart:ui';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:file_picker/file_picker.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:signature/signature.dart';
import '../../controller/homectrl.dart';
import '../../shared/const.dart';
import '../../shared/firebase.dart';
import '../../shared/methods.dart';
import '../../shared/router.dart';

class Stloanform extends StatefulWidget {
  const Stloanform({super.key});

  @override
  State<Stloanform> createState() => _StloanformState();
}

class _StloanformState extends State<Stloanform> {
  bool checkboxValue2 = false;

  TextEditingController appnoctrl = TextEditingController();
  TextEditingController fullnamectrl = TextEditingController();
  TextEditingController acnoctrl = TextEditingController();
  TextEditingController numamtctrl = TextEditingController();
  TextEditingController amtctrl = TextEditingController();
  TextEditingController surity1ctrl = TextEditingController();
  TextEditingController surity2ctrl = TextEditingController();
  TextEditingController balctrl = TextEditingController();
  TextEditingController loanReasonctrl = TextEditingController();

  @override
  void initState() {
    super.initState();
    loadData(Get.find<HomeCtrl>());
  }

  void removeStPDF() {
    setState(() {
      stLoanPdfDoc = null;
    });
  }

  loadData(HomeCtrl ctrl) async {
    fullnamectrl.text = ctrl.users?.name ?? "";
  }

  Uint8List? borrowersignatureImage;

  String? stLoanPdfDoc;

  final picker = ImagePicker();
  UploadTask? uploadTask;

  SignatureController bsignctrl = SignatureController(
    penStrokeWidth: 3,
    penColor: Colors.blue,
  );

  Uint8List? surity1signatureImage;

  SignatureController s1sctrl = SignatureController(
    penStrokeWidth: 3,
    penColor: Colors.blue,
  );

  Uint8List? surity2signatureImage;

  SignatureController s2sctrl = SignatureController(
    penStrokeWidth: 3,
    penColor: Colors.blue,
  );

  bool stOnSubmitLoad = false;

  Future<String?> uploadSignimages(Uint8List file) async {
    try {
      final path = "Images/${DateTime.now().millisecondsSinceEpoch}.png";
      final imageRef = FBStorage.fbstore.ref().child(path);

      final task = await imageRef.putData(file);
      var downloadurl = await task.ref.getDownloadURL();

      return await task.ref.getDownloadURL();
    } catch (e) {
      debugPrint(e.toString());
    }
    return null;
  }

  Future<void> pickFiles() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowMultiple: false,
      allowedExtensions: ['pdf'],
    );

    if (result != null && result.files.isNotEmpty) {
      String filePath = result.files.single.path!; // Keep full file path

      File file = File(filePath);

      int fileSizeInBytes = await file.length();
      int maxSizeInBytes = 3 * 1024 * 1024; // 3MB

      if (fileSizeInBytes <= maxSizeInBytes) {
        setState(() {
          stLoanPdfDoc = filePath;
        });
        // print("File selected: $stLoanPdfDoc");
        showAppSnackBar("File Selected");
      } else {
        // print("File is too large. Please select a file smaller than 3MB.");
        showAppSnackBar(
            "File SelectedFile is too large. Please select a file smaller than 3MB.");
      }
    } else {
      // print("No file selected.");
    }
  }

  Future<String> uploadFile() async {
    String docUrl = "";

    try {
      if (stLoanPdfDoc != null) {
        File file = File(stLoanPdfDoc!);

        // print("File path: $stLoanPdfDoc");

        if (!await file.exists()) {
          // print("The file does not exist at the path: $stLoanPdfDoc");
          return docUrl;
        }

        final fileName = file.path.split("/").last;
        final path = 'files/$fileName';
        final ref = FirebaseStorage.instance.ref().child(path);
        uploadTask = ref.putFile(file);

        final snapshot = await uploadTask!.whenComplete(() => {});
        final urlDownload = await snapshot.ref.getDownloadURL();
        // print('File uploaded: $urlDownload');

        docUrl = urlDownload;
      }

      return docUrl;
    } catch (e) {
      debugPrint("Error uploading file: ${e.toString()}");
      return docUrl;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GetBuilder<HomeCtrl>(builder: (ctrl) {
        loadData(ctrl);

        return SingleChildScrollView(
          padding:
              const EdgeInsets.only(top: 20, right: 15, left: 15, bottom: 50),
          child: Column(
            children: [
              // StFormHeader(appnoctrl: appnoctrl),
              // const SizedBox(height: 15),
              Text(
                style: TextStyle(
                  fontSize: 12.9.sp,
                  fontWeight: FontWeight.w500,
                ),
                "FOOD CORPORATION OF INDIA EMPLOYEES' CO-OPERATIVE",
              ),
              Text(
                style: TextStyle(
                  fontSize: 13.sp,
                  fontWeight: FontWeight.w500,
                ),
                " CREDIT SOCIETY LTD. BARODA.",
              ),
              Text(
                style: TextStyle(
                  fontSize: 13.sp,
                ),
                "Alembic Road, Baroda - 390-003.",
              ),
              const SizedBox(height: 15),
              Align(
                  alignment: Alignment.centerRight,
                  child:
                      Text(DateFormat('MMMM dd, yyyy').format(DateTime.now()))),
              const SizedBox(height: 15),
              Text(
                style: TextStyle(
                  fontSize: 20.sp,
                  fontWeight: FontWeight.bold,
                ),
                "APPLICATION FOR EMERGENCY LOAN",
              ),
              const SizedBox(height: 15),
              CustomSetTextfield2(
                  keyboardType: TextInputType.number,
                  enabled: true,
                  text: 'Account No.',
                  controller: acnoctrl),
              // CustomSetTextfield2(
              //     keyboardType: TextInputType.number,
              //     enabled: true,
              //     text: 'Balance',
              //     controller: balctrl),
              CustomSetTextfield2(
                  enabled: false, text: 'Full Name', controller: fullnamectrl),
              CustomSetTextfield2(
                  keyboardType: TextInputType.number,
                  enabled: true,
                  text: 'Loan Applied in Rs.',
                  controller: numamtctrl),
              CustomSetTextfield2(
                  enabled: true,
                  text: 'Loan Amount in Words',
                  controller: amtctrl),
              CustomSetTextfield2(
                  enabled: true,
                  text: 'Loan Reason',
                  controller: loanReasonctrl),
              // const Text(" ( 1. ) "),
              const SizedBox(height: 15),
              CustomSetTextfield2(
                  enabled: true,
                  text: "Name of the first surety person",
                  controller: surity1ctrl),
              // surity1SignRow(context),
              // const Text(" ( 2. ) "),
              const SizedBox(height: 10),
              CustomSetTextfield2(
                  enabled: true,
                  text: "Name of the second surety person",
                  controller: surity2ctrl),
              // surity2SignRow(context),
              const SizedBox(height: 10),
              borrowerSignRow(context),

              const SizedBox(height: 20),
              Text.rich(
                TextSpan(
                  children: [
                    TextSpan(
                      text: "DOCUMENTS REQUIRED:\n\n",
                      style:
                          TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                    ),
                    WidgetSpan(
                      alignment: PlaceholderAlignment.baseline,
                      baseline: TextBaseline.alphabetic,
                      child: Padding(
                        padding: EdgeInsets.only(left: 12.0, bottom: 4.0),
                        child: Text("• Cancelled Cheque or Passbook\n"),
                      ),
                    ),
                    WidgetSpan(
                      alignment: PlaceholderAlignment.baseline,
                      baseline: TextBaseline.alphabetic,
                      child: Padding(
                        padding: EdgeInsets.only(left: 12.0, bottom: 4.0),
                        child: Text("• Loan Form\n"),
                      ),
                    ),
                    WidgetSpan(
                      alignment: PlaceholderAlignment.baseline,
                      baseline: TextBaseline.alphabetic,
                      child: Padding(
                        padding: EdgeInsets.only(left: 12.0, bottom: 4.0),
                        child: Text("• Stamp\n"),
                      ),
                    ),
                    WidgetSpan(
                      alignment: PlaceholderAlignment.baseline,
                      baseline: TextBaseline.alphabetic,
                      child: Padding(
                        padding: EdgeInsets.only(left: 12.0),
                        child: Text("• PDF size should not exceed 3 MB\n"),
                      ),
                    ),
                  ],
                ),
              ),

              Row(
                children: [
                  (stLoanPdfDoc == null)
                      ? ElevatedButton(
                          style: const ButtonStyle(
                            shape: WidgetStatePropertyAll(
                                ContinuousRectangleBorder(
                                    borderRadius:
                                        BorderRadius.all(Radius.circular(10)))),
                            backgroundColor:
                                WidgetStatePropertyAll(Colors.black),
                            elevation: WidgetStatePropertyAll(0),
                          ),
                          onPressed: () async {
                            await pickFiles();

                            if (stLoanPdfDoc != null) {
                              String urls = await uploadFile();
                              // print('Uploaded file URLs: $urls');
                            } else {
                              // print("No PDF file selected.");
                            }
                          },
                          child: const Text(
                            "Upload PDF",
                            style: TextStyle(color: Colors.white),
                          ),
                        )
                      : Expanded(
                          child: Text(
                            "$stLoanPdfDoc",
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                  (stLoanPdfDoc != null)
                      ? IconButton(
                          icon: const Icon(Icons.cancel, color: Colors.red),
                          onPressed: removeStPDF, // Remove the selected file
                        )
                      : SizedBox.shrink()
                ],
              ),

              const SizedBox(height: 10),

              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 0, vertical: 20),
                child: Row(
                  children: [
                    Checkbox(
                      value: checkboxValue2,
                      onChanged: (value) {
                        setState(() {
                          checkboxValue2 = value!;
                        });
                      },
                    ),
                    Flexible(
                      child: RichText(
                        text: TextSpan(
                          style: const TextStyle(
                            fontSize: 15,
                            color: Colors.black,
                          ),
                          children: [
                            const TextSpan(
                              text: "I have read and agree to the ",
                            ),
                            TextSpan(
                              text: "Terms and Conditions",
                              style: const TextStyle(
                                color: Colors.blue,
                                decoration: TextDecoration.underline,
                              ),
                              recognizer: TapGestureRecognizer()
                                ..onTap = () {
                                  context.push(Routes.stloantc);
                                },
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              stOnSubmitLoad
                  ? const CircularProgressIndicator()
                  : ElevatedButton(
                      style: const ButtonStyle(
                        padding: WidgetStatePropertyAll(
                            EdgeInsetsDirectional.symmetric(
                                horizontal: 100, vertical: 15)),
                        shape: WidgetStatePropertyAll(ContinuousRectangleBorder(
                            borderRadius:
                                BorderRadius.all(Radius.circular(10)))),
                        backgroundColor: WidgetStatePropertyAll(Colors.green),
                        elevation: WidgetStatePropertyAll(0),
                      ),
                      onPressed: () async {
                        // if (checkboxValue2 == false) {
                        //   showAppSnackBar(
                        //       " Please Accept the Terms and Conditions");
                        //   return;
                        // } else {
                        if (acnoctrl.text.isEmpty ||
                            fullnamectrl.text.isEmpty ||
                            numamtctrl.text.isEmpty ||
                            surity1ctrl.text.isEmpty ||
                            surity2ctrl.text.isEmpty ||
                            amtctrl.text.isEmpty ||
                            loanReasonctrl.text.isEmpty ||
                            borrowersignatureImage == null) {
                          showAppSnackBar(
                              duration: const Duration(milliseconds: 2000),
                              "Please fill all the details");
                          return;
                        }

                        if (stLoanPdfDoc == null) {
                          showAppSnackBar(
                              duration: const Duration(milliseconds: 2000),
                              "Please upload Pdf");
                          return;
                        }

                        final appliedAmt =
                            num.tryParse(numamtctrl.text.trim()) ?? 0;
                        final maxAmt = num.tryParse(
                                ctrl.settings?.maxStLoanAmt.toString() ??
                                    "0") ??
                            0;

                        if (appliedAmt > maxAmt) {
                          showAppSnackBar(
                              duration: const Duration(milliseconds: 2000),
                              "Applied amount exceeds maximum limit");
                          setState(() {
                            stOnSubmitLoad = false;
                          });
                          return;
                        }

                        if (!checkboxValue2) {
                          showAppSnackBar(
                              "Please Accept the Terms and Conditions");
                          setState(() {
                            stOnSubmitLoad = false;
                          });
                          return;
                        } else {
                          final stloans = ctrl.activeLoans
                              .where((element) =>
                                  element.loanType == LoanTypes.emergencyLoan)
                              .where(
                                (element) => element.isNew == true,
                              );

                          // print("stloans : ${stloans.length}");

                          if (stloans.isNotEmpty) {
                            return showAppSnackBar("Loan already exist.");
                          }
                          setState(() {
                            stOnSubmitLoad = true;
                          });

                          try {
                            final borrowImageUrl =
                                await uploadSignimages(borrowersignatureImage!);
                            // final surity1ImageUrl =
                            //     await uploadSignimages(surity1signatureImage!);
                            // final surity2ImageUrl =
                            //     await uploadSignimages(surity2signatureImage!);
                            await FBFireStore.loan.add({
                              'uid': ctrl.users?.docId,
                              // 'name': fullnamectrl.text,

                              // FBAuth.auth.currentUser?.uid,
                              'createdAt': Timestamp.now(),
                              'totalLoanAmt': 0,
                              'appliedLoanAmt':
                                  num.tryParse(numamtctrl.text.trim()) ?? 0,
                              'totalLoanPaid': 0,
                              'totalLoanDue': 0,
                              'loanType': LoanTypes.emergencyLoan,
                              'isSettled': false,
                              'settledOn': null, // Nullable
                              'isNew': true,
                              'appliedOn': Timestamp.now(),
                              'applicationNo':
                                  Get.find<HomeCtrl>().settings!.applicationNo +
                                      1,
                              'approvedOn': null, // Nullable
                              'processedOn': null, // Nullable
                              'share': 0,
                              'totalInterestPaid': 0,
                              'designation': null,
                              'bAcNo': num.tryParse(acnoctrl.text) ?? 0,
                              'appliedLoanAmtinWords': amtctrl.text.trim(),
                              'loanReason': loanReasonctrl.text.trim(),
                              'bSign': borrowImageUrl,
                              'surityName1': surity1ctrl.text.trim(),
                              'surityName2': surity2ctrl.text.trim(),
                              'sAcNo1': 0,
                              'sAcNo2': 0,
                              // 'suritySign1': surity1ImageUrl,
                              // 'suritySign2': surity2ImageUrl,
                              'rejectionDate': null,
                              'rejectionReason': null,
                              'monthlyInstallmentAmt': num.tryParse(
                                  ctrl.settings?.defaultStinstallAmt ?? "0"),
                              'pdfString': stLoanPdfDoc,
                            });

                            await FBFireStore.settings.update(
                                {'applicationNo': FieldValue.increment(1)});

                            context.go(Routes.dashboard);

                            appnoctrl.clear();
                            fullnamectrl.clear();
                            acnoctrl.clear();
                            numamtctrl.clear();
                            amtctrl.clear();
                            surity1ctrl.clear();
                            surity2ctrl.clear();
                            balctrl.clear();
                            loanReasonctrl.clear();
                            borrowersignatureImage = null;
                            // surity1signatureImage = null;
                            // surity2signatureImage = null;

                            setState(() {
                              stOnSubmitLoad = false;
                            });
                            showAppSnackBar(
                                duration: const Duration(milliseconds: 2000),
                                "Loan Applied Successfully");
                          } catch (e) {
                            setState(() {
                              stOnSubmitLoad = false;
                            });
                          }
                        }

                        // }
                      },
                      child: const Text(
                        "APPLY",
                        style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            letterSpacing: 3,
                            fontWeight: FontWeight.bold),
                      ),
                    ),
            ],
          ),
        );
      }),
    );
  }

  // Row surity2SignRow(BuildContext context) {
  //   return Row(
  //     children: [
  //       Align(
  //         alignment: Alignment.centerLeft,
  //         child: ElevatedButton(
  //           style: const ButtonStyle(
  //             shape: WidgetStatePropertyAll(ContinuousRectangleBorder(
  //                 borderRadius: BorderRadius.all(Radius.circular(10)))),
  //             backgroundColor: WidgetStatePropertyAll(Colors.black),
  //             elevation: WidgetStatePropertyAll(0),
  //           ),
  //           onPressed: () => showDialog(
  //             context: context,
  //             builder: (context) => Column(
  //               mainAxisAlignment: MainAxisAlignment.center,
  //               crossAxisAlignment: CrossAxisAlignment.center,
  //               children: [
  //                 Signature(
  //                   height: 400,
  //                   width: 300,
  //                   dynamicPressureSupported: true,
  //                   backgroundColor: Colors.white,
  //                   controller: s2sctrl,
  //                 ),
  //                 const SizedBox(height: 10),
  //                 Row(
  //                   mainAxisAlignment: MainAxisAlignment.center,
  //                   children: [
  //                     ElevatedButton(
  //                       style: const ButtonStyle(
  //                         shape: WidgetStatePropertyAll(
  //                             ContinuousRectangleBorder(
  //                                 borderRadius:
  //                                     BorderRadius.all(Radius.circular(10)))),
  //                         backgroundColor: WidgetStatePropertyAll(Colors.green),
  //                         elevation: WidgetStatePropertyAll(0),
  //                       ),
  //                       onPressed: () async {
  //                         final image = await s2sctrl.toImage();
  //                         final byteData = await image?.toByteData(
  //                             format: ImageByteFormat.png);

  //                         if (byteData != null) {
  //                           setState(() {
  //                             surity2signatureImage =
  //                                 byteData.buffer.asUint8List();
  //                           });
  //                         }
  //                         print("Surity2 Signature captured!");
  //                         Navigator.pop(context);
  //                       },
  //                       child: const Text(
  //                         "Submit",
  //                         style: TextStyle(color: Colors.white),
  //                       ),
  //                     ),
  //                     const SizedBox(width: 10),
  //                     ElevatedButton(
  //                       style: const ButtonStyle(
  //                         shape: WidgetStatePropertyAll(
  //                             ContinuousRectangleBorder(
  //                                 borderRadius:
  //                                     BorderRadius.all(Radius.circular(10)))),
  //                         backgroundColor: WidgetStatePropertyAll(Colors.grey),
  //                         elevation: WidgetStatePropertyAll(0),
  //                       ),
  //                       onPressed: () {
  //                         surity2signatureImage = null;
  //                         s2sctrl.clear();
  //                       },
  //                       child: const Text(
  //                         "Clear Signature",
  //                         style: TextStyle(color: Colors.white),
  //                       ),
  //                     ),
  //                   ],
  //                 ),
  //               ],
  //             ),
  //           ),
  //           child: const Text(
  //             "Add Signature",
  //             style: TextStyle(color: Colors.white),
  //           ),
  //         ),
  //       ),
  //       const SizedBox(width: 25),
  //       surity2signatureImage != null
  //           ? SizedBox(
  //               height: 50,
  //               child: Image.memory(surity2signatureImage!),
  //             )
  //           : const SizedBox()
  //     ],
  //   );
  // }

  // Row surity1SignRow(BuildContext context) {
  //   return Row(
  //     children: [
  //       Align(
  //         alignment: Alignment.centerLeft,
  //         child: ElevatedButton(
  //           style: const ButtonStyle(
  //             shape: WidgetStatePropertyAll(ContinuousRectangleBorder(
  //                 borderRadius: BorderRadius.all(Radius.circular(10)))),
  //             backgroundColor: WidgetStatePropertyAll(Colors.black),
  //             elevation: WidgetStatePropertyAll(0),
  //           ),
  //           onPressed: () => showDialog(
  //             context: context,
  //             builder: (context) => Column(
  //               mainAxisAlignment: MainAxisAlignment.center,
  //               crossAxisAlignment: CrossAxisAlignment.center,
  //               children: [
  //                 Signature(
  //                   height: 400,
  //                   width: 300,
  //                   dynamicPressureSupported: true,
  //                   backgroundColor: Colors.white,
  //                   controller: s1sctrl,
  //                 ),
  //                 const SizedBox(height: 10),
  //                 Row(
  //                   mainAxisAlignment: MainAxisAlignment.center,
  //                   children: [
  //                     ElevatedButton(
  //                       style: const ButtonStyle(
  //                         shape: WidgetStatePropertyAll(
  //                             ContinuousRectangleBorder(
  //                                 borderRadius:
  //                                     BorderRadius.all(Radius.circular(10)))),
  //                         backgroundColor: WidgetStatePropertyAll(Colors.green),
  //                         elevation: WidgetStatePropertyAll(0),
  //                       ),
  //                       onPressed: () async {
  //                         final image = await s1sctrl.toImage();
  //                         final byteData = await image?.toByteData(
  //                             format: ImageByteFormat.png);

  //                         if (byteData != null) {
  //                           setState(() {
  //                             surity1signatureImage =
  //                                 byteData.buffer.asUint8List();
  //                           });
  //                         }

  //                         print("Surity1 Signature captured!");
  //                         Navigator.pop(context);
  //                       },
  //                       child: const Text("Submit",
  //                           style: TextStyle(color: Colors.white)),
  //                     ),
  //                     const SizedBox(width: 10),
  //                     ElevatedButton(
  //                       style: const ButtonStyle(
  //                         shape: WidgetStatePropertyAll(
  //                             ContinuousRectangleBorder(
  //                                 borderRadius:
  //                                     BorderRadius.all(Radius.circular(10)))),
  //                         backgroundColor: WidgetStatePropertyAll(Colors.grey),
  //                         elevation: WidgetStatePropertyAll(0),
  //                       ),
  //                       onPressed: () {
  //                         surity1signatureImage = null;
  //                         s1sctrl.clear();
  //                       },
  //                       child: const Text("Clear Signature",
  //                           style: TextStyle(color: Colors.white)),
  //                     ),
  //                   ],
  //                 ),
  //               ],
  //             ),
  //           ),
  //           child: const Text(
  //             "Add Signature",
  //             style: TextStyle(color: Colors.white),
  //           ),
  //         ),
  //       ),
  //       const SizedBox(width: 25),
  //       surity1signatureImage != null
  //           ? SizedBox(
  //               height: 50,
  //               child: Image.memory(surity1signatureImage!),
  //             )
  //           : const SizedBox()
  //     ],
  //   );
  // }

  Row borrowerSignRow(BuildContext context) {
    return Row(
      children: [
        ElevatedButton(
          style: const ButtonStyle(
            shape: WidgetStatePropertyAll(ContinuousRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(10)))),
            backgroundColor: WidgetStatePropertyAll(Colors.black),
            elevation: WidgetStatePropertyAll(0),
          ),
          onPressed: () => showDialog(
            context: context,
            builder: (context) => Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Signature(
                  height: 400,
                  width: 300,
                  dynamicPressureSupported: true,
                  backgroundColor: Colors.white,
                  controller: bsignctrl,
                ),
                const SizedBox(height: 10),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ElevatedButton(
                      style: const ButtonStyle(
                        shape: WidgetStatePropertyAll(ContinuousRectangleBorder(
                            borderRadius:
                                BorderRadius.all(Radius.circular(10)))),
                        backgroundColor: WidgetStatePropertyAll(Colors.green),
                        elevation: WidgetStatePropertyAll(0),
                      ),
                      onPressed: () async {
                        final image = await bsignctrl.toImage();
                        final byteData = await image?.toByteData(
                            format: ImageByteFormat.png);

                        if (byteData != null) {
                          setState(() {
                            borrowersignatureImage =
                                byteData.buffer.asUint8List();
                          });
                        }

                        // print("Signature captured!");
                        Navigator.pop(context);
                      },
                      child: const Text(
                        "Submit",
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                    const SizedBox(width: 10),
                    ElevatedButton(
                      style: const ButtonStyle(
                        shape: WidgetStatePropertyAll(ContinuousRectangleBorder(
                            borderRadius:
                                BorderRadius.all(Radius.circular(10)))),
                        backgroundColor: WidgetStatePropertyAll(Colors.grey),
                        elevation: WidgetStatePropertyAll(0),
                      ),
                      onPressed: () {
                        borrowersignatureImage = null;
                        bsignctrl.clear();
                      },
                      child: const Text(
                        "Clear Signature",
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          child: const Text(
            "Add Borrower's Signature",
            style: TextStyle(color: Colors.white),
          ),
        ),
        const SizedBox(width: 25),
        borrowersignatureImage != null
            ? SizedBox(
                height: 50,
                child: Image.memory(borrowersignatureImage!),
              )
            : const SizedBox()
      ],
    );
  }
}

// class StFormHeader extends StatelessWidget {
//   const StFormHeader({
//     super.key,
//     required this.appnoctrl,
//   });
//   final TextEditingController appnoctrl;

//   @override
//   Widget build(BuildContext context) {
//     return Row(
//       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//       children: [
//         // InkWell(
//         //   onTap: () {
//         //     // print("image inkwell pressed");
//         //     context.pop(Routes.dashboard);
//         //   },
//         //   child: Image(
//         //       height: 50,
//         //       image: AssetImage('assets/images/foodcorpimage4_removebg.png')),
//         // ),
//         // Row(
//         //   children: [
//         //     const Column(
//         //       children: [
//         //         SizedBox(height: 8),
//         //         Text("Application No."),
//         //       ],
//         //     ),
//         //     const SizedBox(width: 5),
//         //     SizedBox(
//         //       width: 100,
//         //       height: 28,
//         //       child: TextFormField(
//         //         keyboardType: TextInputType.number,
//         //         cursorHeight: 15,
//         //         controller: appnoctrl,
//         //         decoration: const InputDecoration(border: OutlineInputBorder()),
//         //       ),
//         //     ),
//         //   ],
//         // )
//       ],
//     );
//   }
// }
