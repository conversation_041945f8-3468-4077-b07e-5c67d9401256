import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:foodcorp/controller/homectrl.dart';
import 'package:foodcorp/models/poll_model.dart';
import 'package:foodcorp/shared/firebase.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:intl/intl.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class NotificationsPage extends StatefulWidget {
  const NotificationsPage({super.key});

  @override
  State<NotificationsPage> createState() => _NotificationsPageState();
}

class _NotificationsPageState extends State<NotificationsPage> {
  bool loading = false;
  final DateFormat dateFormatter = DateFormat('dd MMM yyyy, hh:mm a');

  // Map to keep track of user selections for each poll (pollId -> list of selected options)
  final Map<String, List<String>> _userPollSelections = {};

  // Get current user ID from Firebase Auth
  String get currentUserId => FBAuth.auth.currentUser?.uid ?? '';

  // Submit vote to Firestore
  Future<void> _submitVote(String pollId, List<String> selectedOptions) async {
    if (currentUserId.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please login to vote')),
      );
      return;
    }

    setState(() {
      loading = true;
    });

    try {
      // Update poll document in Firestore
      await FBFireStore.polls.doc(pollId).update({
        'userVotes.$currentUserId': selectedOptions.length == 1
            ? selectedOptions.first
            : selectedOptions,
        // Update vote counts
        ...{
          for (String option in selectedOptions)
            'votes.$option': FieldValue.increment(1)
        }
      });

      setState(() {
        _userPollSelections[pollId] = selectedOptions;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Vote submitted successfully!')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to submit vote: $e')),
        );
      }
    } finally {
      setState(() {
        loading = false;
      });
    }
  }

  // Helper methods for notification styling
  // Color _getNotificationColor(String source) {
  //   switch (source) {
  //     case 'regular':
  //       return Colors.blue;
  //     case 'district':
  //       return const Color.fromRGBO(29, 167, 95, 1);
  //     case 'global':
  //       return Colors.orange;
  //     case 'poll':
  //       return Colors.purple;
  //     default:
  //       return Colors.grey;
  //   }
  // }

  IconData _getNotificationIcon(String source) {
    switch (source) {
      case 'regular':
        return Icons.notifications;
      case 'district':
        return Icons.location_city;
      case 'global':
        return Icons.public;
      case 'poll':
        return Icons.poll;
      default:
        return Icons.notifications;
    }
  }

  String _getSourceLabel(String source) {
    switch (source) {
      case 'regular':
        return 'PERSONAL';
      case 'district':
        return 'DISTRICT';
      case 'global':
        return 'GLOBAL';
      case 'poll':
        return 'POLL';
      default:
        return 'NOTIFICATION';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: false,
        title: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // const Image(
            //     // height: 40,
            //     width: 65,
            //     image: AssetImage("assets/images/foodcorpimage4_removebg.png")),
            // SizedBox(width: 10),
            Text("Notifications",
                style: GoogleFonts.sourceCodePro(
                    letterSpacing: -1,
                    fontSize: 18.71.sp,
                    fontWeight: FontWeight.w700)),
          ],
        ),
        // backgroundColor: const Color.fromRGBO(29, 167, 95, 1),
        // foregroundColor: Colors.white,
      ),
      body: GetBuilder<HomeCtrl>(builder: (hctrl) {
        // Combine all notification types and polls
        final allItems = <Map<String, dynamic>>[
          // Regular notifications
          ...hctrl.notifications.map((n) => {
                'type': 'notification',
                'id': n.docId,
                'title': n.title,
                'desc': n.desc,
                'createdAt': n.createdAt,
                'attachment': n.attachment,
                'source': 'regular',
              }),
          // Custom notifications (district-specific)
          ...hctrl.customnotifications.map((n) => {
                'type': 'notification',
                'id': n.docId,
                'title': n.title,
                'desc': n.desc,
                'createdAt': n.createdAt,
                'attachment': n.attachment,
                'source': 'district',
              }),
          // Global custom notifications
          ...hctrl.globalcustomnotificationsdata.map((n) => {
                'type': 'notification',
                'id': n.docId,
                'title': n.title,
                'desc': n.desc,
                'createdAt': n.createdAt,
                'attachment': n.attachment,
                'source': 'global',
              }),
          // Polls
          ...hctrl.polls.map((p) => {
                'type': 'poll',
                'id': p.id,
                'poll': p,
                'createdAt': p.expiryDate ?? DateTime.now(),
                'source': 'poll',
              }),
        ];

        // Sort by creation date (latest first)
        allItems.sort((a, b) =>
            (b['createdAt'] as DateTime).compareTo(a['createdAt'] as DateTime));

        if (allItems.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.notifications_off, size: 64, color: Colors.grey),
                SizedBox(height: 16),
                Text(
                  "No Notifications or Polls",
                  style: TextStyle(fontSize: 18, color: Colors.grey),
                ),
              ],
            ),
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 13),
          child: loading
              ? const Center(
                  heightFactor: 20, child: CircularProgressIndicator())
              : Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: allItems.map<Widget>((item) {
                    if (item['type'] == 'poll') {
                      final PollModel poll = item['poll'] as PollModel;
                      final selectedOptions =
                          _userPollSelections[poll.id] ?? <String>[];

                      // Check if user has already voted in Firestore
                      final bool hasVotedInFirestore =
                          poll.userVotes?.containsKey(currentUserId) ?? false;
                      final bool hasVoted =
                          hasVotedInFirestore || selectedOptions.isNotEmpty;

                      Widget optionWidget(String option) {
                        if (poll.allowsMultipleAnswers) {
                          return CheckboxListTile(
                            title: Text(option),
                            value: selectedOptions.contains(option),
                            onChanged: hasVoted
                                ? null
                                : (bool? checked) {
                                    setState(() {
                                      if (checked == true) {
                                        selectedOptions.add(option);
                                      } else {
                                        selectedOptions.remove(option);
                                      }
                                      _userPollSelections[poll.id] =
                                          List.from(selectedOptions);
                                    });
                                  },
                          );
                        } else {
                          return RadioListTile<String>(
                            title: Text(option),
                            value: option,
                            groupValue: selectedOptions.isNotEmpty
                                ? selectedOptions.first
                                : null,
                            onChanged: hasVoted
                                ? null
                                : (String? val) {
                                    setState(() {
                                      _userPollSelections[poll.id] =
                                          val != null ? [val] : [];
                                    });
                                  },
                          );
                        }
                      }

                      return Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Poll header
                            Row(
                              children: [
                                const Icon(Icons.poll,
                                    color: Color.fromRGBO(29, 167, 95, 1)),
                                const SizedBox(width: 8),
                                const Text(
                                  'POLL',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: Color.fromRGBO(29, 167, 95, 1),
                                    fontSize: 12,
                                  ),
                                ),
                                const Spacer(),
                                if (poll.expiryDate != null)
                                  Text(
                                    'Expires: ${dateFormatter.format(poll.expiryDate!)}',
                                    style: const TextStyle(
                                      fontSize: 10,
                                      color: Colors.grey,
                                    ),
                                  ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            Text(
                              poll.question,
                              style: const TextStyle(
                                  fontWeight: FontWeight.bold, fontSize: 16),
                            ),
                            const SizedBox(height: 12),
                            ...poll.options.map(optionWidget),
                            const SizedBox(height: 12),
                            Row(
                              children: [
                                Expanded(
                                  child: ElevatedButton(
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: hasVoted
                                          ? Colors.grey
                                          : const Color.fromRGBO(
                                              29, 167, 95, 1),
                                      foregroundColor: Colors.white,
                                    ),
                                    onPressed: (!hasVoted &&
                                            _userPollSelections[poll.id]
                                                    ?.isNotEmpty ==
                                                true)
                                        ? () => _submitVote(poll.id,
                                            _userPollSelections[poll.id]!)
                                        : null,
                                    child: Text(hasVoted
                                        ? 'Thank you for voting'
                                        : 'Submit Vote'),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      );
                    }

                    // Default notification rendering
                    return ListTile(
                      contentPadding: const EdgeInsets.all(16),
                      leading: CircleAvatar(
                        // backgroundColor:
                        //     _getNotificationColor(item['source'] as String),
                        child: Icon(
                          _getNotificationIcon(item['source'] as String),
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                      title: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  (item['title'] as String).toUpperCase(),
                                  style: const TextStyle(
                                    fontSize: 14,
                                    color: Colors.black,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                              Text(
                                _getSourceLabel(item['source'] as String),
                                style: TextStyle(
                                  fontSize: 10,
                                  // color: _getNotificationColor(
                                  //     item['source'] as String),
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 4),
                          Text(
                            dateFormatter.format(item['createdAt'] as DateTime),
                            style: const TextStyle(
                              fontSize: 11,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                      subtitle: Padding(
                        padding: const EdgeInsets.only(top: 8),
                        child: Text(
                          item['desc'] as String,
                          style: const TextStyle(
                            fontSize: 13,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                      trailing: item['attachment'] != null
                          ? IconButton(
                              icon: const Icon(Icons.download_rounded,
                                  color: Colors.blue),
                              tooltip: 'Download Attachment',
                              onPressed: () async {
                                final url = item['attachment'] as String;
                                final scaffoldMessenger =
                                    ScaffoldMessenger.of(context);

                                try {
                                  if (await canLaunchUrl(Uri.parse(url))) {
                                    await launchUrl(Uri.parse(url),
                                        mode: LaunchMode.externalApplication);
                                  } else {
                                    if (mounted) {
                                      scaffoldMessenger.showSnackBar(
                                        const SnackBar(
                                            content: Text(
                                                'Could not launch document URL')),
                                      );
                                    }
                                  }
                                } catch (e) {
                                  if (mounted) {
                                    scaffoldMessenger.showSnackBar(
                                      SnackBar(content: Text('Error: $e')),
                                    );
                                  }
                                }
                              },
                            )
                          : null,
                    );
                  }).toList(),
                ),
        );
      }),
    );
  }
}
