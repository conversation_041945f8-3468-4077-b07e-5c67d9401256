import 'package:flutter/material.dart';
import 'package:foodcorp/controller/homectrl.dart';
import 'package:foodcorp/models/poll_model.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';
// Adjust import to your PollModel location

class NotificationsPage extends StatefulWidget {
  const NotificationsPage({super.key});

  @override
  State<NotificationsPage> createState() => _NotificationsPageState();
}

class _NotificationsPageState extends State<NotificationsPage> {
  bool loading = false;

  // Map to keep track of user selections for each poll (pollId -> list of selected options)
  final Map<String, List<String>> _userPollSelections = {};

  // TODO: Replace with actual current user ID from your auth system
  final String currentUserId = 'user_123';

  // Simulated async function to submit user vote for a poll
  Future<void> _submitVote(String pollId, List<String> selectedOptions) async {
    setState(() {
      loading = true;
    });
    try {
      // TODO: Add backend submission logic here (e.g., Firestore transaction updating votes and userVotes with currentUserId)

      // Simulate delay for demo
      await Future.delayed(const Duration(seconds: 1));

      setState(() {
        _userPollSelections[pollId] = selectedOptions;
      });
    } catch (e) {
      ScaffoldMessenger.of(context)
          .showSnackBar(SnackBar(content: Text('Failed to submit vote: $e')));
    } finally {
      setState(() {
        loading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Notifications')),
      body: GetBuilder<HomeCtrl>(builder: (hctrl) {
        final allItems = [
          ...hctrl.notifications.map((n) => {
                'type': 'notification',
                'id': n.uId,
                'title': n.title.toUpperCase(),
                'desc': n.desc,
                'createdAt': n.createdAt,
                'attachment': n.attachment,
              }),
          ...hctrl.polls.map((p) => {
                'type': 'poll',
                'id': p.id,
                'poll': p,
                // Use poll expiryDate or current date for sorting as example
                'createdAt': p.expiryDate ?? DateTime.now(),
              }),
        ];
        // You can sort `allItems` by createdAt descending if desired:
        // allItems.sort((a, b) => b['createdAt'].compareTo(a['createdAt']));

        if (allItems.isEmpty) {
          return const Center(child: Text("No Notifications"));
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 13),
          child: loading
              ? const Center(
                  heightFactor: 20, child: CircularProgressIndicator())
              : Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: allItems.map<Widget>((item) {
                    if (item['type'] == 'poll') {
                      final PollModel poll = item['poll'] as PollModel;
                      final selectedOptions =
                          _userPollSelections[poll.id] ?? <String>[];
                      final bool hasVoted = selectedOptions.isNotEmpty;

                      Widget optionWidget(String option) {
                        if (poll.allowsMultipleAnswers) {
                          return CheckboxListTile(
                            title: Text(option),
                            value: selectedOptions.contains(option),
                            onChanged: hasVoted
                                ? null
                                : (bool? checked) {
                                    setState(() {
                                      if (checked == true) {
                                        selectedOptions.add(option);
                                      } else {
                                        selectedOptions.remove(option);
                                      }
                                      _userPollSelections[poll.id] =
                                          List.from(selectedOptions);
                                    });
                                  },
                          );
                        } else {
                          return RadioListTile<String>(
                            title: Text(option),
                            value: option,
                            groupValue: selectedOptions.isNotEmpty
                                ? selectedOptions.first
                                : null,
                            onChanged: hasVoted
                                ? null
                                : (String? val) {
                                    setState(() {
                                      _userPollSelections[poll.id] =
                                          val != null ? [val] : [];
                                    });
                                  },
                          );
                        }
                      }

                      return Card(
                        margin: const EdgeInsets.only(bottom: 20),
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                poll.question,
                                style: const TextStyle(
                                    fontWeight: FontWeight.bold, fontSize: 16),
                              ),
                              const SizedBox(height: 12),
                              ...poll.options.map(optionWidget),
                              const SizedBox(height: 12),
                              ElevatedButton(
                                onPressed: (!hasVoted &&
                                        _userPollSelections[poll.id]
                                                ?.isNotEmpty ==
                                            true)
                                    ? () => _submitVote(
                                        poll.id, _userPollSelections[poll.id]!)
                                    : null,
                                child: Text(hasVoted
                                    ? 'Thank you for voting'
                                    : 'Submit Vote'),
                              )
                            ],
                          ),
                        ),
                      );
                    }

                    // Default notification rendering
                    return ListTile(
                      shape: const Border(
                          bottom: BorderSide(
                              color: Color.fromARGB(255, 178, 177, 177))),
                      leading: const Icon(Icons.notifications_active),
                      title: Text(
                        item['title'],
                        style: const TextStyle(
                            fontSize: 13,
                            color: Colors.black,
                            fontWeight: FontWeight.w500),
                      ),
                      subtitle: Text(
                        item['desc'],
                        style: const TextStyle(
                            fontSize: 15,
                            color: Colors.black,
                            fontWeight: FontWeight.bold),
                      ),
                      trailing: item['attachment'] != null
                          ? IconButton(
                              icon: const Icon(Icons.download_rounded,
                                  color: Colors.blue),
                              tooltip: 'Download Attachment',
                              onPressed: () async {
                                final url = item['attachment'];
                                if (await canLaunchUrl(Uri.parse(url))) {
                                  await launchUrl(Uri.parse(url),
                                      mode: LaunchMode.externalApplication);
                                } else {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                        content: Text(
                                            'Could not launch document URL')),
                                  );
                                }
                              },
                            )
                          : null,
                    );
                  }).toList(),
                ),
        );
      }),
    );
  }
}
