import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../controller/homectrl.dart';

class NotificationsPage extends StatefulWidget {
  const NotificationsPage({super.key});

  @override
  State<NotificationsPage> createState() => _NotificationsPageState();
}

class _NotificationsPageState extends State<NotificationsPage> {
  bool loading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        surfaceTintColor: Colors.white,
        scrolledUnderElevation: 5,
        shadowColor: Colors.black26,
        centerTitle: false,
        title: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // const Image(
            //     // height: 40,
            //     width: 65,
            //     image: AssetImage("assets/images/foodcorpimage4_removebg.png")),
            // SizedBox(width: 10),
            Text(
              "Notifications",
              style: GoogleFonts.sourceCodePro(
                  letterSpacing: -1,
                  fontSize: 18.71.sp,
                  fontWeight: FontWeight.w700),
            ),
          ],
        ),
      ),
      body: GetBuilder<HomeCtrl>(builder: (hctrl) {
        final List<Map<String, dynamic>> allNotifications = [
          ...hctrl.notifications.map<Map<String, dynamic>>((n) => {
                'title': n.title.toUpperCase(),
                'desc': n.desc,
                'createdAt': n.createdAt,
              }),
          ...hctrl.customnotifications.map<Map<String, dynamic>>((n) => {
                'title': n.title,
                'desc': n.desc,
                'createdAt': n.createdAt,
              }),
          ...hctrl.globalcustomnotificationsdata
              .map<Map<String, dynamic>>((n) => {
                    'title': n.title.toUpperCase(),
                    'desc': n.desc,
                    'createdAt': n.createdAt,
                  }),
        ]..sort((a, b) => b['createdAt'].compareTo(a['createdAt']));

        if (allNotifications.isEmpty) {
          return Center(child: Text("No Notifications"));
        }
        return SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.only(top: 20.h, right: 13.w, left: 13.w),
            child: loading
                ? Center(heightFactor: 20, child: CircularProgressIndicator())
                : Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ...allNotifications.map((notification) => ListTile(
                            shape: const Border(
                                bottom: BorderSide(
                                    color: Color.fromARGB(255, 178, 177, 177))),
                            leading: const Icon(Icons.notifications_active),
                            title: Text(
                              notification['title'],
                              style: TextStyle(
                                  fontSize: 13.sp,
                                  color: Colors.black,
                                  fontWeight: FontWeight.w500),
                            ),
                            subtitle: Text(
                              notification['desc'],
                              style: TextStyle(
                                  fontSize: 15.sp,
                                  color: Colors.black,
                                  fontWeight: FontWeight.bold),
                            ),
                          )),
                    ],
                  ),
          ),
        );
      }),
    );
  }
}



 // padding:
                    //     EdgeInsets.symmetric(horizontal: 10.h, vertical: 10.h),
                    // decoration: BoxDecoration(
                    //   border: Border.all(
                    //       color: const Color.fromRGBO(224, 224, 224, 1)),
                    //   borderRadius: BorderRadius.circular(16),
                    // ),
                      // ...notifications.map((notification) => ListTile(
                      //       shape: const Border(
                      //           bottom: BorderSide(
                      //               color: Color.fromARGB(255, 178, 177, 177))),
                      //       leading: Icon(Icons.notifications_active),
                      //       title: Text(
                      //         notification.title.toUpperCase(),
                      //         style: TextStyle(
                      //             fontSize: 13.sp,
                      //             color: Colors.black,
                      //             fontWeight: FontWeight.w500),
                      //       ),
                      //       subtitle: Text(
                      //         notification.desc.toString(),
                      //         style: TextStyle(
                      //             fontSize: 15.sp,
                      //             color: Colors.black,
                      //             fontWeight: FontWeight.bold),
                      //       ),
                      //     )),
                      // ...customNotifications.map((custom) => ListTile(
                      //       shape: const Border(
                      //           bottom: BorderSide(
                      //               color: Color.fromARGB(255, 178, 177, 177))),
                      //       leading: Icon(Icons.notifications_active),
                      //       title: Text(
                      //         custom.title,
                      //         style: TextStyle(
                      //             fontSize: 13.sp,
                      //             color: Colors.black,
                      //             fontWeight: FontWeight.w500),
                      //       ),
                      //       subtitle: Text(
                      //         custom.desc.toString(),
                      //         style: TextStyle(
                      //             fontSize: 15.sp,
                      //             color: Colors.black,
                      //             fontWeight: FontWeight.bold),
                      //       ),
                      //     )),
                      // ...globalcustomnotifications.map((global) => ListTile(
                      //       shape: const Border(
                      //           bottom: BorderSide(
                      //               color: Color.fromARGB(255, 178, 177, 177))),
                      //       leading: Icon(Icons.notifications_active),
                      //       title: Text(
                      //         global.title.toUpperCase(),
                      //         style: TextStyle(
                      //             fontSize: 13.sp,
                      //             color: Colors.black,
                      //             fontWeight: FontWeight.w500),
                      //       ),
                      //       subtitle: Text(
                      //         global.desc.toString(),
                      //         style: TextStyle(
                      //             fontSize: 15.sp,
                      //             color: Colors.black,
                      //             fontWeight: FontWeight.bold),
                      //       ),
                      //     )),