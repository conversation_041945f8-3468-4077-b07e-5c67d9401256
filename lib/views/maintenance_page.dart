import 'package:flutter/material.dart';
import 'package:foodcorp/controller/homectrl.dart';
import 'package:get/get_state_manager/src/simple/get_state.dart';

class MaintenancePage extends StatefulWidget {
  const MaintenancePage({super.key});

  @override
  State<MaintenancePage> createState() => _MaintenancePageState();
}

class _MaintenancePageState extends State<MaintenancePage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GetBuilder<HomeCtrl>(builder: (ctrl) {
        return Column(
          children: [
            Text(ctrl.settings?.maintenanceString ?? ""),
          ],
        );
      }),
    );
  }
}
