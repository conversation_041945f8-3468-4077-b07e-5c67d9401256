// ignore_for_file: use_build_context_synchronously

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:foodcorp/controller/homectrl.dart';
import 'package:foodcorp/main.dart';
import 'package:foodcorp/models/user_monthlyrecord_model.dart';
import 'package:foodcorp/shared/const.dart';
import 'package:foodcorp/shared/firebase.dart';
import 'package:foodcorp/shared/methods.dart';
import 'package:foodcorp/shared/router.dart';
import 'package:foodcorp/views/profile/profile_page.dart';
import 'package:foodcorp/views/wrapper.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:printing/printing.dart';
import 'package:screenshot/screenshot.dart';

import 'package:pdf/widgets.dart' as pw;
import 'dart:io' as io;

import 'package:share_plus/share_plus.dart';

class DashboardPage extends StatefulWidget {
  const DashboardPage({super.key});

  @override
  State<DashboardPage> createState() => _DashboardPageState();
}

bool secondTime = false;

class _DashboardPageState extends State<DashboardPage> {
  TextEditingController nctrl = TextEditingController();
  TextEditingController cpfnoctrl = TextEditingController();
  TextEditingController doctrl = TextEditingController();
  TextEditingController phonectrl = TextEditingController();
  TextEditingController emailctrl = TextEditingController();
  TextEditingController totalsubsctrl = TextEditingController();
  TextEditingController settlementctrl = TextEditingController();
  TextEditingController totalSubsIntctrl = TextEditingController();
  TextEditingController ltLoansDuectrl = TextEditingController();
  TextEditingController stLoansDuectrl = TextEditingController();
  TextEditingController totalLtLoansctrl = TextEditingController();
  TextEditingController totalStLoansctrl = TextEditingController();
  TextEditingController totalLtIntPaidctrl = TextEditingController();
  TextEditingController totalStIntPaidctrl = TextEditingController();
  TextEditingController totalDividentctrl = TextEditingController();
  TextEditingController totalSharesctrl = TextEditingController();

  final ScreenshotController screenshotController = ScreenshotController();

  String? selectedloan;
  bool isLoading = false;
  String? doOfficeName;

  bool scLoading = false;
  bool slLoading = false;

  loadData(HomeCtrl ctrl) async {
    phonectrl.text = ctrl.users?.phoneNo ?? "";
    emailctrl.text = ctrl.users?.email ?? "";
    totalsubsctrl.text = ctrl.users?.totalSubs.toString() ?? "";
    settlementctrl.text = ctrl.users?.settlement.toString() ?? "";
    totalSubsIntctrl.text = ctrl.users?.totalSubsInt.toString() ?? "";
    ltLoansDuectrl.text = ctrl.users?.ltLoansDue.toString() ?? "";
    stLoansDuectrl.text = ctrl.users?.stLoansDue.toString() ?? "";
    totalLtLoansctrl.text = ctrl.users?.totalLtLoans.toString() ?? "";
    totalStLoansctrl.text = ctrl.users?.totalStLoans.toString() ?? "";
    totalLtIntPaidctrl.text = ctrl.users?.totalLtIntPaid.toString() ?? "";
    totalStIntPaidctrl.text = ctrl.users?.totalStIntPaid.toString() ?? "";
    totalDividentctrl.text = ctrl.users?.totalDivident.toString() ?? "";
    totalSharesctrl.text = ctrl.users?.totalShares.toString() ?? "";
    doOfficeName = ctrl.districtoffice
        .firstWhereOrNull(
            (element) => element.docId == ctrl.users?.districtoffice)
        ?.name;
    doctrl.text = doOfficeName ?? "-";
  }

  @override
  void initState() {
    super.initState();
    setupFcm();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      loadData(ctrl);

      final ltactiveloans = ctrl.activeLoans
          .where((element) => element.loanType == LoanTypes.longTerm)
          .where((element) => element.rejectionDate == null)
          .where((element) => element.isSettled == false)
          .where((element) => element.isNew == false)
          .length;

      final stactiveloans = ctrl.activeLoans
          .where((element) => element.loanType == LoanTypes.emergencyLoan)
          .where((element) => element.rejectionDate == null)
          .where((element) => element.isSettled == false)
          .where((element) => element.isNew == false)
          .length;

      final sharevalue = ctrl.users?.totalShares ?? 0;

      ctrl.transactions.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      return ctrl.versionSupported
          ? Scaffold(
              appBar: AppBar(
                forceMaterialTransparency: true,
                surfaceTintColor: Colors.transparent,
                systemOverlayStyle: const SystemUiOverlayStyle(
                    statusBarColor: Colors.transparent),
                centerTitle: false,
                title: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 11),
                  child: Text(
                    "FCI SOCIETY",
                    textAlign: TextAlign.start,
                    style: GoogleFonts.sourceCodePro(
                        letterSpacing: -1,
                        color: const Color.fromRGBO(0, 166, 80, 1),
                        fontSize: 18.71.sp,
                        fontWeight: FontWeight.w700),
                  ),
                ),
                actions: [
                  ctrl.settings?.maintenanceString != null
                      ? Text("")
                      : Row(
                          children: [
                            IconButton(
                                splashColor: Colors.transparent,
                                highlightColor: Colors.transparent,
                                hoverColor: Colors.transparent,
                                focusColor: Colors.transparent,
                                color: const Color.fromRGBO(106, 106, 106, 1),
                                iconSize: 30.sp,
                                onPressed: () {
                                  context.push(Routes.notifications);
                                },
                                icon: const Icon(Icons.notifications_outlined)),
                            IconButton(
                                splashColor: Colors.transparent,
                                highlightColor: Colors.transparent,
                                hoverColor: Colors.transparent,
                                focusColor: Colors.transparent,
                                color: const Color.fromRGBO(106, 106, 106, 1),
                                iconSize: 30.sp,
                                onPressed: () {
                                  context.push(Routes.profile);
                                },
                                icon:
                                    const Icon(CupertinoIcons.profile_circled)),
                            SizedBox(width: 25.w),
                          ],
                        )
                ],
              ),
              body: ctrl.settings?.maintenanceString != null
                  ? Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: Center(
                          heightFactor: 35,
                          child: Text(
                            "${ctrl.settings?.maintenanceString?.toUpperCase()}",
                            style: TextStyle(fontSize: 15),
                          )),
                    )
                  : isLoading
                      ? CircularProgressIndicator()
                      : SingleChildScrollView(
                          // padding: EdgeInsets.only(
                          //     top: 20.h, right: 20.w, left: 20.w),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SingleChildScrollView(
                                padding: EdgeInsets.only(
                                    top: 20.h, right: 19.9.w, left: 19.9.w),
                                scrollDirection: Axis.horizontal,
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Container(
                                      padding:
                                          EdgeInsets.symmetric(vertical: 20.h),
                                      // height: 260.h,
                                      width: ctrl.activeLoans.isNotEmpty
                                          ? 370.w
                                          : 390.w,
                                      decoration: BoxDecoration(
                                          borderRadius: const BorderRadius.all(
                                              Radius.circular(16)),
                                          color: const Color.fromARGB(
                                              125, 188, 220, 204),
                                          border: Border.all(
                                              color: const Color.fromRGBO(
                                                  147, 207, 176, 1))),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Padding(
                                            padding: EdgeInsets.symmetric(
                                              horizontal: 25.w,
                                            ),
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                // SizedBox(height: 20.h),
                                                Text(
                                                  "CLOSING AMOUNT (SUBS)",
                                                  style:
                                                      GoogleFonts.sourceCodePro(
                                                          letterSpacing: -1.1,
                                                          wordSpacing: -3,
                                                          color: const Color
                                                              .fromRGBO(
                                                              96, 96, 96, 1),
                                                          fontSize: 16.sp,
                                                          fontWeight:
                                                              FontWeight.w600),
                                                ),
                                                Text(
                                                  // "₹${(ctrl.users?.stLoansDue ?? 0) + (ctrl.users?.ltLoansDue ?? 0)}",
                                                  "₹${ctrl.users?.totalSubs ?? 0}",
                                                  style: GoogleFonts.aBeeZee(
                                                      color:
                                                          const Color.fromRGBO(
                                                              39, 39, 39, 1),
                                                      fontWeight:
                                                          FontWeight.w700,
                                                      fontSize: 45.sp),
                                                ),
                                                SizedBox(height: 5.h)
                                              ],
                                            ),
                                          ),
                                          Divider(
                                              thickness: 0.5.sp,
                                              color: Colors.grey),
                                          Padding(
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 25, vertical: 10),
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                Row(
                                                  children: [
                                                    Icon(
                                                      color: Colors.grey,
                                                      size: 10.sp,
                                                      Icons.circle,
                                                    ),
                                                    SizedBox(width: 10.w),
                                                    Text(
                                                      "ACTIVE LOAN : ${stactiveloans + ltactiveloans}",
                                                      style: TextStyle(
                                                          color: Colors
                                                              .grey.shade700,
                                                          letterSpacing: -0.01,
                                                          fontWeight:
                                                              FontWeight.w600,
                                                          fontSize: 13.sp),
                                                    )
                                                  ],
                                                ),
                                                SizedBox(width: 10),
                                                Row(
                                                  children: [
                                                    Icon(
                                                      size: 10.sp,
                                                      Icons.circle,
                                                      color: Colors.green,
                                                    ),
                                                    SizedBox(width: 10.w),
                                                    Text(
                                                      "SHARE VALUE : ₹$sharevalue",
                                                      style: TextStyle(
                                                          color: Colors
                                                              .grey.shade700,
                                                          letterSpacing: -0.01,
                                                          fontWeight:
                                                              FontWeight.w600,
                                                          fontSize: 13.sp),
                                                    )
                                                  ],
                                                )
                                              ],
                                            ),
                                          ),
                                          Padding(
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 25),
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                Row(
                                                  children: [
                                                    Icon(
                                                      color: Colors.grey,
                                                      size: 10.sp,
                                                      Icons.circle,
                                                    ),
                                                    SizedBox(width: 10.w),
                                                    Text(
                                                      "DUES : ₹${ctrl.userMonthlyRecord.isNotEmpty ? (ctrl.userMonthlyRecord.first.dues?.ceil() ?? 0) + (ctrl.userMonthlyRecord.first.penalty?.ceil() ?? 0) : 0}",
                                                      style: TextStyle(
                                                          color: Colors
                                                              .grey.shade700,
                                                          letterSpacing: -0.01,
                                                          fontWeight:
                                                              FontWeight.w600,
                                                          fontSize: 13.sp),
                                                    )
                                                  ],
                                                ),
                                                SizedBox(width: 10),
                                                Row(
                                                  children: [
                                                    Icon(
                                                      size: 10.sp,
                                                      Icons.circle,
                                                      color: Colors.green,
                                                    ),
                                                    SizedBox(width: 10.w),
                                                    Text(
                                                      "PENALTY : ₹${ctrl.userMonthlyRecord.isNotEmpty ? (ctrl.userMonthlyRecord.first.penalty?.ceil() ?? 0) : 0}",
                                                      style: TextStyle(
                                                          color: Colors
                                                              .grey.shade700,
                                                          letterSpacing: -0.01,
                                                          fontWeight:
                                                              FontWeight.w600,
                                                          fontSize: 13.sp),
                                                    )
                                                  ],
                                                )
                                              ],
                                            ),
                                          )
                                        ],
                                      ),
                                    ),
                                    if (ltactiveloans > 0) ...[
                                      SizedBox(width: 10),
                                      Container(
                                        padding: EdgeInsets.symmetric(
                                            vertical: 20.h),
                                        // height: 260.h,
                                        width: 368.w,
                                        decoration: BoxDecoration(
                                            borderRadius:
                                                const BorderRadius.all(
                                                    Radius.circular(16)),
                                            color: const Color.fromARGB(
                                                125, 188, 220, 204),
                                            border: Border.all(
                                                color: const Color.fromRGBO(
                                                    147, 207, 176, 1))),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Padding(
                                              padding: EdgeInsets.symmetric(
                                                horizontal: 25.w,
                                              ),
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  // SizedBox(height: 20.h),
                                                  Text(
                                                    "CLOSING AMOUNT (LONGTERM)",
                                                    style: GoogleFonts
                                                        .sourceCodePro(
                                                            letterSpacing: -1.1,
                                                            wordSpacing: -3,
                                                            color: const Color
                                                                .fromRGBO(
                                                                96, 96, 96, 1),
                                                            fontSize: 16.sp,
                                                            fontWeight:
                                                                FontWeight
                                                                    .w600),
                                                  ),
                                                  Text(
                                                    "₹${ctrl.users?.ltLoansDue ?? 0}",
                                                    // "₹${(ctrl.users?.stLoansDue ?? 0) + (ctrl.users?.ltLoansDue ?? 0)}",
                                                    style: GoogleFonts.aBeeZee(
                                                        color: const Color
                                                            .fromRGBO(
                                                            39, 39, 39, 1),
                                                        fontWeight:
                                                            FontWeight.w700,
                                                        fontSize: 45.sp),
                                                  ),
                                                  SizedBox(height: 5.h)
                                                ],
                                              ),
                                            ),
                                            Divider(
                                                thickness: 0.5.sp,
                                                color: Colors.grey),
                                            Padding(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 25,
                                                      vertical: 10),
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  Row(
                                                    children: [
                                                      Icon(
                                                        color: Colors.grey,
                                                        size: 10.sp,
                                                        Icons.circle,
                                                      ),
                                                      SizedBox(width: 10.w),
                                                      Text(
                                                        "ACTIVE LOAN : $ltactiveloans",
                                                        style: TextStyle(
                                                            color: Colors
                                                                .grey.shade700,
                                                            letterSpacing:
                                                                -0.01,
                                                            fontWeight:
                                                                FontWeight.w600,
                                                            fontSize: 13.sp),
                                                      )
                                                    ],
                                                  ),
                                                  SizedBox(width: 12.w),
                                                  Row(
                                                    children: [
                                                      Icon(
                                                        size: 10.sp,
                                                        Icons.circle,
                                                        color: Colors.green,
                                                      ),
                                                      SizedBox(width: 10.w),
                                                      Text(
                                                        "SHARE VALUE : ₹$sharevalue",
                                                        style: TextStyle(
                                                            color: Colors
                                                                .grey.shade700,
                                                            letterSpacing:
                                                                -0.01,
                                                            fontWeight:
                                                                FontWeight.w600,
                                                            fontSize: 13.sp),
                                                      )
                                                    ],
                                                  )
                                                ],
                                              ),
                                            ),
                                            Padding(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 25),
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  Row(
                                                    children: [
                                                      Icon(
                                                        color: Colors.grey,
                                                        size: 10.sp,
                                                        Icons.circle,
                                                      ),
                                                      SizedBox(width: 10.w),
                                                      Text(
                                                        "DUES : ₹${ctrl.userMonthlyRecord.isNotEmpty ? (ctrl.userMonthlyRecord.first.dues?.ceil() ?? 0) + (ctrl.userMonthlyRecord.first.penalty?.ceil() ?? 0) : 0}",
                                                        style: TextStyle(
                                                            color: Colors
                                                                .grey.shade700,
                                                            letterSpacing:
                                                                -0.01,
                                                            fontWeight:
                                                                FontWeight.w600,
                                                            fontSize: 13.sp),
                                                      )
                                                    ],
                                                  ),
                                                  SizedBox(width: 80.w),
                                                  Row(
                                                    children: [
                                                      Icon(
                                                        size: 10.sp,
                                                        Icons.circle,
                                                        color: Colors.green,
                                                      ),
                                                      SizedBox(width: 10.w),
                                                      Text(
                                                        "PENALTY : ₹${ctrl.userMonthlyRecord.isNotEmpty ? (ctrl.userMonthlyRecord.first.penalty?.ceil() ?? 0) : 0}",
                                                        style: TextStyle(
                                                            color: Colors
                                                                .grey.shade700,
                                                            letterSpacing:
                                                                -0.01,
                                                            fontWeight:
                                                                FontWeight.w600,
                                                            fontSize: 13.sp),
                                                      )
                                                    ],
                                                  )
                                                ],
                                              ),
                                            )
                                          ],
                                        ),
                                      )
                                    ],
                                    if (stactiveloans > 0) ...[
                                      SizedBox(width: 10),
                                      Container(
                                        padding: EdgeInsets.symmetric(
                                            vertical: 20.h),
                                        // height: 260.h,
                                        width: 368.w,
                                        decoration: BoxDecoration(
                                            borderRadius:
                                                const BorderRadius.all(
                                                    Radius.circular(16)),
                                            color: const Color.fromARGB(
                                                125, 188, 220, 204),
                                            border: Border.all(
                                                color: const Color.fromRGBO(
                                                    147, 207, 176, 1))),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Padding(
                                              padding: EdgeInsets.symmetric(
                                                horizontal: 25.w,
                                              ),
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  // SizedBox(height: 20.h),
                                                  Text(
                                                    "CLOSING AMOUNT (SHORTTERM)",
                                                    style: GoogleFonts
                                                        .sourceCodePro(
                                                            letterSpacing: -1.1,
                                                            wordSpacing: -3,
                                                            color: const Color
                                                                .fromRGBO(
                                                                96, 96, 96, 1),
                                                            fontSize: 16.sp,
                                                            fontWeight:
                                                                FontWeight
                                                                    .w600),
                                                  ),
                                                  Text(
                                                    "₹${ctrl.users?.stLoansDue ?? 0}",
                                                    // "₹${(ctrl.users?.stLoansDue ?? 0) + (ctrl.users?.ltLoansDue ?? 0)}",
                                                    style: GoogleFonts.aBeeZee(
                                                        color: const Color
                                                            .fromRGBO(
                                                            39, 39, 39, 1),
                                                        fontWeight:
                                                            FontWeight.w700,
                                                        fontSize: 45.sp),
                                                  ),
                                                  SizedBox(height: 5.h)
                                                ],
                                              ),
                                            ),
                                            Divider(
                                                thickness: 0.5.sp,
                                                color: Colors.grey),
                                            Padding(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 25,
                                                      vertical: 10),
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  Row(
                                                    children: [
                                                      Icon(
                                                        color: Colors.grey,
                                                        size: 10.sp,
                                                        Icons.circle,
                                                      ),
                                                      SizedBox(width: 10.w),
                                                      Text(
                                                        "ACTIVE LOAN : $stactiveloans",
                                                        style: TextStyle(
                                                            color: Colors
                                                                .grey.shade700,
                                                            letterSpacing:
                                                                -0.01,
                                                            fontWeight:
                                                                FontWeight.w600,
                                                            fontSize: 13.sp),
                                                      )
                                                    ],
                                                  ),
                                                  SizedBox(width: 12.w),
                                                  Row(
                                                    children: [
                                                      Icon(
                                                        size: 10.sp,
                                                        Icons.circle,
                                                        color: Colors.green,
                                                      ),
                                                      SizedBox(width: 10.w),
                                                      Text(
                                                        "SHARE VALUE : ₹$sharevalue",
                                                        style: TextStyle(
                                                            color: Colors
                                                                .grey.shade700,
                                                            letterSpacing:
                                                                -0.01,
                                                            fontWeight:
                                                                FontWeight.w600,
                                                            fontSize: 13.sp),
                                                      )
                                                    ],
                                                  )
                                                ],
                                              ),
                                            ),
                                            Padding(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 25),
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  Row(
                                                    children: [
                                                      Icon(
                                                        color: Colors.grey,
                                                        size: 10.sp,
                                                        Icons.circle,
                                                      ),
                                                      SizedBox(width: 10.w),
                                                      Text(
                                                        "DUES : ₹${ctrl.userMonthlyRecord.isNotEmpty ? (ctrl.userMonthlyRecord.first.dues?.ceil() ?? 0) + (ctrl.userMonthlyRecord.first.penalty?.ceil() ?? 0) : 0}",
                                                        style: TextStyle(
                                                            color: Colors
                                                                .grey.shade700,
                                                            letterSpacing:
                                                                -0.01,
                                                            fontWeight:
                                                                FontWeight.w600,
                                                            fontSize: 13.sp),
                                                      )
                                                    ],
                                                  ),
                                                  SizedBox(width: 80.w),
                                                  Row(
                                                    children: [
                                                      Icon(
                                                        size: 10.sp,
                                                        Icons.circle,
                                                        color: Colors.green,
                                                      ),
                                                      SizedBox(width: 10.w),
                                                      Text(
                                                        "PENALTY : ₹${ctrl.userMonthlyRecord.isNotEmpty ? (ctrl.userMonthlyRecord.first.penalty?.ceil() ?? 0) : 0}",
                                                        style: TextStyle(
                                                            color: Colors
                                                                .grey.shade700,
                                                            letterSpacing:
                                                                -0.01,
                                                            fontWeight:
                                                                FontWeight.w600,
                                                            fontSize: 13.sp),
                                                      )
                                                    ],
                                                  )
                                                ],
                                              ),
                                            )
                                          ],
                                        ),
                                      )
                                    ]
                                  ],
                                ),
                              ),
                              Padding(
                                padding: EdgeInsets.only(
                                    top: 20.h, right: 20.w, left: 20.w),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Padding(
                                      padding: EdgeInsets.symmetric(
                                          vertical: 20.h, horizontal: 5.w),
                                      child: Text(
                                        "Loans Details",
                                        style: GoogleFonts.sourceCodePro(
                                            letterSpacing: -0.8,
                                            wordSpacing: -3,
                                            fontSize: 18.sp,
                                            fontWeight: FontWeight.w600),
                                      ),
                                    ),

                                    //LOAN CONTAINERS

                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceAround,
                                      children: [
                                        Expanded(
                                          child: CustomCont(
                                              inkwellOnTap: () {
                                                if (ctrl.users?.totalLtLoans ==
                                                    0) {
                                                  return;
                                                }
                                                context.push(Routes.loandetails,
                                                    extra: LoanTypes.longTerm);
                                              },
                                              amount:
                                                  ctrl.users?.ltLoansDue ?? 0,
                                              titletext: "Long term loan"),
                                        ),
                                        SizedBox(width: 10.w),
                                        Expanded(
                                          child: CustomCont(
                                              inkwellOnTap: () {
                                                if (ctrl.users?.totalStLoans ==
                                                    0) {
                                                  return;
                                                }
                                                context.push(Routes.loandetails,
                                                    extra: LoanTypes
                                                        .emergencyLoan);
                                              },
                                              amount:
                                                  ctrl.users?.stLoansDue ?? 0,
                                              titletext: "Short term loan"),
                                        )
                                      ],
                                    ),

                                    //APPLY FOR LOAN BUTTON

                                    Center(
                                      child: Padding(
                                          padding: EdgeInsets.symmetric(
                                              vertical:
                                                  ctrl.settings?.applyButton ==
                                                          true
                                                      ? 20
                                                      : 14,
                                              horizontal: 0),
                                          child: ctrl.settings?.applyButton ==
                                                  true
                                              ? ElevatedButton(
                                                  style: ButtonStyle(
                                                    elevation:
                                                        WidgetStatePropertyAll(
                                                            0),
                                                    backgroundColor:
                                                        WidgetStatePropertyAll(
                                                            Colors.transparent),
                                                    fixedSize:
                                                        WidgetStatePropertyAll(
                                                            Size(395, 52)),
                                                    shape: WidgetStatePropertyAll(
                                                        ContinuousRectangleBorder(
                                                            side: BorderSide(
                                                                color: const Color
                                                                    .fromRGBO(
                                                                    147,
                                                                    207,
                                                                    176,
                                                                    1)),
                                                            borderRadius:
                                                                BorderRadius.all(
                                                                    Radius.circular(
                                                                        6.r)))),
                                                    // backgroundColor:
                                                    //     const WidgetStatePropertyAll(
                                                    //         Color.fromRGBO(29,
                                                    //             167, 95, 1))
                                                  ),
                                                  onPressed: () {
                                                    context.push(
                                                        Routes.loanselection);
                                                  },
                                                  child: Text(
                                                    "Apply for Loan Now",
                                                    style: TextStyle(
                                                        color: Color.fromRGBO(
                                                            29, 167, 95, 1),
                                                        fontSize: 16.sp,
                                                        fontWeight:
                                                            FontWeight.w500),
                                                  ))
                                              : SizedBox.shrink()),
                                    ),

                                    //buttons

                                    Padding(
                                      padding: EdgeInsets.only(bottom: 20.h),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          slLoading
                                              ? Expanded(
                                                  child: Center(
                                                      child:
                                                          CircularProgressIndicator(
                                                    color: Colors.black,
                                                  )),
                                                )
                                              : Expanded(
                                                  child: Center(
                                                    //subsidiary ledger
                                                    child: ElevatedButton(
                                                      style: ButtonStyle(
                                                        elevation:
                                                            WidgetStatePropertyAll(
                                                                0),
                                                        padding:
                                                            WidgetStatePropertyAll(
                                                                EdgeInsets
                                                                    .symmetric(
                                                                        vertical:
                                                                            17)),
                                                        shape:
                                                            WidgetStatePropertyAll(
                                                          ContinuousRectangleBorder(
                                                            side: BorderSide(
                                                                color: const Color
                                                                    .fromRGBO(
                                                                    147,
                                                                    207,
                                                                    176,
                                                                    1)),
                                                            borderRadius:
                                                                BorderRadius
                                                                    .all(Radius
                                                                        .circular(
                                                                            6.r)),
                                                          ),
                                                        ),
                                                        backgroundColor:
                                                            WidgetStatePropertyAll(
                                                                Colors
                                                                    .transparent),
                                                      ),
                                                      onPressed: () async {
                                                        setState(() =>
                                                            slLoading = true);

                                                        final ledger = ctrl
                                                            .subsidiaryLedger;

                                                        if (ledger == null) {
                                                          showAppSnackBar(
                                                              "Subsidiary Ledger Report does not exist right now!");
                                                          setState(() =>
                                                              slLoading =
                                                                  false);
                                                          return;
                                                        }

                                                        try {
                                                          final image =
                                                              await screenshotController
                                                                  .captureFromWidget(
                                                            MediaQuery(
                                                              data:
                                                                  MediaQuery.of(
                                                                      context),
                                                              child:
                                                                  MaterialApp(
                                                                debugShowCheckedModeBanner:
                                                                    false,
                                                                home: Scaffold(
                                                                  body:
                                                                      Container(
                                                                    width: 1080,
                                                                    height:
                                                                        1200,
                                                                    padding:
                                                                        EdgeInsets.all(
                                                                            16),
                                                                    child: SubsidiaryLedgerPreviewWidget(
                                                                        ctrl:
                                                                            ctrl),
                                                                  ),
                                                                ),
                                                              ),
                                                            ),
                                                            pixelRatio: 2.0,
                                                          );

                                                          //inside subsidiary ledger dialog

                                                          showDialog(
                                                              barrierDismissible:
                                                                  false,
                                                              context: context,
                                                              builder: (_) =>
                                                                  StatefulBuilder(
                                                                      builder:
                                                                          (context,
                                                                              setState2) {
                                                                    return Dialog(
                                                                        insetPadding: EdgeInsets.symmetric(
                                                                            horizontal:
                                                                                20,
                                                                            vertical:
                                                                                40),
                                                                        shape:
                                                                            RoundedRectangleBorder(
                                                                          borderRadius:
                                                                              BorderRadius.circular(16),
                                                                        ),
                                                                        child: Container(
                                                                            // padding:
                                                                            //     EdgeInsets.all(0),
                                                                            decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(16)),
                                                                            child: SingleChildScrollView(
                                                                              child: Column(mainAxisSize: MainAxisSize.min, children: [
                                                                                Row(
                                                                                  mainAxisAlignment: MainAxisAlignment.end,
                                                                                  children: [
                                                                                    IconButton(
                                                                                      icon: const Icon(Icons.close, color: Colors.black),
                                                                                      onPressed: () => Navigator.of(context).pop(),
                                                                                    ),
                                                                                  ],
                                                                                ),
                                                                                SubsidiaryLedgerPreviewWidget(ctrl: ctrl),
                                                                                slLoading
                                                                                    ? Center(
                                                                                        child: CircularProgressIndicator(
                                                                                        color: Colors.black,
                                                                                      ))
                                                                                    : Padding(
                                                                                        padding: const EdgeInsets.only(bottom: 18),
                                                                                        child: ElevatedButton(
                                                                                          style: ButtonStyle(
                                                                                            fixedSize: WidgetStatePropertyAll(Size(200, 40)),
                                                                                            shape: WidgetStatePropertyAll(
                                                                                              ContinuousRectangleBorder(
                                                                                                borderRadius: BorderRadius.all(Radius.circular(16)),
                                                                                              ),
                                                                                            ),
                                                                                            backgroundColor: WidgetStatePropertyAll(Color.fromRGBO(29, 167, 95, 1)),
                                                                                          ),
                                                                                          onPressed: () async {
                                                                                            final userMonthlyRecords = ctrl.userMonthlyRecord.toList();
                                                                                            final totals = calculateMonthlyTotals(userMonthlyRecords);

                                                                                            // Generate dynamic balance date based on last month with data
                                                                                            String getBalanceAsOnDate() {
                                                                                              if (userMonthlyRecords.isEmpty) {
                                                                                                return 'BALANCE AS ON 31-3-${DateTime.now().year}';
                                                                                              }

                                                                                              // Find the last month with data
                                                                                              final lastRecord = userMonthlyRecords.last;
                                                                                              final lastMonth = lastRecord.selectedmonth ?? 3;
                                                                                              final year = lastRecord.selectedyear ?? DateTime.now().year;

                                                                                              // Get last day of the month
                                                                                              final lastDayOfMonth = DateTime(year, lastMonth + 1, 0).day;

                                                                                              return 'BALANCE AS ON $lastDayOfMonth-$lastMonth-$year';
                                                                                            }

                                                                                            UserMonthlyRecordModel aprilRecord = userMonthlyRecords.firstWhere(
                                                                                              (record) => record.selectedmonth == 4 && record.selectedyear == DateTime.now().year,
                                                                                              orElse: () => UserMonthlyRecordModel(
                                                                                                docId: '',
                                                                                                selectedyear: DateTime.now().year,
                                                                                                selectedmonth: 3,
                                                                                                cpfNo: 0,
                                                                                                name: '',
                                                                                                districtoffice: '',
                                                                                                obLt: 0,
                                                                                                obSt: 0,
                                                                                                loanPaidLt: 0,
                                                                                                loanPaidst: 0,
                                                                                                loanTotal: 0,
                                                                                                subs: 0,
                                                                                                ltInstallment: 0,
                                                                                                stInstallment: 0,
                                                                                                interest: 0,
                                                                                                total: 0,
                                                                                                installmentRec: 0,
                                                                                                installmentRecDate: null,
                                                                                                ltCb: 0,
                                                                                                stCb: 0,
                                                                                                subscriptionPaid: 0,
                                                                                                longTermInstalmentPaid: 0,
                                                                                                shortTermInstalmentPaid: 0,
                                                                                                longTermInterestPaid: 0,
                                                                                                shortTermInterestPaid: 0,
                                                                                                isPaid: false,
                                                                                                status: '',
                                                                                                dues: 0,
                                                                                                penalty: 0,
                                                                                                shareValue: 0,
                                                                                                societySubsPayout: 0,
                                                                                                societySharesPayout: 0,
                                                                                                penaltyPaid: 0,
                                                                                              ),
                                                                                            );

                                                                                            final obSubs = ctrl.users?.obSubs ?? 0;
                                                                                            final obShare = ctrl.users?.obShares ?? 0;
                                                                                            final obLtLoan = aprilRecord.obLt ?? 0;
                                                                                            final obStLoan = aprilRecord.obSt ?? 0;

                                                                                            final recShares = (ctrl.users?.totalShares ?? 0) - obShare;

                                                                                            final pdf = pw.Document();
                                                                                            final dateFormatter = DateFormat('dd-MM-yyyy');

                                                                                            pdf.addPage(
                                                                                              pw.MultiPage(
                                                                                                pageFormat: PdfPageFormat.a4.landscape,
                                                                                                build: (context) {
                                                                                                  return [
                                                                                                    pw.Container(
                                                                                                      padding: const pw.EdgeInsets.all(12),
                                                                                                      child: pw.Column(
                                                                                                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                                                                                                        children: [
                                                                                                          // Centered Header Lines
                                                                                                          pw.Center(
                                                                                                            child: pw.Text(
                                                                                                              'FCI EMPLOYEES CO OPERATIVE CREDIT SOCIETY : BARODA',
                                                                                                              style: pw.TextStyle(
                                                                                                                fontWeight: pw.FontWeight.bold,
                                                                                                                fontSize: 14,
                                                                                                              ),
                                                                                                            ),
                                                                                                          ),
                                                                                                          pw.Center(
                                                                                                            child: pw.Text(
                                                                                                              'MEMBER SUBSIDIARY LEDGER FOR THE FINANCIAL YEAR 2024-25',
                                                                                                              style: pw.TextStyle(
                                                                                                                fontWeight: pw.FontWeight.bold,
                                                                                                                fontSize: 11,
                                                                                                              ),
                                                                                                            ),
                                                                                                          ),
                                                                                                          pw.SizedBox(height: 8),

                                                                                                          // Name of Society Member - Single Row, Centered
                                                                                                          pw.Center(
                                                                                                            child: pw.Text(
                                                                                                              'NAME OF SOCIETY MEMBER : ${ctrl.users?.name}',
                                                                                                              style: pw.TextStyle(
                                                                                                                fontWeight: pw.FontWeight.bold,
                                                                                                                fontSize: 13,
                                                                                                                color: PdfColors.black,
                                                                                                              ),
                                                                                                            ),
                                                                                                          ),

                                                                                                          pw.SizedBox(height: 12),

                                                                                                          // OBs Container Box centered horizontally
                                                                                                          pw.Align(
                                                                                                            alignment: pw.Alignment.center,
                                                                                                            child: pw.Container(
                                                                                                              width: 200, // Fixed width box for OBs section
                                                                                                              decoration: pw.BoxDecoration(
                                                                                                                border: pw.Border.all(color: PdfColors.grey),
                                                                                                              ),
                                                                                                              padding: const pw.EdgeInsets.symmetric(vertical: 8, horizontal: 8),
                                                                                                              child: pw.Column(
                                                                                                                children: [
                                                                                                                  pw.Row(
                                                                                                                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                                                                                                    children: [
                                                                                                                      pw.Text('OB SUBS :', style: pw.TextStyle(fontSize: 10)),
                                                                                                                      pw.Text(obSubs.toString(), style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 10)),
                                                                                                                    ],
                                                                                                                  ),
                                                                                                                  pw.SizedBox(height: 4),
                                                                                                                  pw.Row(
                                                                                                                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                                                                                                    children: [
                                                                                                                      pw.Text('OB SHARES :', style: pw.TextStyle(fontSize: 10)),
                                                                                                                      pw.Text(obShare.toString(), style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 10)),
                                                                                                                    ],
                                                                                                                  ),
                                                                                                                  pw.SizedBox(height: 4),
                                                                                                                  pw.Row(
                                                                                                                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                                                                                                    children: [
                                                                                                                      pw.Text('OBLT :', style: pw.TextStyle(fontSize: 10)),
                                                                                                                      pw.Text(obLtLoan.toString(), style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 10)),
                                                                                                                    ],
                                                                                                                  ),
                                                                                                                  pw.SizedBox(height: 4),
                                                                                                                  pw.Row(
                                                                                                                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                                                                                                    children: [
                                                                                                                      pw.Text('OBST :', style: pw.TextStyle(fontSize: 10)),
                                                                                                                      pw.Text(obStLoan.toString(), style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 10)),
                                                                                                                    ],
                                                                                                                  ),
                                                                                                                ],
                                                                                                              ),
                                                                                                            ),
                                                                                                          ),

                                                                                                          pw.SizedBox(height: 15),

                                                                                                          // Ledger Table (with all columns & totals)
                                                                                                          pw.Table.fromTextArray(
                                                                                                            headers: [
                                                                                                              'MONTH',
                                                                                                              'DATE',
                                                                                                              'OBLT',
                                                                                                              'OBST',
                                                                                                              'LOAN PAID LT',
                                                                                                              'LOAN PAID ST',
                                                                                                              'SUBSCRIPTION',
                                                                                                              'LT INSTALLMENT',
                                                                                                              'ST INSTALLMENT',
                                                                                                              'INTEREST',
                                                                                                              'TOTAL AMT PAID',
                                                                                                              'LTCB',
                                                                                                              'STCB'
                                                                                                            ],
                                                                                                            data: [
                                                                                                              for (final record in userMonthlyRecords)
                                                                                                                [
                                                                                                                  getMonthName(record.selectedmonth ?? 0),
                                                                                                                  record.installmentRecDate != null ? dateFormatter.format(record.installmentRecDate!) : '-',
                                                                                                                  (record.obLt ?? 0).toString(),
                                                                                                                  (record.obSt ?? 0).toString(),
                                                                                                                  (record.loanPaidLt ?? 0).toString(),
                                                                                                                  (record.loanPaidst ?? 0).toString(),
                                                                                                                  (record.subscriptionPaid ?? 0).toString(),
                                                                                                                  (record.longTermInstalmentPaid ?? 0).toString(),
                                                                                                                  (record.shortTermInstalmentPaid ?? 0).toString(),
                                                                                                                  (record.interest ?? 0).toString(),
                                                                                                                  (record.installmentRec ?? 0).toString(),
                                                                                                                  (record.ltCb ?? 0).toString(),
                                                                                                                  (record.stCb ?? 0).toString(),
                                                                                                                ],
                                                                                                              [
                                                                                                                'TOTAL',
                                                                                                                '',
                                                                                                                '',
                                                                                                                '',
                                                                                                                totals['loanPaidLtTotal'].toString(),
                                                                                                                totals['loanPaidStTotal'].toString(),
                                                                                                                totals['subscriptionTotal'].toString(),
                                                                                                                totals['ltInstallmentTotal'].toString(),
                                                                                                                totals['stInstallmentTotal'].toString(),
                                                                                                                totals['interestTotal'].toString(),
                                                                                                                totals['totalAmtPaidTotal'].toString(),
                                                                                                                '',
                                                                                                                '',
                                                                                                              ]
                                                                                                            ],
                                                                                                            cellAlignment: pw.Alignment.center,
                                                                                                            headerStyle: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 8),
                                                                                                            cellStyle: pw.TextStyle(fontSize: 8),
                                                                                                            border: pw.TableBorder.all(color: PdfColors.grey300),
                                                                                                          ),

                                                                                                          pw.SizedBox(height: 15),

                                                                                                          // Summary Table - Ensure it fits on PDF page
                                                                                                          pw.Container(
                                                                                                            width: double.infinity,
                                                                                                            child: pw.Column(children: [
                                                                                                              pw.Text('SUMMARY', style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 10)),
                                                                                                              pw.Table.fromTextArray(
                                                                                                                headers: [
                                                                                                                  'PARTICULAR',
                                                                                                                  'OB',
                                                                                                                  'RECEIVED DURING THE YEAR',
                                                                                                                  'PAID DURING THE YEAR',
                                                                                                                  getBalanceAsOnDate()
                                                                                                                ],
                                                                                                                data: [
                                                                                                                  [
                                                                                                                    'SUBSCRIPTION',
                                                                                                                    obSubs.toString(),
                                                                                                                    totals['subscriptionTotal'].toString(),
                                                                                                                    '0',
                                                                                                                    (obSubs + (num.tryParse(totals['subscriptionTotal'].toString() ?? "0") ?? 0))
                                                                                                                  ],
                                                                                                                  [
                                                                                                                    'SHARE',
                                                                                                                    obShare.toString(),
                                                                                                                    recShares.toString(),
                                                                                                                    '0',
                                                                                                                    (obShare + recShares).toString()
                                                                                                                  ],
                                                                                                                  [
                                                                                                                    'LT LOAN',
                                                                                                                    obLtLoan.toString(),
                                                                                                                    totals['ltInstallmentTotal'].toString(),
                                                                                                                    totals['loanPaidLtTotal'].toString(),
                                                                                                                    (obLtLoan - (num.tryParse(totals['ltInstallmentTotal'].toString() ?? "0") ?? 0) + (num.tryParse(totals['loanPaidLtTotal'].toString() ?? "0") ?? 0) ?? 0)
                                                                                                                  ],
                                                                                                                  [
                                                                                                                    'ST LOAN',
                                                                                                                    obStLoan.toString(),
                                                                                                                    totals['stInstallmentTotal'].toString(),
                                                                                                                    totals['loanPaidStTotal'].toString(),
                                                                                                                    (obStLoan - (num.tryParse(totals['stInstallmentTotal'].toString() ?? "0") ?? 0) + (num.tryParse(totals['loanPaidStTotal'].toString() ?? "0") ?? 0) ?? 0)
                                                                                                                  ],
                                                                                                                ],
                                                                                                                cellAlignment: pw.Alignment.center,
                                                                                                                headerStyle: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 8),
                                                                                                                cellStyle: pw.TextStyle(fontSize: 8),
                                                                                                                border: pw.TableBorder.all(color: PdfColors.grey300),
                                                                                                              ),
                                                                                                            ]),
                                                                                                          ),

                                                                                                          // pw.SizedBox(height: 20),

                                                                                                          // Footer Right Aligned
                                                                                                          // pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
                                                                                                          //   pw.Column(crossAxisAlignment: pw.CrossAxisAlignment.end, children: [
                                                                                                          //     pw.Text('RAKESH KUMAR', style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 10)),
                                                                                                          //     pw.Text('Treasurer : FCI Emp. Co. Op. Credit society : BARODA', style: pw.TextStyle(fontSize: 9))
                                                                                                          //   ])
                                                                                                          // ])
                                                                                                        ],
                                                                                                      ),
                                                                                                    )
                                                                                                  ];
                                                                                                },
                                                                                              ),
                                                                                            );

                                                                                            final bytes = await pdf.save();

                                                                                            if (kIsWeb) {
                                                                                              await Printing.sharePdf(bytes: bytes, filename: 'subsidiary_ledger_${DateTime.now().year}}.pdf');
                                                                                            } else {
                                                                                              final dir = io.Platform.isIOS ? await getLibraryDirectory() : await getExternalStorageDirectory();
                                                                                              final filePath = '${dir!.path}/subsidiary_ledger_${DateTime.now().year}.pdf';
                                                                                              final file = io.File(filePath);
                                                                                              await file.writeAsBytes(bytes);

                                                                                              final xFile = XFile(filePath, name: 'subsidiary_ledger_${DateTime.now().year}.pdf');
                                                                                              await Share.shareXFiles([xFile]);
                                                                                            }
                                                                                          },
                                                                                          child: Text(
                                                                                            "Download",
                                                                                            style: TextStyle(color: Colors.white),
                                                                                          ),
                                                                                        ))
                                                                              ]),
                                                                            )));
                                                                  }));
                                                        } catch (e) {
                                                          showAppSnackBar(
                                                              "Error generating preview: $e");
                                                        }
                                                        setState(() =>
                                                            slLoading = false);
                                                      },
                                                      child: Center(
                                                        child: Text(
                                                          "Subsidiary Ledger",
                                                          style: TextStyle(
                                                              color: Color
                                                                  .fromRGBO(
                                                                      29,
                                                                      167,
                                                                      95,
                                                                      1),
                                                              fontSize: 15.sp,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w500),
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                          SizedBox(width: 10.w),
                                          scLoading
                                              ? Expanded(
                                                  child: Center(
                                                      child:
                                                          CircularProgressIndicator(
                                                    color: Colors.black,
                                                  )),
                                                )
                                              : Expanded(
                                                  child: Center(
                                                    child: ElevatedButton(
                                                        style: ButtonStyle(
                                                            elevation:
                                                                WidgetStatePropertyAll(
                                                                    0),
                                                            padding: WidgetStatePropertyAll(
                                                                EdgeInsets.symmetric(
                                                                    vertical:
                                                                        17)),
                                                            // fixedSize:
                                                            //     WidgetStatePropertyAll(
                                                            //         Size(190, 48)),
                                                            shape: WidgetStatePropertyAll(ContinuousRectangleBorder(
                                                                side: BorderSide(
                                                                    color:
                                                                        const Color.fromRGBO(
                                                                            147,
                                                                            207,
                                                                            176,
                                                                            1)),
                                                                borderRadius: BorderRadius.all(
                                                                    Radius.circular(
                                                                        6.r)))),
                                                            backgroundColor:
                                                                WidgetStatePropertyAll(Colors.transparent)),
                                                        onPressed: () async {
                                                          setState(() =>
                                                              scLoading = true);

                                                          final image =
                                                              await screenshotController
                                                                  .captureFromWidget(
                                                            ShareCertificate(
                                                              ctrl: ctrl,
                                                              share: (ctrl.users
                                                                          ?.totalShares ??
                                                                      0)
                                                                  .toDouble(),
                                                              sctrl:
                                                                  screenshotController,
                                                            ),
                                                            pixelRatio: 3.0,
                                                          );

                                                          showDialog(
                                                              barrierDismissible:
                                                                  false,
                                                              context: context,
                                                              builder: (context) =>
                                                                  StatefulBuilder(
                                                                      builder:
                                                                          (context,
                                                                              setState2) {
                                                                    return Dialog(
                                                                        insetPadding: EdgeInsets.symmetric(
                                                                            horizontal: 20
                                                                                .w,
                                                                            vertical: 40
                                                                                .h),
                                                                        shape: RoundedRectangleBorder(
                                                                            borderRadius:
                                                                                BorderRadius.circular(16)),
                                                                        child: Container(
                                                                            padding: EdgeInsets.all(16),
                                                                            decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(16)),
                                                                            child: Column(mainAxisSize: MainAxisSize.min, children: [
                                                                              Stack(children: [
                                                                                ClipRRect(borderRadius: BorderRadius.circular(16), child: Image.memory(image, fit: BoxFit.contain)),
                                                                                Positioned(
                                                                                    right: 0,
                                                                                    top: -14,
                                                                                    child: IconButton(
                                                                                      icon: const Icon(Icons.close, color: Colors.black),
                                                                                      onPressed: () => Navigator.of(context).pop(),
                                                                                    ))
                                                                              ]),
                                                                              Padding(
                                                                                  padding: const EdgeInsets.symmetric(vertical: 12),
                                                                                  child: scLoading
                                                                                      ? SizedBox(
                                                                                          height: 20,
                                                                                          width: 20,
                                                                                          child: CircularProgressIndicator(
                                                                                            color: Colors.black,
                                                                                          ))
                                                                                      : ElevatedButton(
                                                                                          style: ButtonStyle(fixedSize: WidgetStatePropertyAll(Size(200.w, 40.h)), shape: const WidgetStatePropertyAll(ContinuousRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(16)))), backgroundColor: const WidgetStatePropertyAll(Color.fromRGBO(29, 167, 95, 1))),
                                                                                          onPressed: scLoading
                                                                                              ? null
                                                                                              : () async {
                                                                                                  setState2(() {
                                                                                                    scLoading = true;
                                                                                                  });
                                                                                                  await shareCertificateOnPressed(ctrl, (ctrl.users?.totalShares ?? 0).toDouble(), screenshotController, context, setState2);

                                                                                                  Navigator.of(context).pop();

                                                                                                  setState2(() {
                                                                                                    scLoading = false;
                                                                                                  });
                                                                                                },
                                                                                          child: Text(
                                                                                            "Download",
                                                                                            style: TextStyle(color: Colors.white),
                                                                                          )))
                                                                            ])));
                                                                  }));
                                                          setState(() =>
                                                              scLoading =
                                                                  false);
                                                        },
                                                        child: Center(
                                                          child: Text(
                                                            "Share Certificate",
                                                            style: TextStyle(
                                                                color: Color
                                                                    .fromRGBO(
                                                                        29,
                                                                        167,
                                                                        95,
                                                                        1),
                                                                fontSize: 15.sp,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w500),
                                                          ),
                                                        )),
                                                  ),
                                                ),
                                        ],
                                      ),
                                    ),

                                    //TRANSACTIONS

                                    Padding(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: 10.w, vertical: 8.h),
                                      child: Text(
                                        "Transaction",
                                        style: GoogleFonts.sourceCodePro(
                                            letterSpacing: -0.9,
                                            fontSize: 18.sp,
                                            fontWeight: FontWeight.w600),
                                      ),
                                    ),

                                    //TRANSACTIONS LIST

                                    ...List.generate(ctrl.transactions.length,
                                        (index) {
                                      return Column(
                                        children: [
                                          ListTile(
                                            contentPadding:
                                                EdgeInsets.symmetric(
                                                    vertical: 10,
                                                    horizontal: 10),
                                            trailing: Text(
                                              ctrl.transactions[index].amount
                                                  .ceil()
                                                  .toString(),
                                              style: TextStyle(
                                                  fontSize: 18,
                                                  fontWeight: FontWeight.w500),
                                            ),
                                            subtitle: Text(
                                              DateFormat("dd-MM-yyyy - hh:mm a")
                                                  .format(ctrl
                                                      .transactions[index]
                                                      .createdAt),
                                              style: const TextStyle(
                                                  fontSize: 12,
                                                  fontWeight: FontWeight.w400),
                                            ),
                                            title: Text(
                                              ctrl.transactions[index].title,
                                              style: TextStyle(
                                                  fontSize: 16,
                                                  fontWeight: FontWeight.w500),
                                            ),
                                            leading: ctrl.transactions[index]
                                                        .inn ==
                                                    false
                                                ? Icon(
                                                    color: const Color.fromARGB(
                                                        255, 230, 122, 114),
                                                    CupertinoIcons
                                                        .arrow_up_right)
                                                : Icon(
                                                    color: const Color.fromARGB(
                                                        255, 164, 230, 114),
                                                    CupertinoIcons
                                                        .arrow_down_right),
                                            //  Image(
                                            //     image: AssetImage(
                                            //         "assets/images/image.png")),
                                          ),
                                          Divider(
                                            color: Colors.grey.shade300,
                                            thickness: 1,
                                          ),
                                        ],
                                      );
                                    }),
                                  ],
                                ),
                              )
                            ],
                          ),
                        ),
            )
          : UpdateVersionPage();
    });
  }

  Future<void> shareCertificateOnPressed(
    HomeCtrl ctrl,
    double share,
    ScreenshotController sctrl,
    BuildContext context,
    void Function(void Function()) setState,
  ) async {
    try {
      setState(() => scLoading = true);

      // Capture the widget as an image
      final Uint8List uint8List = await sctrl.captureFromWidget(
        ShareCertificate(
          ctrl: ctrl,
          share: share,
          sctrl: sctrl,
        ),
        pixelRatio: 3.0,
      );

      // Generate PDF from the image
      final pdf = pw.Document();
      pdf.addPage(
        pw.Page(
          orientation: pw.PageOrientation.landscape,
          pageFormat: PdfPageFormat.a4,
          build: (pw.Context context) =>
              pw.Center(child: pw.Image(pw.MemoryImage(uint8List))),
        ),
      );

      final pdfBytes = await pdf.save();

      if (kIsWeb) {
        // ✅ Share on Web
        await Printing.sharePdf(
          bytes: pdfBytes,
          filename: 'share_certificate.pdf',
        );
      } else {
        // ✅ Share on Android/iOS
        final tempDir = await getTemporaryDirectory();
        final filePath = '${tempDir.path}/share_certificate.pdf';
        final file = io.File(filePath);
        await file.writeAsBytes(pdfBytes);

        final xFile = XFile(
          file.path,
          mimeType: 'application/pdf',
          name: 'share_certificate.pdf',
        );

        await Share.shareXFiles([xFile]);
      }
    } catch (e) {
      debugPrint('Error generating share certificate: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("Failed to generate PDF: $e")),
        );
      }
    } finally {
      setState(() => scLoading = false); // ✅ Hide loader
    }
  }

  setupFcm() async {
    try {
      final ctrl = Get.find<HomeCtrl>();
      FirebaseMessaging messaging = FirebaseMessaging.instance;

      // Request notification permission
      await messaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      // Handle foreground messages
      FirebaseMessaging.onMessage.listen((RemoteMessage message) {
        RemoteNotification? notification = message.notification;

        if (notification != null) {
          if (!kIsWeb) {
            // Native Android/iOS handling
            flutterLocalNotificationsPlugin.show(
              notification.hashCode,
              notification.title,
              notification.body,
              const NotificationDetails(
                android: androidPlatformChannelSpecifics,
                iOS: iOSPlatformChannelSpecifics,
              ),
            );
          } else {
            // Web: Show snackbar
            Get.snackbar(
              notification.title ?? 'Notification',
              notification.body ?? '',
              snackPosition: SnackPosition.TOP,
            );
          }
        }
      });

      // Unsubscribe from old topics (one-time)
      unSubScribeFunction();

      // Subscribe to global topic
      if (!kIsWeb) await messaging.subscribeToTopic('global');

      List<String> topics = [];

      if (ctrl.users != null && ctrl.users?.districtoffice != null) {
        final topic = ctrl.users!.districtoffice.substring(0, 6);

        if (!kIsWeb) await messaging.subscribeToTopic(topic);

        topics.add(topic);
        debugPrint('Subscribed topics: $topics');

        await FBFireStore.users.doc(ctrl.users?.docId).update({
          'topics': topics,
        });
      }

      // Notification tap from background
      FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage event) {
        // Handle notification tap navigation here
        // Example: Get.to(() => const NotiesPage());
      });
    } catch (e) {
      debugPrint("setupFcm error: $e");
    }
  }

  unSubScribeFunction() async {
    if (secondTime) return;
    secondTime = true;

    final ctrl = Get.find<HomeCtrl>();
    final toUnsubscribeTopics = ctrl.topics;

    if (!kIsWeb) {
      for (var topic in toUnsubscribeTopics) {
        await FirebaseMessaging.instance.unsubscribeFromTopic(topic);
        await Future.delayed(const Duration(milliseconds: 20));
      }
    }
  }
}
// if (ctrl.banners.isNotEmpty) ...[
              //   // SizedBox(height: 16),
              //   SizedBox(
              //     width: double.maxFinite,
              //     height: 120,
              //     child: CarouselSlider(
              //       options: CarouselOptions(
              //         viewportFraction: 1,
              //         enlargeCenterPage: true,
              //         autoPlay: false,
              //         enableInfiniteScroll: true,
              //         autoPlayInterval: const Duration(seconds: 5),
              //       ),
              //       items: ctrl.banners.map((ad) {
              //         // print(ad.imageUrl);
              //         return Padding(
              //           padding: EdgeInsets.symmetric(horizontal: 0.w),
              //           child: ClipRRect(
              //               borderRadius: BorderRadius.circular(26.r),
              //               child: FCIBanner(adBanner: ad)),
              //         );
              //       }).toList(),
              //     ),
              //   ),
              //   SizedBox(height: 16),
              // ],