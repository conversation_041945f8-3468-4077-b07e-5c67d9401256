import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:foodcorp/controller/homectrl.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

class MoreLoanDetails extends StatefulWidget {
  const MoreLoanDetails({super.key, required this.loanId});
  // final String? loanType;
  final String? loanId;

  @override
  State<MoreLoanDetails> createState() => _MoreLoanDetailsState();
}

class _MoreLoanDetailsState extends State<MoreLoanDetails> {
  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      final loan =
          ctrl.activeLoans.where((element) => element.docId == widget.loanId);

      // print("Loan : $loan");

      return Scaffold(
        appBar: AppBar(
          title: Text(loan.first.loanType),
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(14),
          child: Column(
            children: [
              Container(
                height: MediaQuery.sizeOf(context).height - 180,
                decoration: BoxDecoration(
                    borderRadius: const BorderRadius.all(Radius.circular(16)),
                    color: const Color.fromARGB(125, 188, 220, 204),
                    border: Border.all(
                        color: const Color.fromRGBO(147, 207, 176, 1))),
                width: MediaQuery.sizeOf(context).width,
                child: Padding(
                  padding: const EdgeInsets.all(10),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text("Loan Application No.",
                              style: TextStyle(
                                fontWeight: FontWeight.w400,
                                fontSize: 12.sp,
                              )),
                          Text("Applied On",
                              style: TextStyle(
                                fontWeight: FontWeight.w400,
                                fontSize: 12.sp,
                              )),
                          Text("Approved On",
                              style: TextStyle(
                                fontWeight: FontWeight.w400,
                                fontSize: 12.sp,
                              )),
                          Text("Applied Loan Amount",
                              style: TextStyle(
                                fontWeight: FontWeight.w400,
                                fontSize: 12.sp,
                              )),
                          Text("Applied Loan Amount in words",
                              style: TextStyle(
                                fontWeight: FontWeight.w400,
                                fontSize: 12.sp,
                              )),
                          Text("Monthly Installment Amount",
                              style: TextStyle(
                                fontWeight: FontWeight.w400,
                                fontSize: 12.sp,
                              )),
                          Text("Total Loan Paid",
                              style: TextStyle(
                                fontWeight: FontWeight.w400,
                                fontSize: 12.sp,
                              )),
                          Text("Total Loan Due",
                              style: TextStyle(
                                fontWeight: FontWeight.w400,
                                fontSize: 12.sp,
                              )),
                          Text("Total Interest Paid",
                              style: TextStyle(
                                fontWeight: FontWeight.w400,
                                fontSize: 12.sp,
                              )),
                          Text("Share",
                              style: TextStyle(
                                fontWeight: FontWeight.w400,
                                fontSize: 12.sp,
                              )),
                          Text("Loan Reason",
                              style: TextStyle(
                                fontWeight: FontWeight.w400,
                                fontSize: 12.sp,
                              )),
                          Text("Surity Name 1",
                              style: TextStyle(
                                fontWeight: FontWeight.w400,
                                fontSize: 12.sp,
                              )),
                          Text("Surity Account 1",
                              style: TextStyle(
                                fontWeight: FontWeight.w400,
                                fontSize: 12.sp,
                              )),
                          Text("Surity Name 2",
                              style: TextStyle(
                                fontWeight: FontWeight.w400,
                                fontSize: 12.sp,
                              )),
                          Text("Surity Account 2",
                              style: TextStyle(
                                fontWeight: FontWeight.w400,
                                fontSize: 12.sp,
                              )),
                        ],
                      ),
                      Column(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(loan.first.applicationNo.toString(),
                              // ctrl.loans?.applicationNo.toString() ?? "",
                              style: TextStyle(
                                fontWeight: FontWeight.w800,
                                fontSize: 12.sp,
                              )),
                          Text(
                              DateFormat("dd/MM/yyyy")
                                  .format(loan.first.appliedOn),
                              style: TextStyle(
                                fontWeight: FontWeight.w800,
                                fontSize: 12.sp,
                              )),
                          Text(
                              loan.first.approvedOn != null
                                  ? DateFormat("dd/MM/yyyy")
                                      .format(loan.first.approvedOn!)
                                  : "N/A",
                              style: TextStyle(
                                fontWeight: FontWeight.w800,
                                fontSize: 12.sp,
                              )),
                          Text(loan.first.appliedLoanAmt.toString(),
                              style: TextStyle(
                                fontWeight: FontWeight.w800,
                                fontSize: 12.sp,
                              )),
                          Text(loan.first.appliedLoanAmtinWords ?? "-",
                              style: TextStyle(
                                  fontWeight: FontWeight.w800,
                                  fontSize: 12.sp,
                                  overflow: TextOverflow.clip)),
                          Text(loan.first.monthlyInstallmentAmt.toString(),
                              style: TextStyle(
                                fontWeight: FontWeight.w800,
                                fontSize: 12.sp,
                              )),
                          Text(loan.first.totalLoanPaid.toString(),
                              style: TextStyle(
                                fontWeight: FontWeight.w800,
                                fontSize: 12.sp,
                              )),
                          Text(loan.first.totalLoanDue.toString(),
                              style: TextStyle(
                                fontWeight: FontWeight.w800,
                                fontSize: 12.sp,
                              )),
                          Text(loan.first.totalInterestPaid.toString(),
                              style: TextStyle(
                                fontWeight: FontWeight.w800,
                                fontSize: 12.sp,
                              )),
                          Text(loan.first.share.toString(),
                              style: TextStyle(
                                fontWeight: FontWeight.w800,
                                fontSize: 12.sp,
                              )),
                          Text(loan.first.loanReason ?? "-",
                              style: TextStyle(
                                fontWeight: FontWeight.w800,
                                fontSize: 12.sp,
                              )),
                          Text(loan.first.surityName1 ?? "-",
                              style: TextStyle(
                                fontWeight: FontWeight.w800,
                                fontSize: 12.sp,
                              )),
                          Text(
                              loan.first.sAcNo1.toString() == "0" ||
                                      loan.first.sAcNo2 == 0
                                  ? "-"
                                  : loan.first.sAcNo2.toString(),
                              style: TextStyle(
                                fontWeight: FontWeight.w800,
                                fontSize: 12.sp,
                              )),
                          Text(loan.first.surityName2 ?? "-",
                              style: TextStyle(
                                fontWeight: FontWeight.w800,
                                fontSize: 12.sp,
                              )),
                          Text(
                              loan.first.sAcNo2.toString() == "0" ||
                                      loan.first.sAcNo2 == 0
                                  ? "-"
                                  : loan.first.sAcNo2.toString(),
                              style: TextStyle(
                                fontWeight: FontWeight.w800,
                                fontSize: 12.sp,
                              )),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    });
  }
}
