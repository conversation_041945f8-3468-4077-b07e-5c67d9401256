import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:foodcorp/controller/homectrl.dart';
import 'package:foodcorp/shared/router.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import '../../models/loan_model.dart';

class LoanDetailsPage extends StatefulWidget {
  const LoanDetailsPage({super.key, this.loanType});

  final String? loanType;

  @override
  State<LoanDetailsPage> createState() => _LoanDetailsPageState();
}

class _LoanDetailsPageState extends State<LoanDetailsPage> {
  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      List<LoanModel> abc = ctrl.activeLoans
          .where((element) => element.loanType == widget.loanType)
          .toList();

      // print("abc length : ${abc.length}");
      return Scaffold(
        appBar: AppBar(),
        body: Padding(
          padding: const EdgeInsets.all(10),
          child: Column(
            children: [
              ...List.generate(
                abc.length,
                (index) => SizedBox(
                  height: 170.h,
                  width: MediaQuery.sizeOf(context).width,
                  child: InkWell(
                    focusColor: Colors.transparent,
                    splashColor: Colors.transparent,
                    hoverColor: Colors.transparent,
                    highlightColor: Colors.transparent,
                    onTap: () => context.push(
                        "${Routes.moreloandetails}/${abc[index].docId}",
                        extra: abc[index].loanType),
                    child: Card(
                      shadowColor: Colors.transparent,
                      color: Colors.transparent,
                      surfaceTintColor: Colors.transparent,
                      elevation: 0,
                      shape: ContinuousRectangleBorder(
                          side: BorderSide(color: Colors.black),
                          borderRadius: BorderRadius.circular(28.r)),
                      child: Padding(
                        padding: const EdgeInsets.all(20),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Column(
                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text("Loan Application No.",
                                    style: TextStyle(
                                        fontWeight: FontWeight.w400,
                                        fontSize: 13.sp)),
                                Text("Applied Loan Amount",
                                    style: TextStyle(
                                        fontWeight: FontWeight.w400,
                                        fontSize: 13.sp)),
                                Text("Monthly Installment Amount",
                                    style: TextStyle(
                                        fontWeight: FontWeight.w400,
                                        fontSize: 13.sp))
                              ],
                            ),
                            Column(
                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                Text(abc[index].applicationNo.toString(),
                                    style: TextStyle(
                                        fontWeight: FontWeight.w800,
                                        fontSize: 13.sp)),
                                Text(abc[index].appliedLoanAmt.toString(),
                                    style: TextStyle(
                                        fontWeight: FontWeight.w800,
                                        fontSize: 13.sp)),
                                Text(
                                    abc[index].monthlyInstallmentAmt.toString(),
                                    style: TextStyle(
                                        fontWeight: FontWeight.w800,
                                        fontSize: 13.sp))
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              )
            ],
          ),
        ),
      );
    });
  }
}
