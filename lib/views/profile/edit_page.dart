import 'dart:io';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:file_picker/file_picker.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:foodcorp/models/update_request_model.dart';
import 'package:foodcorp/shared/router.dart';
import 'package:go_router/go_router.dart';

class EditPage extends StatefulWidget {
  const EditPage({super.key});

  @override
  State<EditPage> createState() => _EditPageState();
}

class _EditPageState extends State<EditPage> {
  final _formKey = GlobalKey<FormState>();

  final TextEditingController requestTypeController = TextEditingController();
  final TextEditingController remarksController = TextEditingController();
  final TextEditingController docNameController = TextEditingController();
  final TextEditingController supportiveDocController = TextEditingController();

  String? supportiveDocumentName;
  bool _isLoading = false;

  String? _supportiveFilePath;

  String getUserId() {
    final user = FirebaseAuth.instance.currentUser;
    return user != null ? user.uid : 'unknown_user';
  }

  Future<void> _submitData() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    String? supportiveDownloadUrl;
    if (_supportiveFilePath != null) {
      supportiveDownloadUrl = await _uploadSupportiveDocument();
    }

    try {
      // Prepare Model
      final model = UpdateRequestModel(
          docId: '', // Will be set after upload
          uId: getUserId(),
          requestType: requestTypeController.text.trim(),
          remarks: remarksController.text.trim(),
          docName: docNameController.text.trim(),
          supportiveDoc: supportiveDownloadUrl,
          accepted: null,
          createdAt: DateTime.now());

      // Add to Firestore
      final docRef = await FirebaseFirestore.instance
          .collection('requests')
          .add(model.toJson());

      // Optionally, update with docId after creation
      await docRef.update({'docId': docRef.id});

      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Request Sent Successfully!')),
      );

      context.pop();

      // Optional clear
      requestTypeController.clear();
      remarksController.clear();
      docNameController.clear();
      setState(() => supportiveDocumentName = null);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to submit request: $e')),
      );
      setState(() => _isLoading = false);
    }
  }

  Future<String?> _uploadSupportiveDocument() async {
    if (_supportiveFilePath == null) return null;
    try {
      File file = File(_supportiveFilePath!);

      if (!await file.exists()) {
        return null;
      }
      final fileName = file.path.split("/").last;
      final path = 'files/$fileName';
      final ref = FirebaseStorage.instance.ref().child(path);

      UploadTask uploadTask = ref.putFile(file);
      final snapshot = await uploadTask.whenComplete(() => {});
      final urlDownload = await snapshot.ref.getDownloadURL();

      return urlDownload;
    } catch (e) {
      print("Error uploading file: $e");
      return null;
    }
  }

  Future<void> _pickSupportiveDocument() async {
    FilePickerResult? result =
        await FilePicker.platform.pickFiles(type: FileType.any);

    if (result != null &&
        result.files.isNotEmpty &&
        result.files.single.path != null) {
      setState(() {
        _supportiveFilePath = result.files.single.path!;
        supportiveDocumentName = result.files.single.name;
        supportiveDocController.text = supportiveDocumentName!;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        centerTitle: false,
        title: Text(
          "New Update",
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 18.71.sp,
            letterSpacing: -1,
          ),
        ),
        actions: [
          Padding(
            padding: EdgeInsets.only(right: 20.w),
            child: Row(
              children: [
                IconButton(
                    style: ButtonStyle(iconSize: WidgetStatePropertyAll(34)),
                    splashColor: Colors.transparent,
                    highlightColor: Colors.transparent,
                    hoverColor: Colors.transparent,
                    focusColor: Colors.transparent,
                    onPressed: () {
                      context.push(Routes.edithistory);
                    },
                    icon: Icon(Icons.history)),
              ],
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(20),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text("Request Type *",
                  style: TextStyle(fontWeight: FontWeight.bold)),
              SizedBox(height: 4),
              TextFormField(
                controller: requestTypeController,
                decoration: InputDecoration(
                  contentPadding:
                      EdgeInsets.symmetric(vertical: 12, horizontal: 12),
                  hintText: "Enter Request Type",
                  border: OutlineInputBorder(),
                ),
                validator: (value) => value == null || value.trim().isEmpty
                    ? 'Request type required'
                    : null,
              ),
              SizedBox(height: 20),
              Text("Remarks *", style: TextStyle(fontWeight: FontWeight.bold)),
              SizedBox(height: 4),
              TextFormField(
                controller: remarksController,
                minLines: 4,
                maxLines: 6,
                decoration: InputDecoration(
                  hintText: "Remarks",
                  border: OutlineInputBorder(),
                  contentPadding:
                      EdgeInsets.symmetric(vertical: 12, horizontal: 12),
                ),
                validator: (value) => value == null || value.trim().isEmpty
                    ? 'Remarks are required'
                    : null,
              ),
              SizedBox(height: 20),
              Text("Document Name *",
                  style: TextStyle(fontWeight: FontWeight.bold)),
              SizedBox(height: 4),
              TextFormField(
                controller: docNameController,
                decoration: InputDecoration(
                  hintText: "Enter Document Name",
                  border: OutlineInputBorder(),
                  contentPadding:
                      EdgeInsets.symmetric(vertical: 12, horizontal: 12),
                ),
                validator: (value) => value == null || value.trim().isEmpty
                    ? 'Document name required'
                    : null,
              ),
              SizedBox(height: 20),
              Text("Supportive Document",
                  style: TextStyle(fontWeight: FontWeight.bold)),
              SizedBox(height: 4),
              Row(
                children: [
                  Expanded(
                    child: IgnorePointer(
                      child: TextFormField(
                        controller: supportiveDocController,
                        decoration: InputDecoration(
                          hintText: "Upload Document",
                          border: OutlineInputBorder(),
                          contentPadding: EdgeInsets.symmetric(
                              vertical: 12, horizontal: 12),
                        ),
                        enabled: false,
                      ),
                    ),
                  ),
                  SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: _pickSupportiveDocument,
                    child: Text("Upload"),
                  ),
                ],
              ),
              SizedBox(height: 40),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _submitData,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Color.fromRGBO(29, 167, 95, 1),
                        padding: EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(4)),
                        textStyle: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      child: _isLoading
                          ? CircularProgressIndicator(color: Colors.white)
                          : Text(
                              "Submit",
                              style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 16.sp,
                                  fontWeight: FontWeight.w600),
                            ),
                    ),
                  ),
                  SizedBox(width: 16),
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _isLoading
                          ? null
                          : () {
                              Navigator.of(context).pop();
                            },
                      style: OutlinedButton.styleFrom(
                        padding: EdgeInsets.symmetric(vertical: 16),
                        textStyle: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      child: Text("Cancel"),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
