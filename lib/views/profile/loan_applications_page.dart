import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:foodcorp/controller/homectrl.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

class LoanApplicationsPage extends StatefulWidget {
  const LoanApplicationsPage({super.key});

  @override
  State<LoanApplicationsPage> createState() => _LoanHistoryPageState();
}

class _LoanHistoryPageState extends State<LoanApplicationsPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text("Loan Applications")),
      body: GetBuilder<HomeCtrl>(builder: (ctrl) {
        ctrl.appliedLoans.sort((a, b) => a.appliedOn.compareTo(b.appliedOn));
        return ctrl.appliedLoans.isEmpty
            ? Center(child: Text("No Loan Applications"))
            : SingleChildScrollView(
                child: Padding(
                    padding:
                        EdgeInsets.only(top: 10.h, left: 20.w, right: 20.w),
                    child: Column(children: [
                      ...List.generate(
                          ctrl.appliedLoans.length,
                          (index) => Padding(
                              padding: const EdgeInsets.only(bottom: 10),
                              child: Container(
                                  padding: EdgeInsets.all(10),
                                  height: 300,
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.all(
                                          Radius.circular(16.r)),
                                      color: const Color.fromARGB(
                                          125, 188, 220, 204),
                                      border: Border.all(
                                          color: const Color.fromRGBO(
                                              147, 207, 176, 1))),
                                  child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Expanded(
                                          child: Column(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceAround,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text("Loan Type",
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.w400,
                                                    fontSize: 13.sp,
                                                  )),
                                              Text("Loan Status",
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.w400,
                                                    fontSize: 13.sp,
                                                  )),
                                              Text("No. of Loans Ahead",
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.w400,
                                                    fontSize: 13.sp,
                                                  )),
                                              Text("Loan Application No.",
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.w400,
                                                    fontSize: 13.sp,
                                                  )),
                                              Text("Applied On",
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.w400,
                                                    fontSize: 13.sp,
                                                  )),
                                              Text("Applied Loan Amount",
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.w400,
                                                    fontSize: 13.sp,
                                                  )),
                                              Text(
                                                  "Applied Loan Amount in words",
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.w400,
                                                    fontSize: 13.sp,
                                                  )),
                                              Text("Monthly Installment Amount",
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.w400,
                                                    fontSize: 13.sp,
                                                  )),
                                              // Text("Share",
                                              //     style: TextStyle(
                                              //       fontWeight: FontWeight.w400,
                                              //       fontSize: 13.sp,
                                              //     )),
                                              Text("Loan Reason",
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.w400,
                                                    fontSize: 13.sp,
                                                  )),
                                            ],
                                          ),
                                        ),
                                        Expanded(
                                          child: Column(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceAround,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.end,
                                            children: [
                                              Text(
                                                  ctrl.appliedLoans[index]
                                                      .loanType
                                                      .toString(),
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.w800,
                                                    fontSize: 13.sp,
                                                  )),
                                              Text(
                                                getLoanStatus(
                                                    ctrl.appliedLoans[index]),
                                                style: TextStyle(
                                                    color: Colors.orange,
                                                    fontWeight: FontWeight.w800,
                                                    fontSize: 13.sp),
                                              ),
                                              Text(
                                                  countApplicantsBefore(
                                                          ctrl.appliedLoans[
                                                              index],
                                                          ctrl.activeLoans)
                                                      .toString(),
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.w800,
                                                    fontSize: 13.sp,
                                                  )),
                                              Text(
                                                  ctrl.appliedLoans[index]
                                                      .applicationNo
                                                      .toString(),
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.w800,
                                                    fontSize: 13.sp,
                                                  )),
                                              Text(
                                                  DateFormat("dd/MM/yyyy")
                                                      .format(ctrl
                                                          .appliedLoans[index]
                                                          .appliedOn),
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.w800,
                                                    fontSize: 13.sp,
                                                  )),
                                              Text(
                                                  ctrl.appliedLoans[index]
                                                      .appliedLoanAmt
                                                      .toString(),
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.w800,
                                                    fontSize: 13.sp,
                                                  )),
                                              Text(
                                                  ctrl.appliedLoans[index]
                                                      .appliedLoanAmtinWords
                                                      .toString(),
                                                  style: TextStyle(
                                                      fontWeight:
                                                          FontWeight.w800,
                                                      fontSize: 13.sp,
                                                      overflow: TextOverflow
                                                          .ellipsis)),
                                              Text(
                                                  ctrl.appliedLoans[index]
                                                      .monthlyInstallmentAmt
                                                      .toString(),
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.w800,
                                                    fontSize: 13.sp,
                                                  )),
                                              // Text(
                                              //     ctrl.appliedLoans[index].share
                                              //         .toString(),
                                              //     style: TextStyle(
                                              //       fontWeight: FontWeight.w800,
                                              //       fontSize: 13.sp,
                                              //     )),
                                              Text(
                                                  ctrl.appliedLoans[index]
                                                      .loanReason
                                                      .toString(),
                                                  style: TextStyle(
                                                      fontWeight:
                                                          FontWeight.w800,
                                                      fontSize: 13.sp,
                                                      overflow: TextOverflow
                                                          .ellipsis)),
                                            ],
                                          ),
                                        ),
                                      ]))))
                    ])));
      }),
    );
  }

  int countApplicantsBefore(currentLoan, allLoans) {
    return allLoans
        .where((loan) =>
            loan.loanType == currentLoan.loanType &&
            loan.appliedOn.isBefore(currentLoan.appliedOn) &&
            loan.rejectionDate == null &&
            loan.isSettled == false)
        .length;
  }

  String getLoanStatus(loan) {
    if (loan.isSettled) return "Settled";
    if (loan.rejectionDate != null) return "Rejected";
    if (loan.processedOn != null) return "Processed";
    if (loan.approvedOn != null) return "Approved";
    return "Pending";
  }
}
