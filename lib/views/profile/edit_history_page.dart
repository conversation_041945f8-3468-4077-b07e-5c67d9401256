import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:foodcorp/shared/firebase.dart';
import 'package:foodcorp/shared/methods.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';

class EditHistoryPage extends StatefulWidget {
  const EditHistoryPage({super.key});

  @override
  State<EditHistoryPage> createState() => _EditHistoryPageState();
}

class _EditHistoryPageState extends State<EditHistoryPage> {
  String _getStatusText(bool? accepted) {
    if (accepted == null) return 'Pending';
    return accepted ? 'Accepted' : 'Rejected';
  }

  Color _getStatusColor(bool? accepted) {
    if (accepted == null) return Colors.grey.shade600;
    return accepted ? Colors.green.shade700 : Colors.red.shade700;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        centerTitle: false,
        elevation: 0.5,
        iconTheme: IconThemeData(color: Colors.black87),
        title: Text(
          "Update History",
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 18.sp,
            color: Colors.black87,
          ),
        ),
      ),
      body: Padding(
        padding: EdgeInsets.all(16.w),
        child: StreamBuilder<QuerySnapshot>(
          stream: FBFireStore.requests
              .orderBy('createdAt', descending: true)
              .snapshots(),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return Center(child: CircularProgressIndicator());
            }
            if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
              return Center(
                child: Text(
                  "No requests found.",
                  style:
                      TextStyle(color: Colors.grey.shade600, fontSize: 14.sp),
                ),
              );
            }

            final docs = snapshot.data!.docs;

            return ListView.separated(
              physics: BouncingScrollPhysics(),
              separatorBuilder: (_, __) => SizedBox(height: 16.h),
              itemCount: docs.length,
              itemBuilder: (context, index) {
                final data = docs[index].data() as Map<String, dynamic>;

                final requestType = data['requestType'] ?? '';
                final remarks = data['remarks'] ?? '';
                final docName = data['docName'] ?? '';
                final supportiveDoc = data['supportiveDoc'] ?? '';
                final accepted = data['accepted'] as bool?;
                final createdAtTimestamp = data['createdAt'] as Timestamp?;
                final formattedDate = createdAtTimestamp != null
                    ? DateFormat.yMMMEd()
                        .add_jm()
                        .format(createdAtTimestamp.toDate())
                    : 'Date not available';

                return Container(
                  padding: EdgeInsets.all(16.w),
                  decoration: BoxDecoration(
                    // color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildLabelValue("Request Type", requestType),
                      SizedBox(height: 8.h),
                      _buildLabelValue("Remarks", remarks),
                      SizedBox(height: 8.h),
                      _buildLabelValue("Document Name", docName),
                      if (supportiveDoc.isEmpty) SizedBox(height: 8.h),
                      if (supportiveDoc.isNotEmpty) ...[
                        Row(
                          children: [
                            Text(
                              "Supportive Document:",
                              style: TextStyle(
                                fontWeight: FontWeight.w700,
                                fontSize: 14.sp,
                                color: Colors.black87,
                              ),
                            ),
                            IconButton(
                              icon: Icon(Icons.download_rounded,
                                  color: Colors.blue, size: 24.sp),
                              tooltip: 'Download Supportive Document',
                              onPressed: () async {
                                final url = supportiveDoc;
                                if (await canLaunchUrl(Uri.parse(url))) {
                                  await launchUrl(Uri.parse(url),
                                      mode: LaunchMode.externalApplication);
                                } else {
                                  showAppSnackBar(
                                      "Could not launch document URL");
                                }
                              },
                            ),
                          ],
                        ),
                      ],
                      Row(
                        children: [
                          Text(
                            "Status: ",
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 14.sp,
                              color: Colors.black87,
                            ),
                          ),
                          Text(
                            _getStatusText(accepted),
                            style: TextStyle(
                              fontSize: 14.sp,
                              fontWeight: FontWeight.w600,
                              color: _getStatusColor(accepted),
                            ),
                          ),
                          Spacer(),
                          Text(
                            formattedDate,
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: Colors.grey.shade600,
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                );
              },
            );
          },
        ),
      ),
    );
  }

  Widget _buildLabelValue(String label, String value) {
    return RichText(
      text: TextSpan(
        style: TextStyle(
          fontSize: 14.sp,
          color: Colors.black87,
          height: 1.3,
        ),
        children: [
          TextSpan(
            text: "$label: ",
            style: TextStyle(fontWeight: FontWeight.w700),
          ),
          TextSpan(text: value),
        ],
      ),
    );
  }
}
