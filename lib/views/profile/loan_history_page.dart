import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:foodcorp/controller/homectrl.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

class LoanHistoryPage extends StatefulWidget {
  const LoanHistoryPage({super.key});

  @override
  State<LoanHistoryPage> createState() => _LoanHistoryPageState();
}

class _LoanHistoryPageState extends State<LoanHistoryPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Loan History"),
      ),
      body: GetBuilder<HomeCtrl>(builder: (ctrl) {
        // print(" ctrl.completedLoans.length : ${ctrl.completedLoans.length}");
        return ctrl.completedLoans.isEmpty
            ? Center(child: Text("No Loan History"))
            : SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.only(top: 10.h, left: 20.w, right: 20.w),
                  child: Column(
                    children: [
                      ...List.generate(
                        ctrl.completedLoans.length,
                        (index) => Padding(
                          padding: const EdgeInsets.only(bottom: 10),
                          child: Container(
                            padding: EdgeInsets.all(10),
                            height: 500,
                            decoration: BoxDecoration(
                                borderRadius:
                                    BorderRadius.all(Radius.circular(16.r)),
                                color: const Color.fromARGB(125, 188, 220, 204),
                                border: Border.all(
                                    color: const Color.fromRGBO(
                                        147, 207, 176, 1))),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                  child: Column(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceAround,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text("Loan Application No.",
                                          style: TextStyle(
                                            fontWeight: FontWeight.w400,
                                            fontSize: 13.sp,
                                          )),
                                      Text("Applied On",
                                          style: TextStyle(
                                            fontWeight: FontWeight.w400,
                                            fontSize: 13.sp,
                                          )),
                                      Text("Approved On",
                                          style: TextStyle(
                                            fontWeight: FontWeight.w400,
                                            fontSize: 13.sp,
                                          )),
                                      Text("Settled On",
                                          style: TextStyle(
                                            fontWeight: FontWeight.w400,
                                            fontSize: 13.sp,
                                          )),
                                      Text("Applied Loan Amount",
                                          style: TextStyle(
                                            fontWeight: FontWeight.w400,
                                            fontSize: 13.sp,
                                          )),
                                      Text("Applied Loan Amount in words",
                                          style: TextStyle(
                                              fontWeight: FontWeight.w400,
                                              fontSize: 13.sp)),
                                      Text("Monthly Installment Amount",
                                          style: TextStyle(
                                            fontWeight: FontWeight.w400,
                                            fontSize: 13.sp,
                                          )),
                                      Text("Total Loan Paid",
                                          style: TextStyle(
                                            fontWeight: FontWeight.w400,
                                            fontSize: 13.sp,
                                          )),
                                      Text("Total Loan Due",
                                          style: TextStyle(
                                            fontWeight: FontWeight.w400,
                                            fontSize: 13.sp,
                                          )),
                                      Text("Total Interest Paid",
                                          style: TextStyle(
                                            fontWeight: FontWeight.w400,
                                            fontSize: 13.sp,
                                          )),
                                      Text("Share",
                                          style: TextStyle(
                                            fontWeight: FontWeight.w400,
                                            fontSize: 13.sp,
                                          )),
                                      Text("Loan Reason",
                                          style: TextStyle(
                                            fontWeight: FontWeight.w400,
                                            fontSize: 13.sp,
                                          )),
                                    ],
                                  ),
                                ),
                                Expanded(
                                  child: Column(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceAround,
                                    crossAxisAlignment: CrossAxisAlignment.end,
                                    children: [
                                      Text(
                                          ctrl.completedLoans[index]
                                              .applicationNo
                                              .toString(),
                                          // ctrl.loans?.applicationNo.toString() ?? "",
                                          style: TextStyle(
                                            fontWeight: FontWeight.w800,
                                            fontSize: 13.sp,
                                          )),
                                      Text(
                                          DateFormat("dd/MM/yyyy").format(ctrl
                                              .completedLoans[index].appliedOn),
                                          style: TextStyle(
                                            fontWeight: FontWeight.w800,
                                            fontSize: 13.sp,
                                          )),
                                      Text(
                                          ctrl.completedLoans[index]
                                                      .approvedOn !=
                                                  null
                                              ? DateFormat("dd/MM/yyyy").format(
                                                  ctrl.completedLoans[index]
                                                      .approvedOn!)
                                              : "N/A",
                                          style: TextStyle(
                                            fontWeight: FontWeight.w800,
                                            fontSize: 13.sp,
                                          )),
                                      Text(
                                          ctrl.completedLoans[index]
                                                      .approvedOn !=
                                                  null
                                              ? DateFormat("dd/MM/yyyy").format(
                                                  ctrl.completedLoans[index]
                                                      .settledOn!)
                                              : "N/A",
                                          style: TextStyle(
                                            fontWeight: FontWeight.w800,
                                            fontSize: 13.sp,
                                          )),
                                      Text(
                                          ctrl.completedLoans[index]
                                              .appliedLoanAmt
                                              .toString(),
                                          style: TextStyle(
                                            fontWeight: FontWeight.w800,
                                            fontSize: 13.sp,
                                          )),
                                      Text(
                                          ctrl.completedLoans[index]
                                              .appliedLoanAmtinWords
                                              .toString(),
                                          style: TextStyle(
                                              fontWeight: FontWeight.w800,
                                              fontSize: 13.sp,
                                              overflow: TextOverflow.ellipsis)),
                                      Text(
                                          ctrl.completedLoans[index]
                                              .monthlyInstallmentAmt
                                              .toString(),
                                          style: TextStyle(
                                            fontWeight: FontWeight.w800,
                                            fontSize: 13.sp,
                                          )),
                                      Text(
                                          ctrl.completedLoans[index]
                                              .totalLoanPaid
                                              .toString(),
                                          style: TextStyle(
                                            fontWeight: FontWeight.w800,
                                            fontSize: 13.sp,
                                          )),
                                      Text(
                                          ctrl.completedLoans[index]
                                              .totalLoanDue
                                              .toString(),
                                          style: TextStyle(
                                            fontWeight: FontWeight.w800,
                                            fontSize: 13.sp,
                                          )),
                                      Text(
                                          ctrl.completedLoans[index]
                                              .totalInterestPaid
                                              .toString(),
                                          style: TextStyle(
                                            fontWeight: FontWeight.w800,
                                            fontSize: 13.sp,
                                          )),
                                      Text(
                                          ctrl.completedLoans[index].share
                                              .toString(),
                                          style: TextStyle(
                                            fontWeight: FontWeight.w800,
                                            fontSize: 13.sp,
                                          )),
                                      Text(
                                          ctrl.completedLoans[index].loanReason
                                              .toString(),
                                          style: TextStyle(
                                            fontWeight: FontWeight.w800,
                                            fontSize: 13.sp,
                                          )),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      )
                    ],
                  ),
                ),
              );
      }),
    );
  }
}
