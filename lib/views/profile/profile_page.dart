// ignore_for_file: use_build_context_synchronously, deprecated_member_use, unused_local_variable

import 'dart:io' as io;
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:foodcorp/models/user_monthlyrecord_model.dart';
import 'package:foodcorp/shared/methods.dart';
import 'package:foodcorp/views/profile/loan_applications_page.dart';
import 'package:foodcorp/views/profile/loan_calculator_page.dart';
import 'package:foodcorp/views/profile/loan_history_page.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:screenshot/screenshot.dart';
import 'package:share_plus/share_plus.dart';
import '../../controller/homectrl.dart';
import '../../shared/firebase.dart';
import '../../shared/router.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  final ScreenshotController screenshotController = ScreenshotController();

  int getMonthNum(int index) => (index + 3) > 12 ? (index - 9) : (index + 3);

  int counter = 0;
  Uint8List? imageFile;

  bool loading = false;
  bool scLoading = false;
  bool slLoading = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        centerTitle: false,
        actions: [
          Padding(
            padding: EdgeInsets.only(right: 20.w),
            child: Row(
              children: [
                IconButton(
                    style: ButtonStyle(iconSize: WidgetStatePropertyAll(34)),
                    splashColor: Colors.transparent,
                    highlightColor: Colors.transparent,
                    hoverColor: Colors.transparent,
                    focusColor: Colors.transparent,
                    onPressed: () async => giftOnPressed(context),
                    icon: Icon(CupertinoIcons.gift)),
                IconButton(
                    style: ButtonStyle(iconSize: WidgetStatePropertyAll(34)),
                    splashColor: Colors.transparent,
                    highlightColor: Colors.transparent,
                    hoverColor: Colors.transparent,
                    focusColor: Colors.transparent,
                    // color: const Color.fromARGB(255, 91, 14, 14),
                    onPressed: () {
                      context.push(Routes.edit);
                    },
                    icon: Icon(Icons.edit_outlined)),
                IconButton(
                    style: ButtonStyle(iconSize: WidgetStatePropertyAll(34)),
                    splashColor: Colors.transparent,
                    highlightColor: Colors.transparent,
                    hoverColor: Colors.transparent,
                    focusColor: Colors.transparent,
                    // color: const Color.fromARGB(255, 91, 14, 14),
                    // iconSize: 40.sp,
                    onPressed: () async {
                      await showDialog(
                        context: context,
                        builder: (context) => AlertDialog(
                          backgroundColor: Colors.white,
                          title: const Text("Logout?"),
                          content:
                              const Text("Are you sure you want to LOGOUT?"),
                          actions: [
                            TextButton(
                                onPressed: () async {
                                  setState(() {
                                    loading = true;
                                  });

                                  await FBAuth.auth.signOut();
                                  context.pop();
                                  if (context.mounted) {
                                    context.go(Routes.login);
                                  }

                                  setState(() {
                                    loading = false;
                                  });
                                },
                                child: const Text(
                                  "Yes",
                                  style: TextStyle(fontSize: 21),
                                )),
                            TextButton(
                                onPressed: () async {
                                  if (context.mounted) {
                                    Navigator.of(context).pop(false);
                                  }
                                },
                                child: const Text(
                                  "No",
                                  style: TextStyle(fontSize: 21),
                                )),
                          ],
                        ),
                      );
                    },
                    icon: const Icon(CupertinoIcons.square_arrow_right)),
              ],
            ),
          ),
        ],
        title: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // const Image(
            //     // height: 40,
            //     width: 65,
            //     image: AssetImage("assets/images/foodcorpimage4_removebg.png")),
            // SizedBox(width: 10),
            Text("Profile",
                style: GoogleFonts.sourceCodePro(
                    letterSpacing: -1,
                    fontSize: 18.71.sp,
                    fontWeight: FontWeight.w700)),
          ],
        ),
        backgroundColor: Colors.white,
        surfaceTintColor: Colors.white,
        scrolledUnderElevation: 5,
        shadowColor: Colors.black26,
      ),
      body: GetBuilder<HomeCtrl>(builder: (ctrl) {
        final doName = ctrl.districtoffice.firstWhereOrNull(
            (element) => element.docId == ctrl.users?.districtoffice);
        return SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.only(
                top: 20.h, right: 20.w, left: 20.w, bottom: 40.h),
            child: loading
                ? Center(
                    heightFactor: 20,
                    child: CircularProgressIndicator(),
                  )
                : Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        padding: EdgeInsets.only(
                            left: 25.h, right: 25.h, top: 25.h, bottom: 25.h),
                        decoration: BoxDecoration(
                            border: Border.all(
                                color: const Color.fromRGBO(224, 224, 224, 1)),
                            borderRadius: BorderRadius.circular(16.r)),
                        child: Column(
                          children: [
                            CustomProfileRow(
                                data: ctrl.users?.cpfNo.toString() ?? "",
                                title: "CPF NO"),
                            SizedBox(height: 20.h),
                            CustomProfileRow(
                                data: ctrl.users?.employeeNo.toString() ?? "",
                                title: "EMP NO"),
                            SizedBox(height: 20.h),
                            CustomProfileRow(
                                data: ctrl.users?.name ?? "", title: "NAME"),
                            SizedBox(height: 20.h),
                            CustomProfileRow(
                                data: doName?.name,
                                // ctrl.users?.districtoffice.toString() ?? "",
                                title: "DO"),
                            SizedBox(height: 20.h),
                            CustomProfileRow(
                                data: ctrl.users?.phoneNo.toString() ?? "-",
                                title: "Phone"),
                            SizedBox(height: 20.h),
                            CustomProfileRow(
                                data: ctrl.users?.email ?? "-", title: "Email"),
                            SizedBox(height: 20.h),
                            CustomProfileRow(
                                data: ctrl.users?.currentAddress ?? "-",
                                title: "Address"),
                            SizedBox(height: 20.h),
                            CustomProfileRow(
                                data: ctrl.users?.bankAcNo.toString() ?? "-",
                                title: "Bank A/c No."),
                            SizedBox(height: 20.h),
                            CustomProfileRow(
                                data: ctrl.users?.bankAcName ?? "-",
                                title: "Bank A/c Name"),
                            SizedBox(height: 20.h),
                            CustomProfileRow(
                                data: ctrl.users?.bankName ?? "-",
                                title: "Bank Name"),
                            SizedBox(height: 20.h),
                            CustomProfileRow(
                                data: ctrl.users?.ifscCode ?? "-",
                                title: "IFSC Code"),
                            SizedBox(height: 20.h),
                            CustomProfileRow(
                                data: ctrl.users?.nomineeName ?? "-",
                                title: "Nominee Name"),
                            SizedBox(height: 20.h),
                            CustomProfileRow(
                                data: ctrl.users?.nomineeRelation ?? "-",
                                title: "Nominee Relation"),
                            SizedBox(height: 20.h),
                          ],
                        ),
                      ),
                      const SizedBox(height: 15),
                      InkWell(
                        overlayColor:
                            const WidgetStatePropertyAll(Colors.transparent),
                        onTap: () => Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => LoanApplicationsPage(),
                            )),
                        child: Container(
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                              border: Border.all(
                                  color:
                                      const Color.fromRGBO(224, 224, 224, 1)),
                              borderRadius: BorderRadius.circular(16)),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                children: [
                                  const Icon(Icons.launch_outlined),
                                  SizedBox(
                                    width: 10.w,
                                  ),
                                  Text(
                                    "My Loan Applications",
                                    style: GoogleFonts.poppins(
                                        fontSize: 15.sp,
                                        fontWeight: FontWeight.w500,
                                        color: Colors.black),
                                  ),
                                ],
                              ),
                              const Icon(Icons.arrow_forward_ios_rounded)
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 15),
                      InkWell(
                        overlayColor:
                            const WidgetStatePropertyAll(Colors.transparent),
                        onTap: () => Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => LoanHistoryPage(),
                            )),
                        child: Container(
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                              border: Border.all(
                                  color:
                                      const Color.fromRGBO(224, 224, 224, 1)),
                              borderRadius: BorderRadius.circular(16)),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                children: [
                                  const Icon(Icons.history),
                                  SizedBox(
                                    width: 10.w,
                                  ),
                                  Text(
                                    "Loan History",
                                    style: GoogleFonts.poppins(
                                        fontSize: 15.sp,
                                        fontWeight: FontWeight.w500,
                                        color: Colors.black),
                                  ),
                                ],
                              ),
                              const Icon(Icons.arrow_forward_ios_rounded)
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 15),
                      InkWell(
                        overlayColor:
                            const WidgetStatePropertyAll(Colors.transparent),
                        onTap: () => Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => LoanCalculatorPage(),
                            )),
                        child: Container(
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                              border: Border.all(
                                  color:
                                      const Color.fromRGBO(224, 224, 224, 1)),
                              borderRadius: BorderRadius.circular(16)),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                children: [
                                  const Icon(Icons.calculate_outlined),
                                  SizedBox(
                                    width: 10.w,
                                  ),
                                  Text(
                                    "Loan Calculator",
                                    style: GoogleFonts.poppins(
                                        fontSize: 15.sp,
                                        fontWeight: FontWeight.w500,
                                        color: Colors.black),
                                  ),
                                ],
                              ),
                              const Icon(Icons.arrow_forward_ios_rounded)
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
          ),
        );
      }),
    );
  }

  Future<void> giftOnPressed(BuildContext context) async {
    try {
      final currentUserId = FBAuth.auth.currentUser?.uid;

      final ctrl = Get.find<HomeCtrl>();
      final currentUser = ctrl.users;

      final now = DateTime.now();
      final int fyStartMonth = 4; // April
      int fyStartYear;
      int fyEndYear;
      if (now.month >= fyStartMonth) {
        fyStartYear = now.year;
        fyEndYear = now.year + 1;
      } else {
        fyStartYear = now.year - 1;
        fyEndYear = now.year;
      }

      final recoverySnapshots = await FBFireStore.recoverymonthly
          .where('doId', isEqualTo: currentUser?.districtoffice)
          .where('year', isEqualTo: fyStartYear)
          .get();

      int paidMonths = 0;

      for (var doc in recoverySnapshots.docs) {
        final data = doc.data();

        final int? year = data['selectedyear'];
        final int? month = data['selectedmonth'];

        if (year == fyStartYear && month != null) {
          if (month >= fyStartMonth && month <= now.month) {
            final num subs = data['subs'] ?? 0;
            final num subscriptionPaid = data['subscriptionPaid'] ?? 0;

            if (subs > 0 && subscriptionPaid >= subs) {
              paidMonths++;
            }
          }
        }
      }

      final isEligible = paidMonths >= 6;

      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text("Gift Eligibility"),
          content: RichText(
            text: TextSpan(
              style:
                  Theme.of(context).textTheme.bodyLarge, // default text style
              children: [
                TextSpan(
                  text: isEligible ? "YES" : "NO",
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16, // optional for slightly bigger text
                  ),
                ),
                TextSpan(
                  text: isEligible
                      ? ", you are eligible for gift."
                      : ", you are not eligible.",
                  style: const TextStyle(
                    fontWeight: FontWeight.normal,
                  ),
                ),
              ],
            ),
          ),

          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text("OK"),
            ),
          ],
          backgroundColor: Colors.white,
          // content: Text("Current User ID: $currentUserId"),
        ),
      );
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  Future<void> shareCertificateOnPressed(
    HomeCtrl ctrl,
    double share,
    ScreenshotController sctrl,
    BuildContext context,
    void Function(void Function()) setState,
  ) async {
    try {
      setState(() => scLoading = true);

      // Capture the widget as an image
      final Uint8List uint8List = await sctrl.captureFromWidget(
        ShareCertificate(
          ctrl: ctrl,
          share: share,
          sctrl: sctrl,
        ),
        pixelRatio: 3.0,
      );

      // Generate PDF from the image
      final pdf = pw.Document();
      pdf.addPage(
        pw.Page(
          orientation: pw.PageOrientation.landscape,
          pageFormat: PdfPageFormat.a4,
          build: (pw.Context context) =>
              pw.Center(child: pw.Image(pw.MemoryImage(uint8List))),
        ),
      );

      final pdfBytes = await pdf.save();

      if (kIsWeb) {
        // ✅ Share on Web
        await Printing.sharePdf(
          bytes: pdfBytes,
          filename: 'share_certificate.pdf',
        );
      } else {
        // ✅ Share on Android/iOS
        final tempDir = await getTemporaryDirectory();
        final filePath = '${tempDir.path}/share_certificate.pdf';
        final file = io.File(filePath);
        await file.writeAsBytes(pdfBytes);

        final xFile = XFile(
          file.path,
          mimeType: 'application/pdf',
          name: 'share_certificate.pdf',
        );

        await Share.shareXFiles([xFile]);
      }
    } catch (e) {
      debugPrint('Error generating share certificate: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("Failed to generate PDF: $e")),
        );
      }
    } finally {
      setState(() => scLoading = false); // ✅ Hide loader
    }
  }
}

Map<String, num> calculateMonthlyTotals(List<UserMonthlyRecordModel> records) {
  num loanPaidLtTotal = 0;
  num loanPaidStTotal = 0;
  num subscriptionTotal = 0;
  num ltInstallmentTotal = 0;
  num stInstallmentTotal = 0;
  num interestTotal = 0;
  num totalAmtPaidTotal = 0;

  for (var record in records) {
    loanPaidLtTotal += (record.loanPaidLt ?? 0);
    loanPaidStTotal += (record.loanPaidst ?? 0);
    subscriptionTotal += (record.subscriptionPaid ?? 0);
    ltInstallmentTotal += (record.longTermInstalmentPaid ?? 0);
    stInstallmentTotal += (record.shortTermInstalmentPaid ?? 0);
    interestTotal += (record.interest ?? 0);
    totalAmtPaidTotal += (record.installmentRec ?? 0);
  }

  return {
    "loanPaidLtTotal": loanPaidLtTotal,
    "loanPaidStTotal": loanPaidStTotal,
    "subscriptionTotal": subscriptionTotal,
    "ltInstallmentTotal": ltInstallmentTotal,
    "stInstallmentTotal": stInstallmentTotal,
    "interestTotal": interestTotal,
    "totalAmtPaidTotal": totalAmtPaidTotal,
  };
}

class SubsidiaryLedgerPreviewWidget extends StatefulWidget {
  final HomeCtrl ctrl;

  const SubsidiaryLedgerPreviewWidget({super.key, required this.ctrl});

  @override
  State<SubsidiaryLedgerPreviewWidget> createState() =>
      _SubsidiaryLedgerPreviewWidgetState();
}

class _SubsidiaryLedgerPreviewWidgetState
    extends State<SubsidiaryLedgerPreviewWidget> {
  @override
  Widget build(BuildContext context) {
    final dateFormatter = DateFormat('dd-MM-yyyy');

    List<UserMonthlyRecordModel> userMonthlyRecords =
        widget.ctrl.userMonthlyRecord.toList();

    final totals = calculateMonthlyTotals(userMonthlyRecords);

    UserMonthlyRecordModel? aprilRecord = userMonthlyRecords.firstWhere(
      (record) =>
          record.selectedmonth == 4 &&
          record.selectedyear == DateTime.now().year,
      orElse: () => UserMonthlyRecordModel(
        docId: '',
        selectedyear: DateTime.now().year,
        selectedmonth: 3,
        cpfNo: 0,
        name: '',
        districtoffice: '',
        obLt: 0,
        obSt: 0,
        loanPaidLt: 0,
        loanPaidst: 0,
        loanTotal: 0,
        subs: 0,
        ltInstallment: 0,
        stInstallment: 0,
        interest: 0,
        total: 0,
        installmentRec: 0,
        installmentRecDate: null,
        ltCb: 0,
        stCb: 0,
        subscriptionPaid: 0,
        longTermInstalmentPaid: 0,
        shortTermInstalmentPaid: 0,
        longTermInterestPaid: 0,
        shortTermInterestPaid: 0,
        isPaid: false,
        status: '',
        dues: 0,
        penalty: 0,
        shareValue: 0,
        societySubsPayout: 0,
        societySharesPayout: 0,
        penaltyPaid: 0,
      ),
    );

    num obSubs = aprilRecord.subscriptionPaid ?? 0;
    num obShare = aprilRecord.shareValue ?? 0;
    num obLtLoan = aprilRecord.obLt ?? 0;
    num obStLoan = aprilRecord.obSt ?? 0;

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            "Subsidiary Ledger Preview",
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Container(
            decoration: BoxDecoration(border: Border.all()),
            padding: EdgeInsets.all(8),
            child: Text(
                "NAME OF SOCIETY MEMBER : ${widget.ctrl.users?.name ?? "-"}",
                style:
                    const TextStyle(fontWeight: FontWeight.bold, fontSize: 17)),
          ),
          const SizedBox(height: 16),
          Table(
            border: TableBorder.all(),
            columnWidths: const {0: FlexColumnWidth(4), 1: FlexColumnWidth(2)},
            children: [
              TableRow(children: [
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text("OB SUBS :",
                      style: TextStyle(fontWeight: FontWeight.w500)),
                ),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text("$obSubs", textAlign: TextAlign.right),
                ),
              ]),
              TableRow(children: [
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text("OB SHARE :",
                      style: TextStyle(fontWeight: FontWeight.w500)),
                ),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text("$obShare", textAlign: TextAlign.right),
                ),
              ]),
              TableRow(children: [
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text("OB LT LOAN :",
                      style: TextStyle(fontWeight: FontWeight.w500)),
                ),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text("$obLtLoan", textAlign: TextAlign.right),
                ),
              ]),
              TableRow(children: [
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text("OB ST LOAN :",
                      style: TextStyle(fontWeight: FontWeight.w500)),
                ),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text("$obStLoan", textAlign: TextAlign.right),
                ),
              ]),
            ],
          ),
          const SizedBox(height: 16),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: ConstrainedBox(
              constraints: BoxConstraints(maxWidth: 1600),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Table(
                    border: TableBorder.all(color: Colors.grey.shade400),
                    // columnWidths: const {
                    //   0: FlexColumnWidth(2),
                    //   1: FlexColumnWidth(2),
                    //   2: FlexColumnWidth(1),
                    //   3: FlexColumnWidth(1),
                    //   4: FlexColumnWidth(1),
                    // },
                    children: [
                      TableRow(
                        decoration: BoxDecoration(color: Colors.grey.shade300),
                        children: [
                          _tableCell("Month", isHeader: true),
                          _tableCell("Date", isHeader: true),
                          _tableCell("OBLT", isHeader: true),
                          _tableCell("OBST", isHeader: true),
                          _tableCell("LoanPaidLt", isHeader: true),
                          _tableCell("LoanPaidSt", isHeader: true),
                          _tableCell("Subscription", isHeader: true),
                          _tableCell("LT Installment", isHeader: true),
                          _tableCell("ST Installment", isHeader: true),
                          _tableCell("Interest", isHeader: true),
                          _tableCell("Total Amt Paid", isHeader: true),
                          _tableCell("LTCB", isHeader: true),
                          _tableCell("STCB", isHeader: true),
                        ],
                      ),
                      for (var record in userMonthlyRecords)
                        TableRow(children: [
                          _tableCell(getMonthName(
                              int.tryParse(record.selectedmonth.toString()) ??
                                  0)),
                          _tableCell(record.installmentRecDate != null
                              ? dateFormatter.format(DateTime.parse(
                                  record.installmentRecDate.toString()))
                              : "-"),
                          _tableCell((record.obLt ?? 0).toString()),
                          _tableCell((record.obSt ?? 0).toString()),
                          _tableCell((record.loanPaidLt ?? 0).toString()),
                          _tableCell((record.loanPaidst ?? 0).toString()),
                          _tableCell((record.subscriptionPaid ?? 0).toString()),
                          _tableCell(
                              (record.longTermInstalmentPaid ?? 0).toString()),
                          _tableCell(
                              (record.shortTermInstalmentPaid ?? 0).toString()),
                          _tableCell((record.interest ?? 0).toString()),
                          _tableCell((record.installmentRec ?? 0).toString()),
                          _tableCell((record.ltCb ?? 0).toString()),
                          _tableCell((record.stCb ?? 0).toString()),
                        ]),
                      TableRow(
                        decoration: BoxDecoration(color: Colors.grey.shade200),
                        children: [
                          _tableCell("Total", isHeader: true),
                          _tableCell(""), // Date column empty
                          _tableCell(""), // OBLT empty
                          _tableCell(""), // OBST empty
                          _tableCell(totals["loanPaidLtTotal"].toString(),
                              isHeader: true),
                          _tableCell(totals["loanPaidStTotal"].toString(),
                              isHeader: true),
                          _tableCell(totals["subscriptionTotal"].toString(),
                              isHeader: true),
                          _tableCell(totals["ltInstallmentTotal"].toString(),
                              isHeader: true),
                          _tableCell(totals["stInstallmentTotal"].toString(),
                              isHeader: true),
                          _tableCell(totals["interestTotal"].toString(),
                              isHeader: true),
                          _tableCell(totals["totalAmtPaidTotal"].toString(),
                              isHeader: true),
                          _tableCell(""), // LTCB empty
                          _tableCell(""), // STCB empty
                        ],
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  ConstrainedBox(
                    constraints: BoxConstraints(maxWidth: 616),
                    child: Table(
                      border: TableBorder.all(color: Colors.grey.shade400),
                      children: [
                        //headings
                        TableRow(
                          decoration:
                              BoxDecoration(color: Colors.grey.shade300),
                          children: [
                            _tableCell("Particular", isHeader: true),
                            _tableCell("OB", isHeader: true),
                            _tableCell("RECEIVED DURING THE YEAR",
                                isHeader: true),
                            _tableCell("PAID DURING THE YEAR", isHeader: true),
                            _tableCell("BALANCE AS ON 31-3-2025",
                                isHeader: true),
                          ],
                        ),
                        //subs
                        TableRow(
                          children: [
                            _tableCell("Subscription", isHeader: false),
                            _tableCell("$obSubs", isHeader: false),
                            _tableCell(totals['subscriptionTotal'].toString(),
                                isHeader: false),
                            _tableCell("0", isHeader: false),
                            _tableCell(
                                "${obSubs + (num.tryParse(totals['subscriptionTotal'].toString() ?? "0") ?? 0)}",
                                isHeader: false),
                          ],
                        ), //subs
                        TableRow(
                          children: [
                            _tableCell("Share", isHeader: false),
                            _tableCell("$obShare", isHeader: false),
                            _tableCell("0", isHeader: false),
                            _tableCell("0", isHeader: false),
                            _tableCell("0", isHeader: false),
                          ],
                        ), //subs
                        TableRow(
                          children: [
                            _tableCell("Lt Loan", isHeader: false),
                            _tableCell("$obLtLoan", isHeader: false),
                            _tableCell(totals['ltInstallmentTotal'].toString(),
                                isHeader: false),
                            _tableCell(totals['loanPaidLtTotal'].toString(),
                                isHeader: false),
                            _tableCell(
                                "${(obLtLoan - (num.tryParse(totals['ltInstallmentTotal'].toString() ?? "0") ?? 0) + (num.tryParse(totals['loanPaidLtTotal'].toString() ?? "0") ?? 0) ?? 0)}",
                                isHeader: false),
                          ],
                        ), //subs
                        TableRow(
                          children: [
                            _tableCell("St Loan", isHeader: false),
                            _tableCell("$obStLoan", isHeader: false),
                            _tableCell(totals['stInstallmentTotal'].toString(),
                                isHeader: false),
                            _tableCell(totals['loanPaidStTotal'].toString(),
                                isHeader: false),
                            _tableCell(
                                "  ${(obStLoan - (num.tryParse(totals['stInstallmentTotal'].toString() ?? "0") ?? 0) + (num.tryParse(totals['loanPaidStTotal'].toString() ?? "0") ?? 0) ?? 0)}",
                                isHeader: false),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _tableCell(String text, {bool isHeader = false}) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Text(
        text,
        style: TextStyle(
          fontWeight: isHeader ? FontWeight.bold : FontWeight.normal,
          fontSize: 14,
        ),
      ),
    );
  }

  // Make sure you have this utility function
  String getMonthName(int monthNumber) {
    const months = [
      "January",
      "February",
      "March",
      "April",
      "May",
      "June",
      "July",
      "August",
      "September",
      "October",
      "November",
      "December"
    ];
    if (monthNumber < 1 || monthNumber > 12) return "Unknown";
    return months[monthNumber - 1];
  }
}

class ShareCertificate extends StatefulWidget {
  const ShareCertificate({
    super.key,
    required this.ctrl,
    required this.share,
    required this.sctrl,
  });

  final HomeCtrl ctrl;
  final double share;
  final ScreenshotController sctrl;

  @override
  State<ShareCertificate> createState() => _ShareCertificateState();
}

class _ShareCertificateState extends State<ShareCertificate> {
  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: GetBuilder<HomeCtrl>(builder: (ctrl) {
        return Screenshot(
          controller: widget.sctrl,
          child: AspectRatio(
            aspectRatio: 1.355,
            child: Stack(
              children: [
                Positioned.fill(
                  child: Image(
                      fit: BoxFit.cover,
                      image: AssetImage(
                          "assets/images/FCIShareCertificatePlainWhite.png")),
                ),
                Padding(
                  padding:
                      EdgeInsets.symmetric(horizontal: 24.w, vertical: 24.h),
                  child: Column(children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text("Share Certificate No. 0000",
                            style: TextStyle(fontSize: 10.sp)),
                        Text("Share L.F. No. 0000",
                            style: TextStyle(fontSize: 10.sp)),
                      ],
                    ),
                    SizedBox(height: 12),
                    RichText(
                      textAlign: TextAlign.center,
                      text: TextSpan(
                        style: TextStyle(
                            fontFamily: 'Times New Roman',
                            color: Colors.black,
                            fontSize: 10.sp),
                        children: [
                          TextSpan(
                              text: 'SHARE CERTIFICATE\n',
                              style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 15.sp)),
                          TextSpan(
                            text:
                                'THE FOOD CORPORATION OF INDIA EMPLOYEE’S\nCO-OPERATIVE CREDIT SOCIETY LTD., BARODA.\n',
                            style: TextStyle(
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                          TextSpan(
                              text:
                                  '( Regd. No. S 2838 / v.3 / 7490 / 26-9-77 )\n\n',
                              style: TextStyle(
                                  fontSize: 8.sp, fontWeight: FontWeight.w400)),
                          TextSpan(
                              text:
                                  'Share Capital Rs.${ctrl.settings?.shareCapital}\n',
                              style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 15.sp)),
                          TextSpan(
                              text:
                                  'Divided into ${(num.tryParse(ctrl.settings?.shareCapital ?? "0") ?? 0) / (ctrl.settings?.shareValue ?? 0)} shares worth Rs.${(ctrl.settings?.shareValue ?? 0)}/- each.\n'),
                          TextSpan(
                            children: [
                              TextSpan(
                                  text:
                                      'This is to certify that Shri/Smt./Miss '),
                              TextSpan(
                                text: ctrl.users?.name.toUpperCase(),
                                style: TextStyle(fontWeight: FontWeight.bold),
                              ),
                              TextSpan(text: '\n'),
                            ],
                          ),
                          TextSpan(
                            children: [
                              TextSpan(
                                  text:
                                      'of Baroda is a registered member of this Society having ownership of '),
                              TextSpan(
                                text:
                                    '${(ctrl.users?.totalShares ?? 0) / (ctrl.settings?.shareValue ?? 0)}',
                                style: TextStyle(fontWeight: FontWeight.bold),
                              ),
                              TextSpan(
                                  text:
                                      ' share(s) bearing distinctive Nos. from __________ to __________.\n'),
                            ],
                          ),
                          TextSpan(
                            children: [
                              TextSpan(text: 'He/She has paid Rs.'),
                              TextSpan(
                                text: '${ctrl.users?.totalShares}',
                                style: TextStyle(fontWeight: FontWeight.bold),
                              ),
                              TextSpan(
                                  text:
                                      ' /- in full in accordance with the by-laws of the society,\n\n'),
                            ],
                          ),
                          TextSpan(
                              text:
                                  'This share certificate is signed and the seal of the society has been affixed On ${DateTime.now().day}th day of ${DateTime.now().month} ${DateTime.now().year} , Baroda.\n'),
                        ],
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 5),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Text(
                            "Dt.${DateFormat('dd - MM - yyyy').format(DateTime.now())}",
                            style: TextStyle(fontSize: 10.sp),
                          )
                        ],
                      ),
                    ),
                    SizedBox(height: 12),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        Text("Secretary", style: TextStyle(fontSize: 10.sp)),
                        Text("Chairman", style: TextStyle(fontSize: 10.sp)),
                      ],
                    ),
                  ]),
                ),
              ],
            ),
          ),
        );
      }),
    );
  }
}


  // const SizedBox(height: 15),
                      // slLoading
                      //     ? Center(
                      //         child: CircularProgressIndicator(
                      //         color: Colors.black,
                      //       ))
                      //     : ElevatedButton(
                      //         style: ButtonStyle(
                      //           fixedSize: WidgetStatePropertyAll(
                      //             Size(390.w, 60.h),
                      //           ),
                      //           shape: const WidgetStatePropertyAll(
                      //             ContinuousRectangleBorder(
                      //               borderRadius:
                      //                   BorderRadius.all(Radius.circular(16)),
                      //             ),
                      //           ),
                      //           backgroundColor: const WidgetStatePropertyAll(
                      //             Color.fromRGBO(29, 167, 95, 1),
                      //           ),
                      //         ),
                      //         onPressed: () async {
                      //           setState(() => slLoading = true);
                      //           final ledger = ctrl.subsidiaryLedger;
                      //           if (ledger == null) {
                      //             showAppSnackBar(
                      //                 "Subsidiary Ledger Report does not exist right now!");
                      //             setState(() => slLoading = false);
                      //             return;
                      //           }
                      //           try {
                      //             final image = await screenshotController
                      //                 .captureFromWidget(
                      //               SizedBox(
                      //                 width: 1080,
                      //                 height: 800,
                      //                 child: SubsidiaryLedgerPreviewWidget(
                      //                     ctrl: ctrl),
                      //               ),
                      //               pixelRatio: 3.0,
                      //             );
                      //             showDialog(
                      //                 barrierDismissible: false,
                      //                 context: context,
                      //                 builder:
                      //                     (_) => StatefulBuilder(builder:
                      //                             (context, setState2) {
                      //                           return Dialog(
                      //                               insetPadding:
                      //                                   EdgeInsets.symmetric(
                      //                                       horizontal: 20,
                      //                                       vertical: 40),
                      //                               shape:
                      //                                   RoundedRectangleBorder(
                      //                                 borderRadius:
                      //                                     BorderRadius.circular(
                      //                                         16),
                      //                               ),
                      //                               child: Container(
                      //                                   // padding:
                      //                                   //     EdgeInsets.all(0),
                      //                                   decoration: BoxDecoration(
                      //                                       color: Colors.white,
                      //                                       borderRadius:
                      //                                           BorderRadius
                      //                                               .circular(
                      //                                                   16)),
                      //                                   child:
                      //                                       SingleChildScrollView(
                      //                                     child: Column(
                      //                                         mainAxisSize:
                      //                                             MainAxisSize
                      //                                                 .min,
                      //                                         children: [
                      //                                           Row(
                      //                                             mainAxisAlignment:
                      //                                                 MainAxisAlignment
                      //                                                     .end,
                      //                                             children: [
                      //                                               IconButton(
                      //                                                 icon: const Icon(
                      //                                                     Icons
                      //                                                         .close,
                      //                                                     color:
                      //                                                         Colors.black),
                      //                                                 onPressed:
                      //                                                     () =>
                      //                                                         Navigator.of(context).pop(),
                      //                                               ),
                      //                                             ],
                      //                                           ),
                      //                                           SubsidiaryLedgerPreviewWidget(
                      //                                               ctrl: ctrl),
                      //                                           // Stack(
                      //                                           //   children: [
                      //                                           //     // ClipRRect(
                      //                                           //     //   borderRadius:
                      //                                           //     //       BorderRadius.circular(
                      //                                           //     //           16),
                      //                                           //     //   child: Image
                      //                                           //     //       .memory(
                      //                                           //     //     image,
                      //                                           //     //     fit: BoxFit
                      //                                           //     //         .cover,
                      //                                           //     //     height:
                      //                                           //     //         720,
                      //                                           //     //   ),
                      //                                           //     // ),
                      //                                           //   ],
                      //                                           // ),
                      //                                           slLoading
                      //                                               ? Center(
                      //                                                   child:
                      //                                                       CircularProgressIndicator(
                      //                                                   color: Colors
                      //                                                       .black,
                      //                                                 ))
                      //                                               : Padding(
                      //                                                   padding: const EdgeInsets
                      //                                                       .only(
                      //                                                       bottom:
                      //                                                           18),
                      //                                                   child: ElevatedButton(
                      //                                                       style: ButtonStyle(fixedSize: WidgetStatePropertyAll(Size(200.w, 40.h)), shape: const WidgetStatePropertyAll(ContinuousRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(16)))), backgroundColor: const WidgetStatePropertyAll(Color.fromRGBO(29, 167, 95, 1))),
                      //                                                       onPressed: () async {
                      //                                                         setState2(() => slLoading = true);
                      //                                                         try {
                      //                                                           final ledger = ctrl.subsidiaryLedger;
                      //                                                           if (ledger == null || ctrl.subsidiaryUserData?.monthlyEntries.isEmpty == true) {
                      //                                                             ScaffoldMessenger.of(context).showSnackBar(
                      //                                                               const SnackBar(content: Text('Subsidiary Ledger Report does not exist right now!')),
                      //                                                             );
                      //                                                             return;
                      //                                                           }
                      //                                                           final year = ledger.year.toString();
                      //                                                           // Build PDF document
                      //                                                           final pdf = pw.Document();
                      //                                                           // Table headers
                      //                                                           final headers = [
                      //                                                             'Month',
                      //                                                             'Date',
                      //                                                             'OBLT',
                      //                                                             'OBST',
                      //                                                             'Loan Paid LT',
                      //                                                             'Loan Paid ST',
                      //                                                             'Subscription',
                      //                                                             'LT Installment',
                      //                                                             'ST Installment',
                      //                                                             'Interest',
                      //                                                             'Total Amt Paid',
                      //                                                             'LTCB',
                      //                                                             'STCB',
                      //                                                           ];
                      //                                                           // Build table data rows from monthlyEntries
                      //                                                           final dataRows = ctrl.subsidiaryUserData!.monthlyEntries.map((entry) {
                      //                                                             return [
                      //                                                               getMonthName(entry.month),
                      //                                                               entry.subscription.toString(),
                      //                                                               entry.longTermInstallment.toString(),
                      //                                                               entry.shortTermInstallment.toString(),
                      //                                                               entry.interest.toString(),
                      //                                                             ];
                      //                                                           }).toList();
                      //                                                           pdf.addPage(
                      //                                                             pw.Page(
                      //                                                               pageFormat: PdfPageFormat.a4.landscape,
                      //                                                               build: (context) {
                      //                                                                 return pw.Column(
                      //                                                                   children: [
                      //                                                                     pw.Text('Subsidiary Ledger Report - $year', style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold)),
                      //                                                                     pw.SizedBox(height: 20),
                      //                                                                     pw.Table.fromTextArray(
                      //                                                                       headers: headers,
                      //                                                                       data: dataRows,
                      //                                                                       headerStyle: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                      //                                                                       cellAlignment: pw.Alignment.centerLeft,
                      //                                                                       headerDecoration: pw.BoxDecoration(color: PdfColors.grey300),
                      //                                                                     ),
                      //                                                                   ],
                      //                                                                 );
                      //                                                               },
                      //                                                             ),
                      //                                                           );
                      //                                                           final bytes = await pdf.save();
                      //                                                           if (kIsWeb) {
                      //                                                             // Share PDF directly on Web
                      //                                                             await Printing.sharePdf(
                      //                                                               bytes: bytes,
                      //                                                               filename: 'subsidiary_ledger_$year.pdf',
                      //                                                             );
                      //                                                           } else {
                      //                                                             // Android/iOS save and share
                      //                                                             final dir = io.Platform.isIOS ? await getLibraryDirectory() : await getExternalStorageDirectory();
                      //                                                             final path = '${dir!.path}/subsidiary_ledger_$year.pdf';
                      //                                                             final file = io.File(path);
                      //                                                             await file.writeAsBytes(bytes);
                      //                                                             final xFile = XFile(path, name: 'subsidiary_ledger_$year.pdf');
                      //                                                             await Share.shareXFiles([
                      //                                                               xFile
                      //                                                             ]);
                      //                                                           }
                      //                                                         } catch (e) {
                      //                                                           debugPrint("Subsidiary Ledger PDF Error: $e");
                      //                                                           ScaffoldMessenger.of(context).showSnackBar(
                      //                                                             SnackBar(content: Text("Error exporting PDF: $e")),
                      //                                                           );
                      //                                                         } finally {
                      //                                                           setState2(() => slLoading = false);
                      //                                                         }
                      //                                                       },
                      //                                                       child: Text("Download", style: TextStyle(color: Colors.white))),
                      //                                                 )
                      //                                         ]),
                      //                                   )));
                      //                         }));
                      //           } catch (e) {
                      //             showAppSnackBar(
                      //                 "Error generating preview: $e");
                      //           }
                      //           setState(() => slLoading = false);
                      //         },
                      //         child: Text(
                      //           "Download Subsidiary Ledger Report",
                      //           style: TextStyle(
                      //               color: Colors.white, fontSize: 15.sp),
                      //         ),
                      //       ),
                      // const SizedBox(height: 15),
                      // scLoading
                      //     ? Center(
                      //         child: CircularProgressIndicator(
                      //         color: Colors.black,
                      //       ))
                      //     : ElevatedButton(
                      //         style: ButtonStyle(
                      //             fixedSize:
                      //                 WidgetStatePropertyAll(Size(400.w, 60.h)),
                      //             shape: const WidgetStatePropertyAll(
                      //                 ContinuousRectangleBorder(
                      //                     borderRadius: BorderRadius.all(
                      //                         Radius.circular(16)))),
                      //             backgroundColor: const WidgetStatePropertyAll(
                      //                 Color.fromRGBO(29, 167, 95, 1))),
                      //         onPressed: () async {
                      //           setState(() => scLoading = true);
                      //           final image = await screenshotController
                      //               .captureFromWidget(
                      //             ShareCertificate(
                      //               ctrl: ctrl,
                      //               share: (ctrl.users?.totalShares ?? 0)
                      //                   .toDouble(),
                      //               sctrl: screenshotController,
                      //             ),
                      //             pixelRatio: 3.0,
                      //           );
                      //           showDialog(
                      //               barrierDismissible: false,
                      //               context: context,
                      //               builder:
                      //                   (context) => StatefulBuilder(
                      //                           builder: (context, setState2) {
                      //                         return Dialog(
                      //                             insetPadding:
                      //                                 EdgeInsets.symmetric(
                      //                                     horizontal: 20.w,
                      //                                     vertical: 40.h),
                      //                             shape: RoundedRectangleBorder(
                      //                                 borderRadius:
                      //                                     BorderRadius.circular(
                      //                                         16)),
                      //                             child: Container(
                      //                                 padding:
                      //                                     EdgeInsets.all(16),
                      //                                 decoration: BoxDecoration(
                      //                                     color: Colors.white,
                      //                                     borderRadius:
                      //                                         BorderRadius
                      //                                             .circular(
                      //                                                 16)),
                      //                                 child: Column(
                      //                                     mainAxisSize:
                      //                                         MainAxisSize.min,
                      //                                     children: [
                      //                                       Stack(children: [
                      //                                         ClipRRect(
                      //                                             borderRadius:
                      //                                                 BorderRadius
                      //                                                     .circular(
                      //                                                         16),
                      //                                             child: Image.memory(
                      //                                                 image,
                      //                                                 fit: BoxFit
                      //                                                     .contain)),
                      //                                         Positioned(
                      //                                             right: 0,
                      //                                             top: -14,
                      //                                             child:
                      //                                                 IconButton(
                      //                                               icon: const Icon(
                      //                                                   Icons
                      //                                                       .close,
                      //                                                   color: Colors
                      //                                                       .black),
                      //                                               onPressed: () =>
                      //                                                   Navigator.of(context)
                      //                                                       .pop(),
                      //                                             ))
                      //                                       ]),
                      //                                       Padding(
                      //                                           padding:
                      //                                               const EdgeInsets
                      //                                                   .symmetric(
                      //                                                   vertical:
                      //                                                       12),
                      //                                           child: scLoading
                      //                                               ? SizedBox(
                      //                                                   height:
                      //                                                       20,
                      //                                                   width:
                      //                                                       20,
                      //                                                   child:
                      //                                                       CircularProgressIndicator(
                      //                                                     color:
                      //                                                         Colors.black,
                      //                                                   ))
                      //                                               : ElevatedButton(
                      //                                                   style: ButtonStyle(
                      //                                                       fixedSize:
                      //                                                           WidgetStatePropertyAll(Size(200.w, 40.h)),
                      //                                                       shape: const WidgetStatePropertyAll(ContinuousRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(16)))),
                      //                                                       backgroundColor: const WidgetStatePropertyAll(Color.fromRGBO(29, 167, 95, 1))),
                      //                                                   onPressed: scLoading
                      //                                                       ? null
                      //                                                       : () async {
                      //                                                           setState2(() {
                      //                                                             scLoading = true;
                      //                                                           });
                      //                                                           await shareCertificateOnPressed(ctrl, (ctrl.users?.totalShares ?? 0).toDouble(), screenshotController, context, setState2);
                      //                                                           Navigator.of(context).pop();
                      //                                                           setState2(() {
                      //                                                             scLoading = false;
                      //                                                           });
                      //                                                         },
                      //                                                   child: Text(
                      //                                                     "Download",
                      //                                                     style:
                      //                                                         TextStyle(color: Colors.white),
                      //                                                   )))
                      //                                     ])));
                      //                       }));
                      //           setState(() => scLoading = false);
                      //         },
                      //         child: Text(
                      //           "Download Share Certificate",
                      //           style: TextStyle(
                      //               color: Colors.white, fontSize: 15.sp),
                      //         )),
                     