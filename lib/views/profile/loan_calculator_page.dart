import 'dart:math';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:foodcorp/controller/homectrl.dart';
import 'package:foodcorp/views/loan_selection_page.dart';
import 'package:get/get.dart';

class LoanCalculatorPage extends StatefulWidget {
  const LoanCalculatorPage({super.key});

  @override
  State<LoanCalculatorPage> createState() => _LoanState();
}

class _LoanState extends State<LoanCalculatorPage> {
  SelectedLoan? selectedLoan = SelectedLoan.longtermloan;

  final TextEditingController loanAmtCtrl = TextEditingController();
  final TextEditingController intRateCtrl = TextEditingController();
  final TextEditingController loanPeriodCtrl = TextEditingController();

  @override
  void initState() {
    super.initState();
    final ctrl = Get.find<HomeCtrl>();
    intRateCtrl.text = (selectedLoan == SelectedLoan.longtermloan
                ? ctrl.settings?.ltloanInterest
                : ctrl.settings?.stloanInterest)
            ?.toString() ??
        '0';
  }

  void _showDialog(BuildContext? contex) {
    if (loanAmtCtrl.text.isEmpty ||
        intRateCtrl.text.isEmpty ||
        loanPeriodCtrl.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text("Please fill all the fields"),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    double loanAmount = double.parse(loanAmtCtrl.text.replaceAll(',', ''));
    int months = int.parse(loanPeriodCtrl.text);
// Convert annual percent to monthly fraction
    double annualRate = double.parse(intRateCtrl.text);
    double monthlyRate = annualRate / 12 / 100;

// EMI formula for reducing balance
    double emi = loanAmount *
        monthlyRate *
        (pow(1 + monthlyRate, months)) /
        (pow(1 + monthlyRate, months) - 1);

// Calculate total payment and total interest
    double totalPayment = emi * months;
    double totalInterest = totalPayment - loanAmount;

    showModalBottomSheet(
      context: contex!,
      builder: (BuildContext context) {
        return SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  title: const Text("Loan Amount"),
                  trailing: Text(loanAmtCtrl.text.replaceAll(',', '')),
                ),
                ListTile(
                  title: const Text("Interest Rate"),
                  trailing: Text("$annualRate%"),
                ),
                ListTile(
                  title: const Text("Loan Period"),
                  trailing: Text("$months months"),
                ),
                ListTile(
                  title: const Text("Monthly Payment (EMI)"),
                  trailing: Text(emi.ceil().toString()),
                ),
                ListTile(
                  title: const Text("Total Interest"),
                  trailing: Text(totalInterest.ceil().toString()),
                ),
                ListTile(
                  title: const Text("Total Payment"),
                  trailing: Text(totalPayment.ceil().toString()),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        title: const Text("Loan Calculator"),
      ),
      bottomNavigationBar: SafeArea(
        child: Container(
          padding: const EdgeInsets.all(20),
          child: ElevatedButton(
            style: ButtonStyle(
              fixedSize: WidgetStatePropertyAll(
                Size(MediaQuery.sizeOf(context).width, 60),
              ),
              shape: const WidgetStatePropertyAll(
                ContinuousRectangleBorder(
                  borderRadius: BorderRadius.all(Radius.circular(16)),
                ),
              ),
              backgroundColor: const WidgetStatePropertyAll(
                Color.fromRGBO(29, 167, 95, 1),
              ),
            ),
            onPressed: () => _showDialog(context),
            child: const Text(
              "Calculate",
              style: TextStyle(color: Colors.white, fontSize: 18),
            ),
          ),
        ),
      ),
      body: GetBuilder<HomeCtrl>(builder: (ctrl) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: CupertinoSlidingSegmentedControl<SelectedLoan>(
                  thumbColor: Colors.green.shade300,
                  groupValue: selectedLoan,
                  children: {
                    SelectedLoan.longtermloan: Text(
                      "Long Term Loan",
                      style: TextStyle(
                        color: selectedLoan == SelectedLoan.longtermloan
                            ? Colors.white
                            : Colors.black,
                      ),
                    ),
                    SelectedLoan.shorttermloan: Text(
                      "Short Term Loan",
                      style: TextStyle(
                        color: selectedLoan == SelectedLoan.shorttermloan
                            ? Colors.white
                            : Colors.black,
                      ),
                    ),
                  },
                  onValueChanged: (SelectedLoan? value) {
                    setState(() {
                      selectedLoan = value;
                      intRateCtrl.text =
                          (selectedLoan == SelectedLoan.longtermloan
                                      ? ctrl.settings?.ltloanInterest
                                      : ctrl.settings?.stloanInterest)
                                  ?.toString() ??
                              '0';
                    });
                  },
                ),
              ),
              const SizedBox(height: 20),
              TextField(
                keyboardType: TextInputType.number,
                controller: loanAmtCtrl,
                textInputAction: TextInputAction.next,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  labelText: 'Enter Loan Amount',
                  suffixText: "₹",
                ),
              ),
              const SizedBox(height: 20),
              TextField(
                style: TextStyle(color: Colors.black),
                enabled: false,
                controller: intRateCtrl,
                keyboardType: TextInputType.number,
                textInputAction: TextInputAction.next,
                decoration: InputDecoration(
                  border: const OutlineInputBorder(),
                  labelText:
                      "Interest Rate (${selectedLoan == SelectedLoan.longtermloan ? "Long Term" : "Short Term"})",
                  labelStyle: TextStyle(color: Colors.black),
                  suffixIcon: const Icon(
                    Icons.percent_outlined,
                    color: Colors.black,
                  ),
                ),
              ),
              const SizedBox(height: 20),
              TextField(
                controller: loanPeriodCtrl,
                keyboardType: TextInputType.number,
                textInputAction: TextInputAction.done,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  labelText: 'Loan Period (Month)',
                  suffixIcon: Icon(Icons.calendar_month_outlined),
                ),
              ),
            ],
          ),
        );
      }),
    );
  }
}
