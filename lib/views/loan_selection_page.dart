import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:foodcorp/shared/router.dart';
import 'package:foodcorp/views/Loan/ltloanform.dart';
import 'package:go_router/go_router.dart';
import 'Loan/stloanform.dart';

class LoanSelectionPage extends StatefulWidget {
  const LoanSelectionPage({super.key});

  @override
  State<LoanSelectionPage> createState() => _LoanSelectionPageState();
}

enum SelectedLoan { longtermloan, shorttermloan }

SelectedLoan? selectedLoan = SelectedLoan.longtermloan;

class _LoanSelectionPageState extends State<LoanSelectionPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        leading: IconButton(
            focusColor: Colors.transparent,
            highlightColor: Colors.transparent,
            onPressed: () {
              context.go(Routes.dashboard);
            },
            icon: Icon(Icons.arrow_back_ios_rounded)),
        title: Column(
          children: [
            CupertinoSlidingSegmentedControl<SelectedLoan>(
              thumbColor: Colors.green.shade300,
              groupValue: selectedLoan,
              children: {
                SelectedLoan.longtermloan: Text(
                  "Long Term Loan",
                  style: TextStyle(
                    color: selectedLoan == SelectedLoan.longtermloan
                        ? Colors.white
                        : Colors.black,
                  ),
                ),
                SelectedLoan.shorttermloan: Text(
                  "Emergency Loan",
                  style: TextStyle(
                    color: selectedLoan == SelectedLoan.shorttermloan
                        ? Colors.white
                        : Colors.black,
                  ),
                ),
              },
              onValueChanged: (SelectedLoan? value) {
                setState(() {
                  selectedLoan = value;
                });
              },
            ),
          ],
        ),
      ),
      body: Column(
        children: [
          // const SizedBox(height: 60),
          // Row(
          //   mainAxisAlignment: MainAxisAlignment.start,
          //   children: [],
          // ),
          Expanded(
            child: selectedLoan == SelectedLoan.longtermloan
                ? const Ltloanform()
                : const Stloanform(),
          ),
        ],
      ),
    );
  }
}
