import 'package:cloud_firestore/cloud_firestore.dart';

class NewUserApplicationModel {
  final String docId;
  final DateTime createdAt;
  final String name;
  final num cpfNo;
  final String? districtoffice;
  final String email;
  final String? phoneNo;
  // final num? settlement;
  // final num? totalSubs; // Subs = Subscription
  // final num? totalSubsInt; // Int = Interest
  // final num? ltLoansDue; // lt = longterm
  // final num? stLoansDue; // st = shortTerm
  // final num? totalLtLoans;
  // final num? totalStLoans;
  // final num? totalLtIntPaid;
  // final num? totalStIntPaid;
  // final num? totalDivident;
  // final num? totalShares;
  final DateTime? registrationDate;
  final num employeeNo;
  final String currentAddress;
  final String permanentAddress;
  final String documents;

  final DateTime? rejectionDate;
  final String? rejectionReason;
  final bool approved;
  final bool isNew;

  final String bankAcName;
  final String bankName;
  final String ifscCode;
  final num bankAcNo;
  final bool archived;

  final num? momento;
  final String? nomineeName;
  final String? nomineeRelation;

  NewUserApplicationModel({
    required this.docId,
    required this.createdAt,
    required this.name,
    required this.cpfNo,
    required this.districtoffice,
    required this.email,
    required this.phoneNo,
    // required this.settlement,
    // required this.totalSubs,
    // required this.totalSubsInt,
    // required this.ltLoansDue,
    // required this.stLoansDue,
    // required this.totalLtLoans,
    // required this.totalStLoans,
    // required this.totalLtIntPaid,
    // required this.totalStIntPaid,
    // required this.totalDivident,
    // required this.totalShares,
    required this.registrationDate,
    required this.employeeNo,
    required this.currentAddress,
    required this.permanentAddress,
    required this.documents,
    this.rejectionDate,
    this.rejectionReason,
    required this.approved,
    required this.isNew,
    required this.bankAcName,
    required this.bankName,
    required this.ifscCode,
    required this.bankAcNo,
    required this.archived,
    required this.momento,
    required this.nomineeName,
    required this.nomineeRelation,
  });

  // Factory constructor to create an instance from JSON
  factory NewUserApplicationModel.fromJson(Map<String, dynamic> json) {
    return NewUserApplicationModel(
        docId: json['docId'],
        createdAt: DateTime.parse(json['createdAt']),
        name: json['name'],
        cpfNo: json['cpfNo'],
        districtoffice: json['districtoffice'],
        email: json['email'],
        phoneNo: json['phonenumber'],
        // settlement: json['settlement'],
        // totalSubs: json['totalSubs'],
        // totalSubsInt: json['totalSubsInt'],
        // ltLoansDue: json['ltLoansDue'],
        // stLoansDue: json['stLoansDue'],
        // totalLtLoans: json['totalLtLoans'],
        // totalStLoans: json['totalStLoans'],
        // totalLtIntPaid: json['totalLtIntPaid'],
        // totalStIntPaid: json['totalStIntPaid'],
        // totalDivident: json['totalDivident'],
        // totalShares: json['totalShares'],
        registrationDate: json['registrationDate'] != null
            ? DateTime.parse(json['registrationDate'])
            : null,
        employeeNo: json['employeeNo'],
        currentAddress: json['currentAddress'],
        permanentAddress: json['permanentAddress'],
        documents: json['documents'],
        approved: json['approved'],
        isNew: json['isNew'],
        rejectionDate: json['rejectionDate'],
        rejectionReason: json['rejectionReason'],
        bankAcName: json['bankAcName'],
        bankName: json['bankName'],
        ifscCode: json['ifscCode'],
        bankAcNo: json['bankAcNo'],
        archived: json['archived'],
        momento: json['momento'],
        nomineeName: json['nomineeName'],
        nomineeRelation: json['nomineeRelation']);
  }

  // Convert this instance to a map (JSON format)
  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'createdAt': createdAt.toIso8601String(),
      'name': name,
      'cpfNo': cpfNo,
      'districtoffice': districtoffice,
      'email': email,
      'phonenumber': phoneNo,
      // 'settlement': settlement,
      // 'totalSubs': totalSubs,
      // 'totalSubsInt': totalSubsInt,
      // 'ltLoansDue': ltLoansDue,
      // 'stLoansDue': stLoansDue,
      // 'totalLtLoans': totalLtLoans,
      // 'totalStLoans': totalStLoans,
      // 'totalLtIntPaid': totalLtIntPaid,
      // 'totalStIntPaid': totalStIntPaid,
      // 'totalDivident': totalDivident,
      // 'totalShares': totalShares,
      'registrationDate': registrationDate,
      'employeeNo': employeeNo,
      'currentAddress': currentAddress,
      'permanentAddress': permanentAddress,
      'documents': documents,
      'rejectionDate': rejectionDate,
      'rejectionReason': rejectionReason,
      'approved': approved,
      'isNew': isNew,

      'bankAcName': bankAcName,
      'bankName': bankName,
      'ifscCode': ifscCode,
      'archived': archived,
      'bankAcNo': bankAcNo,
      'momento': momento,
      'nomineeName': nomineeName,
      'nomineeRelation': nomineeRelation,
    };
  }

  // Convert a Firestore DocumentSnapshot to a UserModel
  factory NewUserApplicationModel.fromSnap(DocumentSnapshot snap) {
    var data = snap.data() as Map<String, dynamic>;
    return NewUserApplicationModel(
      docId: snap.id,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      name: data['name'],
      cpfNo: data['cpfNo'],
      districtoffice: data['districtoffice'],
      email: data['email'],
      phoneNo: data['phonenumber'],
      // settlement: data['settlement'],
      // totalSubs: data['totalSubs'],
      // totalSubsInt: data['totalSubsInt'],
      // ltLoansDue: data['ltLoansDue'],
      // stLoansDue: data['stLoansDue'],
      // totalLtLoans: data['totalLtLoans'],
      // totalStLoans: data['totalStLoans'],
      // totalLtIntPaid: data['totalLtIntPaid'],
      // totalStIntPaid: data['totalStIntPaid'],
      // totalDivident: data['totalDivident'],
      // totalShares: data['totalShares'],
      registrationDate: data['registrationDate'] != null
          ? (data['registrationDate'] as Timestamp).toDate()
          : null,
      employeeNo: data['employeeNo'],
      currentAddress: data['currentAddress'],
      permanentAddress: data['permanentAddress'],
      documents: data['documents'],
      approved: data['approved'],
      isNew: data['isNew'],
      rejectionDate: data['rejectionDate'],
      rejectionReason: data['rejectionReason'],

      bankAcName: data['bankAcName'],
      bankName: data['bankName'],
      ifscCode: data['ifscCode'],
      bankAcNo: data['bankAcNo'],
      archived: data['archived'],
      momento: data['momento'],
      nomineeName: data['nomineeName'],
      nomineeRelation: data['nomineeRelation'],
    );
  }

  // Convert this UserModel to a Firestore-compatible map
  Map<String, dynamic> toSnap() {
    return {
      'createdAt': Timestamp.fromDate(createdAt),
      'name': name,
      'cpfNo': cpfNo,
      'districtoffice': districtoffice,
      'email': email,
      'phonenumber': phoneNo,
      // 'settlement': settlement,
      // 'totalSubs': totalSubs,
      // 'totalSubsInt': totalSubsInt,
      // 'ltLoansDue': ltLoansDue,
      // 'stLoansDue': stLoansDue,
      // 'totalLtLoans': totalLtLoans,
      // 'totalStLoans': totalStLoans,
      // 'totalLtIntPaid': totalLtIntPaid,
      // 'totalStIntPaid': totalStIntPaid,
      // 'totalDivident': totalDivident,
      // 'totalShares': totalShares,
      'registrationDate': registrationDate != null
          ? Timestamp.fromDate(registrationDate!)
          : null,
      'employeeNo': employeeNo,
      'currentAddress': currentAddress,
      'permanentAddress': permanentAddress,
      'documents': documents,
      'approved': approved,
      'isNew': isNew,
      'rejectionDate': rejectionDate,
      'rejectionReason': rejectionReason,

      'bankAcName': bankAcName,
      'bankName': bankName,
      'ifscCode': ifscCode,
      'bankAcNo': bankAcNo,
      'archived': archived,
      'momento': momento,
      'nomineeName': nomineeName,
      'nomineeRelation': nomineeRelation,
    };
  }
}
