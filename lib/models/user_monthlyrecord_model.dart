import 'package:cloud_firestore/cloud_firestore.dart';

//25
class UserMonthlyRecordModel {
  final String docId;
  final int? selectedyear;
  final int? selectedmonth;
  // final String? doId;
  final num cpfNo;
  final String name;
  final String districtoffice;
  final num? obLt;
  final num? obSt;
  final num? loanPaidLt;
  final num? loanPaidst;
  final num? loanTotal;
  final num? subs;
  final num? ltInstallment;
  final num? stInstallment;
  final num? interest;
  final num? total;
  final num? installmentRec;
  final DateTime? installmentRecDate;
  final num? ltCb;
  final num? stCb;
  final num subscriptionPaid;
  final num longTermInstalmentPaid;
  final num shortTermInstalmentPaid;
  final num longTermInterestPaid;
  final num shortTermInterestPaid;
  // final num totalReceived;
  final bool isPaid;
  final String? status;
  final num? dues;
  final num? penalty;
  final num shareValue;
  final num? societySubsPayout;
  final num? societySharesPayout;

  final num penaltyPaid;

  UserMonthlyRecordModel({
    required this.docId,
    this.status,
    this.selectedyear,
    this.selectedmonth,
    // this.doId,
    required this.cpfNo,
    required this.name,
    required this.districtoffice,
    this.obLt,
    this.obSt,
    this.loanPaidLt,
    this.loanPaidst,
    this.loanTotal,
    this.subs,
    this.ltInstallment,
    this.stInstallment,
    this.interest,
    this.total,
    this.installmentRec,
    this.installmentRecDate,
    this.ltCb,
    this.stCb,
    required this.subscriptionPaid,
    required this.longTermInstalmentPaid,
    required this.shortTermInstalmentPaid,
    required this.longTermInterestPaid,
    required this.shortTermInterestPaid,
    // required this.totalReceived,
    required this.isPaid,
    required this.dues,
    required this.penalty,
    required this.penaltyPaid,
    required this.societySubsPayout,
    required this.societySharesPayout,
    required this.shareValue,

    // required this.docID,
    // required this.createdAt,
    // required this.uid,
    // required this.subsInt,
    // required this.subsIntRate,
    // required this.loanIds,
    // required this.stIntRate,
    // required this.ltIntRate,
    // required this.isPaid,
    // required this.totalShares,
    // required this.totalShareDividend,
    // required this.shareDividend,
    // required this.dividendRate,
    // required this.previousMonthCB,
    // this.paidOn, // Nullable
    // required this.comment,
    // required this.recovery, // Recovery instance
    // required this.receipt, // Receipt instance
    // required this.settlement,
    // required this.updatedAt,
  });

  factory UserMonthlyRecordModel.fromJson(
      Map<String, dynamic> json, String docId) {
    return UserMonthlyRecordModel(
      docId: docId,
      selectedyear: json['selectedyear'],
      selectedmonth: json['selectedmonth'],
      // doId: json['doId'],
      cpfNo: json['cpfNo'],
      name: json['name'],
      districtoffice: json['districtoffice'],
      obLt: json['obLt'],
      obSt: json['obSt'],
      loanPaidLt: json['loanPaidLt'],
      loanPaidst: json['loanPaidst'],
      loanTotal: json['loanTotal'],
      subs: json['subs'],
      ltInstallment: json['ltInstallment'],
      stInstallment: json['stInstallment'],
      interest: json['interest'],
      total: json['total'],
      installmentRec: json['installmentRec'],
      installmentRecDate: json['installmentRecDate'] == null
          ? null
          : (json['installmentRecDate'] as Timestamp).toDate(),

      ltCb: json['ltCb'],
      stCb: json['stCb'],
      subscriptionPaid: json['subscriptionPaid'],
      longTermInstalmentPaid: json['longTermInstalmentPaid'],
      shortTermInstalmentPaid: json['shortTermInstalmentPaid'],
      longTermInterestPaid: json['longTermInterestPaid'],
      shortTermInterestPaid: json['shortTermInterestPaid'],
      // totalReceived: json['totalReceived'],
      isPaid: json['isPaid'],
      status: json['status'],
      dues: json['dues'],
      penalty: json['penalty'],
      penaltyPaid: json['penaltyPaid'],
      shareValue: json['shareValue'],
      societySubsPayout: json['societySubsPayout'],
      societySharesPayout: json['societySharesPayout'],

      // docID: json['docID'],
      // createdAt: DateTime.parse(json['createdAt']),
      // uid: json['uid'],
      // subsInt: json['subscriptionInt'],
      // subsIntRate: json['subsIntRate'],
      // loanIds: List<String>.from(json['loanIds']), // Assuming loanIds is a list
      // stIntRate: json['stIntRate'],
      // ltIntRate: json['ltIntRate'],
      // isPaid: json['isPaid'],
      // totalShares: json['totalShares'],
      // totalShareDividend: json['totalShareDividend'],
      // shareDividend: json['shareDividend'],
      // dividendRate: json['dividendRate'], // Percent
      // previousMonthCB: json['previousMonthCB'],
      // paidOn: json['paidOn'] != null
      //     ? DateTime.parse(json['paidOn'])
      //     : null, // Nullable
      // comment: json['comment'],
      // recovery: RecoveryModel.fromJson(json['recovery']), // Parse Recovery
      // receipt: ReceiptModel.fromJson(json['receipt']), // Parse Receipt
      // settlement: json['settlement'],
      // updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'selectedyear': selectedyear,
      'selectedmonth': selectedmonth,
      // 'doId': doId,
      'cpfNo': cpfNo,
      'name': name,
      'districtoffice': districtoffice,
      'obLt': obLt,
      'obSt': obSt,
      'loanPaidLt': loanPaidLt,
      'loanPaidst': loanPaidst,
      'loanTotal': loanTotal,
      'subs': subs,
      'ltInstallment': ltInstallment,
      'stInstallment': stInstallment,
      'interest': interest,
      'total': total,
      'installmentRec': installmentRec,
      'installmentRecDate': installmentRecDate?.toIso8601String(),
      'ltCb': ltCb,
      'stCb': stCb,
      'subscriptionPaid': subscriptionPaid,
      'longTermInstalmentPaid': longTermInstalmentPaid,
      'shortTermInstalmentPaid': shortTermInstalmentPaid,
      'longTermInterestPaid': longTermInterestPaid,
      'shortTermInterestPaid': shortTermInterestPaid,
      // 'totalReceived': totalReceived,
      'isPaid': isPaid,
      'status': status,
      'dues': dues,
      'penalty': penalty,
      'penaltyPaid': penaltyPaid,
      'shareValue': shareValue,
      'societySubsPayout': societySubsPayout,
      'societySharesPayout': societySharesPayout,
      // 'docID': docID,
      // 'createdAt': createdAt.toIso8601String(),
      // 'uid': uid,
      // 'subscriptionInt': subsInt,
      // 'subsIntRate': subsIntRate, // Percent
      // 'loanIds': loanIds,
      // 'stIntRate': stIntRate,
      // 'ltIntRate': ltIntRate,
      // 'isPaid': isPaid,
      // 'totalShares': totalShares,
      // 'totalShareDividend': totalShareDividend,
      // 'shareDividend': shareDividend,
      // 'dividendRate': dividendRate, // Percent
      // 'previousMonthCB': previousMonthCB,
      // 'paidOn': paidOn?.toIso8601String(), // Nullable
      // 'comment': comment,
      // 'recovery': recovery.toJson(), // Convert Recovery to JSON
      // 'receipt': receipt?.toJson(), // Convert Receipt to JSON
      // 'settlement': settlement,
      // 'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory UserMonthlyRecordModel.fromSnap(DocumentSnapshot snap) {
    final data = snap.data() as Map<String, dynamic>;

    return UserMonthlyRecordModel(
      docId: snap.id,
      selectedyear: data['selectedyear'],
      selectedmonth: data['selectedmonth'],
      // doId: data['doId'],
      cpfNo: data['cpfNo'],
      name: data['name'],
      districtoffice: data['districtoffice'],
      obLt: data['obLt'],
      obSt: data['obSt'],
      loanPaidLt: data['loanPaidLt'],
      loanPaidst: data['loanPaidst'],
      loanTotal: data['loanTotal'],
      subs: data['subs'],
      ltInstallment: data['ltInstallment'],
      stInstallment: data['stInstallment'],
      interest: data['interest'],
      total: data['total'],
      installmentRec: data['installmentRec'],
      installmentRecDate: data['installmentRecDate'] == null
          ? null
          : (data['installmentRecDate'] as Timestamp).toDate(),
      ltCb: data['ltCb'],
      stCb: data['stCb'],
      subscriptionPaid: data['subscriptionPaid'],
      longTermInstalmentPaid: data['longTermInstalmentPaid'],
      shortTermInstalmentPaid: data['shortTermInstalmentPaid'],
      longTermInterestPaid: data['longTermInterestPaid'],
      shortTermInterestPaid: data['shortTermInterestPaid'],
      // totalReceived: data['totalReceived'],
      isPaid: data['isPaid'],
      status: data['status'],
      dues: data['dues'] ?? 0,
      penalty: data['penalty'] ?? 0,
      penaltyPaid: data['penaltyPaid'] ?? 0,
      shareValue: data['shareValue'] ?? 0,
      societySubsPayout: data['societySubsPayout'],
      societySharesPayout: data['societySharesPayout'],
      // docID: snap.id,
      // createdAt: (data['createdAt'] as Timestamp).toDate(),
      // uid: data['uid'],
      // subsInt: data['subscriptionInt'],
      // subsIntRate: data['subsIntRate'],
      // loanIds: List<String>.from(data['loanIds']),
      // stIntRate: data['stIntRate'],
      // ltIntRate: data['ltIntRate'],
      // isPaid: data['isPaid'],
      // totalShares: data['totalShares'],
      // totalShareDividend: data['totalShareDividend'],
      // shareDividend: data['shareDividend'],
      // dividendRate: data['dividendRate'],
      // previousMonthCB: data['previousMonthCB'],
      // paidOn: data['paidOn'] != null
      //     ? (data['paidOn'] as Timestamp).toDate()
      //     : null, // Nullable DateTime
      // comment: data['comment'],
      // recovery: RecoveryModel.fromJson(data['recovery']),
      // receipt: data['receipt'] == null
      //     ? null
      //     : ReceiptModel.fromJson(data['receipt'] ?? {}),
      // settlement: data['settlement'],
      // updatedAt: (data['updatedAt'] as Timestamp).toDate(),
    );
  }
}

// class InputUserMonthlyRecordModel {
//   String docID;
//   DateTime createdAt;
//   String uid;
//   num subsInt;
//   num subsIntRate; // Percent
//   List<String> loanIds; // Assuming loanIds is a list of strings
//   num stIntRate;
//   num ltIntRate;
//   bool isPaid;
//   num totalShares;
//   num totalShareDividend;
//   num shareDividend;
//   num dividendRate; // Percent
//   num previousMonthCB;
//   DateTime? paidOn; // Nullable
//   String comment;
//   InputRecoveryModel recovery; // Integrated Recovery model
//   Receipt receipt; // Integrated Receipt model
//   num settlement; // Current month more/less amount
//   DateTime updatedAt;

//   InputUserMonthlyRecordModel({
//     required this.docID,
//     required this.createdAt,
//     required this.uid,
//     required this.subsInt,
//     required this.subsIntRate,
//     required this.loanIds,
//     required this.stIntRate,
//     required this.ltIntRate,
//     required this.isPaid,
//     required this.totalShares,
//     required this.totalShareDividend,
//     required this.shareDividend,
//     required this.dividendRate,
//     required this.previousMonthCB,
//     this.paidOn, // Nullable
//     required this.comment,
//     required this.recovery, // Recovery instance
//     required this.receipt, // Receipt instance
//     required this.settlement,
//     required this.updatedAt,
//   });
//   Map<String, dynamic> toJson() {
//     return {
//       'docID': docID,
//       'createdAt': createdAt.toIso8601String(),
//       'uid': uid,
//       'subscriptionInt': subsInt,
//       'subsIntRate': subsIntRate, // Percent
//       'loanIds': loanIds,
//       'stIntRate': stIntRate,
//       'ltIntRate': ltIntRate,
//       'isPaid': isPaid,
//       'totalShares': totalShares,
//       'totalShareDividend': totalShareDividend,
//       'shareDividend': shareDividend,
//       'dividendRate': dividendRate, // Percent
//       'previousMonthCB': previousMonthCB,
//       'paidOn': paidOn?.toIso8601String(), // Nullable
//       'comment': comment,
//       'recovery': recovery.toMap(), // Convert Recovery to JSON
//       'receipt': receipt.toJson(), // Convert Receipt to JSON
//       'settlement': settlement,
//       'updatedAt': updatedAt.toIso8601String(),
//     };
//   }

//   factory InputUserMonthlyRecordModel.fromJson(Map<String, dynamic> json) {
//     return InputUserMonthlyRecordModel(
//       docID: json['docID'],
//       createdAt: DateTime.parse(json['createdAt']),
//       uid: json['uid'],
//       subsInt: json['subscriptionInt'],
//       subsIntRate: json['subsIntRate'],
//       loanIds: List<String>.from(json['loanIds']), // Assuming loanIds is a list
//       stIntRate: json['stIntRate'],
//       ltIntRate: json['ltIntRate'],
//       isPaid: json['isPaid'],
//       totalShares: json['totalShares'],
//       totalShareDividend: json['totalShareDividend'],
//       shareDividend: json['shareDividend'],
//       dividendRate: json['dividendRate'], // Percent
//       previousMonthCB: json['previousMonthCB'],
//       paidOn: json['paidOn'] != null
//           ? DateTime.parse(json['paidOn'])
//           : null, // Nullable
//       comment: json['comment'],
//       recovery: InputRecoveryModel.fromJson(json['recovery']), // Parse Recovery
//       receipt: Receipt.fromJson(json['receipt']), // Parse Receipt
//       settlement: json['settlement'],
//       updatedAt: DateTime.parse(json['updatedAt']),
//     );
//   }
//   factory InputUserMonthlyRecordModel.fromSnap(DocumentSnapshot snap) {
//     final data = snap.data() as Map<String, dynamic>;

//     return InputUserMonthlyRecordModel(
//       docID: snap.id,
//       createdAt: (data['createdAt'] as Timestamp).toDate(),
//       uid: data['uid'],
//       subsInt: data['subscriptionInt'],
//       subsIntRate: data['subsIntRate'],
//       loanIds: List<String>.from(data['loanIds']),
//       stIntRate: data['stIntRate'],
//       ltIntRate: data['ltIntRate'],
//       isPaid: data['isPaid'],
//       totalShares: data['totalShares'],
//       totalShareDividend: data['totalShareDividend'],
//       shareDividend: data['shareDividend'],
//       dividendRate: data['dividendRate'],
//       previousMonthCB: data['previousMonthCB'],
//       paidOn: data['paidOn'] != null
//           ? (data['paidOn'] as Timestamp).toDate()
//           : null, // Nullable DateTime
//       comment: data['comment'],
//       recovery: InputRecoveryModel.fromJson(data['recovery']),
//       receipt: Receipt.fromJson(data['receipt']),
//       settlement: data['settlement'],
//       updatedAt: (data['updatedAt'] as Timestamp).toDate(),
//     );
//   }
// }
