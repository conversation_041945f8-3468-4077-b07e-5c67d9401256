import 'package:cloud_firestore/cloud_firestore.dart';

class SocietyYearlyRecordModel {
  final String docId;
  final String doId;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final num openingBalance; // OB
  final num closingBalance; // CB
  final num totalSubscription;
  final num intOnSubscription; // Interest on subscription
  final num subscriptionInterestRate; // Interest rate on subscription
  final num totalLoanGiven;
  final num totalLoanReceived;
  final num ltLoanReceived; // Long-term loans received
  final num stLoanReceived; // Short-term loans received
  final num ltLoanGiven; // Long-term loans given
  final num stLoanGiven; // Short-term loans given
  final num ltIntAmt; // Long-term interest amount
  final num stIntAmt; // Short-term interest amount
  final num totalIntAmt; // Total interest amount
  final num loanIntRate; // Loan interest rate
  final num totalPendingLoan; // Total pending loans
  final num ltPendingLoan; // Long-term pending loans
  final num stPendingLoan; // Short-term pending loans
  final num totalExpenses; // Total expenses for the year
  final List<String>
      expensesIds; // IDs of expenses (assuming a list of strings)
  final num totalDividend; // Total dividend
  final num totalMonthlyShareGiven; // Total monthly shares given
  final num totalShareGiven; // Total shares given
  final num monthlyDividend; // Monthly dividend
  final num dividendRate; // Dividend rate

  SocietyYearlyRecordModel({
    required this.docId,
    required this.doId,
    required this.createdAt,
    required this.updatedAt,
    required this.openingBalance,
    required this.closingBalance,
    required this.totalSubscription,
    required this.intOnSubscription,
    required this.subscriptionInterestRate,
    required this.totalLoanGiven,
    required this.totalLoanReceived,
    required this.ltLoanReceived,
    required this.stLoanReceived,
    required this.ltLoanGiven,
    required this.stLoanGiven,
    required this.ltIntAmt,
    required this.stIntAmt,
    required this.totalIntAmt,
    required this.loanIntRate,
    required this.totalPendingLoan,
    required this.ltPendingLoan,
    required this.stPendingLoan,
    required this.totalExpenses,
    required this.expensesIds,
    required this.totalDividend,
    required this.totalMonthlyShareGiven,
    required this.totalShareGiven,
    required this.monthlyDividend,
    required this.dividendRate,
  });

  factory SocietyYearlyRecordModel.fromJson(Map<String, dynamic> json) {
    return SocietyYearlyRecordModel(
      docId: json['docId'],
      doId: json['doId'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      openingBalance: json['OB'], // Opening Balance
      closingBalance: json['CB'], // Closing Balance
      totalSubscription: json['totalSubscription'],
      intOnSubscription: json['intOnSubscription'],
      subscriptionInterestRate: json['subscriptionInterestRate'],
      totalLoanGiven: json['totalLoanGiven'],
      totalLoanReceived: json['totalLoanReceived'],
      ltLoanReceived: json['ltLoanReceived'],
      stLoanReceived: json['stLoanReceived'],
      ltLoanGiven: json['ltLoanGiven'],
      stLoanGiven: json['stLoanGiven'],
      ltIntAmt: json['ltIntAmt'],
      stIntAmt: json['stIntAmt'],
      totalIntAmt: json['totalIntAmt'],
      loanIntRate: json['loanIntRate'],
      totalPendingLoan: json['totalPendingLoan'],
      ltPendingLoan: json['ltPendingLoan'],
      stPendingLoan: json['stPendingLoan'],
      totalExpenses: json['totalExpenses'],
      expensesIds:
          List<String>.from(json['expensesIds']), // Assuming a list of strings
      totalDividend: json['totalDividend'],
      totalMonthlyShareGiven: json['totalMonthlyShareGiven'],
      totalShareGiven: json['totalShareGiven'],
      monthlyDividend: json['monthlyDividend'],
      dividendRate: json['dividendRate'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'doId': doId,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt,
      'OB': openingBalance, // Opening Balance
      'CB': closingBalance, // Closing Balance
      'totalSubscription': totalSubscription,
      'intOnSubscription': intOnSubscription,
      'subscriptionInterestRate': subscriptionInterestRate,
      'totalLoanGiven': totalLoanGiven,
      'totalLoanReceived': totalLoanReceived,
      'ltLoanReceived': ltLoanReceived,
      'stLoanReceived': stLoanReceived,
      'ltLoanGiven': ltLoanGiven,
      'stLoanGiven': stLoanGiven,
      'ltIntAmt': ltIntAmt,
      'stIntAmt': stIntAmt,
      'totalIntAmt': totalIntAmt,
      'loanIntRate': loanIntRate,
      'totalPendingLoan': totalPendingLoan,
      'ltPendingLoan': ltPendingLoan,
      'stPendingLoan': stPendingLoan,
      'totalExpenses': totalExpenses,
      'expensesIds': expensesIds,
      'totalDividend': totalDividend,
      'totalMonthlyShareGiven': totalMonthlyShareGiven,
      'totalShareGiven': totalShareGiven,
      'monthlyDividend': monthlyDividend,
      'dividendRate': dividendRate,
    };
  }

  // Convert from Firestore snapshot to SocietyYearlyRecordModel object
  factory SocietyYearlyRecordModel.fromSnap(DocumentSnapshot snap) {
    final data = snap.data() as Map<String, dynamic>;

    return SocietyYearlyRecordModel(
      docId: snap.id, // Firestore document ID
      doId: data['doId'],
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
      openingBalance: data['OB'], // Opening Balance
      closingBalance: data['CB'], // Closing Balance
      totalSubscription: data['totalSubscription'],
      intOnSubscription: data['intOnSubscription'],
      subscriptionInterestRate: data['subscriptionInterestRate'],
      totalLoanGiven: data['totalLoanGiven'],
      totalLoanReceived: data['totalLoanReceived'],
      ltLoanReceived: data['ltLoanReceived'],
      stLoanReceived: data['stLoanReceived'],
      ltLoanGiven: data['ltLoanGiven'],
      stLoanGiven: data['stLoanGiven'],
      ltIntAmt: data['ltIntAmt'],
      stIntAmt: data['stIntAmt'],
      totalIntAmt: data['totalIntAmt'],
      loanIntRate: data['loanIntRate'],
      totalPendingLoan: data['totalPendingLoan'],
      ltPendingLoan: data['ltPendingLoan'],
      stPendingLoan: data['stPendingLoan'],
      totalExpenses: data['totalExpenses'],
      expensesIds: List<String>.from(data['expensesIds']),
      totalDividend: data['totalDividend'],
      totalMonthlyShareGiven: data['totalMonthlyShareGiven'],
      totalShareGiven: data['totalShareGiven'],
      monthlyDividend: data['monthlyDividend'],
      dividendRate: data['dividendRate'],
    );
  }

  // Convert SocietyYearlyRecordModel object to Firestore document format
  Map<String, dynamic> toSnap() {
    return {
      'doId': doId,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'OB': openingBalance, // Opening Balance
      'CB': closingBalance, // Closing Balance
      'totalSubscription': totalSubscription,
      'intOnSubscription': intOnSubscription,
      'subscriptionInterestRate': subscriptionInterestRate,
      'totalLoanGiven': totalLoanGiven,
      'totalLoanReceived': totalLoanReceived,
      'ltLoanReceived': ltLoanReceived,
      'stLoanReceived': stLoanReceived,
      'ltLoanGiven': ltLoanGiven,
      'stLoanGiven': stLoanGiven,
      'ltIntAmt': ltIntAmt,
      'stIntAmt': stIntAmt,
      'totalIntAmt': totalIntAmt,
      'loanIntRate': loanIntRate,
      'totalPendingLoan': totalPendingLoan,
      'ltPendingLoan': ltPendingLoan,
      'stPendingLoan': stPendingLoan,
      'totalExpenses': totalExpenses,
      'expensesIds': expensesIds,
      'totalDividend': totalDividend,
      'totalMonthlyShareGiven': totalMonthlyShareGiven,
      'totalShareGiven': totalShareGiven,
      'monthlyDividend': monthlyDividend,
      'dividendRate': dividendRate,
    };
  }
}
