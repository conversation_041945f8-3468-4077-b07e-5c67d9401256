import 'package:cloud_firestore/cloud_firestore.dart';

class SettingsModel {
  final String docId;
  final String? dividentRate;
  final double? ltloanInterest;
  final double? stloanInterest;
  final String? subscriptionInterest;
  // final String? shareIntRate;
  final String? defaultLtinstallAmt;
  final String? defaultStinstallAmt;
  final String? defaultSubsinstallAmt;
  final num shareValue;
  final String? upiString;
  final String? upiId;
  final num? upiAmt;
  final String? maintenanceString;
  final String andbuildNumber;
  final String iosbuildNumber;
  final num applicationNo;
  final String? upiPayeeName;
  final String setPin;
  final String? maxLtLoanAmt;
  final String? maxStLoanAmt;
  final bool? applyButton;
  final String? maxShareValue;
  final String? shareCapital;

  SettingsModel({
    required this.docId,
    required this.andbuildNumber,
    required this.iosbuildNumber,
    this.dividentRate,
    this.subscriptionInterest,
    this.ltloanInterest,
    this.stloanInterest,
    this.upiString,
    this.upiId,
    this.upiAmt,
    // this.shareIntRate,
    this.defaultLtinstallAmt,
    this.defaultStinstallAmt,
    this.defaultSubsinstallAmt,
    required this.shareValue,
    this.maintenanceString,
    required this.applicationNo,
    this.maxLtLoanAmt,
    this.maxStLoanAmt,
    required this.setPin,
    this.upiPayeeName,
    this.applyButton,
    required this.maxShareValue,
    required this.shareCapital,
  });

  // Convert a Setting object into a Map
  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'subscriptionInterest': subscriptionInterest,
      'dividentRate': dividentRate,
      'ltloanInterest': ltloanInterest,
      // 'shareIntRate': shareIntRate,
      'stloanInterest': stloanInterest,
      'defaultLtinstallAmt': defaultLtinstallAmt,
      'defaultStinstallAmt': defaultStinstallAmt,
      'defaultSubsinstallAmt': defaultSubsinstallAmt,
      'shareValue': shareValue,
      'upiString': upiString,
      'upiId': upiId,
      'upiAmt': upiAmt,
      'maintenanceString': maintenanceString,
      'andbuildNumber': andbuildNumber,
      'iosbuildNumber': iosbuildNumber,
      'applicationNo': applicationNo,
      'maxLtLoanAmt': maxLtLoanAmt,
      'maxStLoanAmt': maxStLoanAmt,
      'setPin': setPin,
      'upiPayeeName': upiPayeeName,
      'applyButton': applyButton,
      'maxShareValue': maxShareValue,
      'shareCapital': shareCapital,
    };
  }

  // Convert a Map into a Setting object
  factory SettingsModel.fromJson(Map<String, dynamic> json) {
    return SettingsModel(
      docId: json['docId'],
      dividentRate: json['dividentRate'],
      subscriptionInterest: json['subscriptionInterest'],
      ltloanInterest: json['ltloanInterest'],
      // shareIntRate: json['shareIntRate'],
      stloanInterest: json['stloanInterest'],
      defaultLtinstallAmt: json['defaultLtinstallAmt'],
      defaultStinstallAmt: json['defaultStinstallAmt'],
      defaultSubsinstallAmt: json['defaultSubsinstallAmt'],
      shareValue: json['shareValue'],
      upiString: json['upiString'],
      upiId: json['upiId'],
      upiAmt: json['upiAmt'],
      maintenanceString: json['maintenanceString'],
      andbuildNumber: json['andbuildNumber'],
      iosbuildNumber: json['iosbuildNumber'],
      applicationNo: json['applicationNo'],
      maxLtLoanAmt: json['maxLtLoanAmt'],
      maxStLoanAmt: json['maxStLoanAmt'],
      setPin: json['setPin'],
      upiPayeeName: json['upiPayeeName'],
      applyButton: json['applyButton'],
      maxShareValue: json['maxShareValue'],
      shareCapital: json['shareCapital'],
    );
  }

  // Convert a Firestore DocumentSnapshot into a SettingsModel object
  factory SettingsModel.fromSnap(DocumentSnapshot snap) {
    final data = snap.data() as Map<String, dynamic>;
    return SettingsModel(
      docId: snap.id,
      dividentRate: data['dividentRate'],
      subscriptionInterest: data['subscriptionInterest'],
      ltloanInterest: data['ltloanInterest'],
      stloanInterest: data['stloanInterest'],
      // shareIntRate: data['shareIntRate'],
      defaultLtinstallAmt: data['defaultLtinstallAmt'],
      defaultStinstallAmt: data['defaultStinstallAmt'],
      defaultSubsinstallAmt: data['defaultSubsinstallAmt'],
      shareValue: data['shareValue'],
      upiString: data['upiString'],
      upiId: data['upiId'],
      upiAmt: data['upiAmt'],
      maintenanceString: data['maintenanceString'],
      andbuildNumber: data['andbuildNumber'],
      iosbuildNumber: data['iosbuildNumber'],
      applicationNo: data['applicationNo'], maxLtLoanAmt: data['maxLtLoanAmt'],
      maxStLoanAmt: data['maxStLoanAmt'],
      setPin: data['setPin'],
      upiPayeeName: data['upiPayeeName'],
      applyButton: data['applyButton'],
      maxShareValue: data['maxShareValue'],
      shareCapital: data['shareCapital'],
    );
  }

  // Convert a SettingsModel object into a Firestore DocumentSnapshot format
  Map<String, dynamic> toSnap() {
    return {
      'subscriptionInterest': subscriptionInterest,
      'dividentRate': dividentRate,
      'ltloanInterest': ltloanInterest,
      // 'shareIntRate': shareIntRate,
      'stloanInterest': stloanInterest,
      'defaultLtinstallAmt': defaultLtinstallAmt,
      'defaultStinstallAmt': defaultStinstallAmt,
      'defaultSubsinstallAmt': defaultSubsinstallAmt,
      'shareValue': shareValue,
      'upiString': upiString,
      'upiId': upiId,
      'upiAmt': upiAmt,
      'maintenanceString': maintenanceString,
      'andbuildNumber': andbuildNumber,
      'iosbuildNumber': iosbuildNumber,
      'applicationNo': applicationNo, 'maxLtLoanAmt': maxLtLoanAmt,
      'maxStLoanAmt': maxStLoanAmt,
      'setPin': setPin,
      'upiPayeeName': upiPayeeName,
      'applyButton': applyButton,
      'maxShareValue': maxShareValue,
      'shareCapital': shareCapital,
    };
  }
}
