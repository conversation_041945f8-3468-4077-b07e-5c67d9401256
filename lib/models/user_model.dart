import 'package:cloud_firestore/cloud_firestore.dart';

class UserModel {
  final String docId;
  final DateTime createdAt;
  final String name;
  final num cpfNo;
  final String districtoffice;
  final String? email;
  final String? phoneNo;
  final num? settlement;
  final num? totalSubs; // Subs = Subscription
  final num? totalSubsInt; // Int = Interest
  final num? ltLoansDue; // lt = longterm
  final num? stLoansDue; // st = shortTerm
  final num? totalLtLoans;
  final num? totalStLoans;
  final num? totalLtIntPaid;
  final num? totalStIntPaid;
  final num? totalDivident;
  final num? totalShares;

  final DateTime? registrationDate;
  final num employeeNo;
  final String? currentAddress;
  final String? permanentAddress;
  final String? documents;
  final bool approved;
  final String password;
  final Map<String, dynamic>? userPrevoiusMonthlyRecord;
  final Map<String, dynamic>? userPrevoiusYearlyRecord;

  final String? bankAcName;
  final String? bankName;
  final String? ifscCode;
  final num? bankAcNo;

  final bool archived;
  final bool subsIntPayoutStatus;
  final bool dividendPayoutStatus;

  final num? momento;
  final String? nomineeName;
  final String? nomineeRelation;

  final num? obSubs;
  final num? obShares;
  final num? obLt;
  final num? obSt;

  UserModel({
    required this.bankAcName,
    required this.bankName,
    required this.ifscCode,
    required this.bankAcNo,
    this.registrationDate,
    required this.docId,
    required this.createdAt,
    required this.name,
    required this.cpfNo,
    required this.districtoffice,
    required this.email,
    required this.phoneNo,
    required this.settlement,
    required this.totalSubs,
    required this.totalSubsInt,
    required this.ltLoansDue,
    required this.stLoansDue,
    required this.totalLtLoans,
    required this.totalStLoans,
    required this.totalLtIntPaid,
    required this.totalStIntPaid,
    required this.totalDivident,
    required this.totalShares,
    required this.employeeNo,
    required this.currentAddress,
    required this.permanentAddress,
    required this.documents,
    required this.approved,
    required this.password,
    required this.archived,
    required this.userPrevoiusMonthlyRecord,
    this.userPrevoiusYearlyRecord,
    required this.subsIntPayoutStatus,
    required this.dividendPayoutStatus,
    required this.momento,
    required this.nomineeName,
    required this.nomineeRelation,
    required this.obSubs,
    required this.obShares,
    required this.obLt,
    required this.obSt,
  });

  // Factory constructor to create an instance from JSON
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      docId: json['docId'],
      createdAt: DateTime.parse(json['createdAt']),
      name: json['name'],
      cpfNo: json['cpfNo'],
      districtoffice: json['districtoffice'],
      email: json['email'],
      phoneNo: json['phonenumber'],
      settlement: json['settlement'],
      totalSubs: json['totalSubs'],
      totalSubsInt: json['totalSubsInt'],
      ltLoansDue: json['ltLoansDue'],
      stLoansDue: json['stLoansDue'],
      totalLtLoans: json['totalLtLoans'],
      totalStLoans: json['totalStLoans'],
      totalLtIntPaid: json['totalLtIntPaid'],
      totalStIntPaid: json['totalStIntPaid'],
      totalDivident: json['totalDivident'],
      totalShares: json['totalShares'],
      registrationDate: json['registrationDate'] != null
          ? DateTime.parse(json['registrationDate'])
          : null,
      userPrevoiusMonthlyRecord:
          Map<String, dynamic>.from(json['userPrevoiusMonthlyRecord']),
      employeeNo: json['employeeNo'],
      currentAddress: json['currentAddress'],
      permanentAddress: json['permanentAddress'],
      documents: json['documents'],
      approved: json['approved'],
      password: json['password'],
      bankAcName: json['bankAcName'],
      bankName: json['bankName'],
      ifscCode: json['ifscCode'],
      bankAcNo: json['bankAcNo'],
      archived: json['archived'],
      subsIntPayoutStatus: json['subsIntPayoutStatus'] ?? false,
      dividendPayoutStatus: json['dividendPayoutStatus'] ?? false,
      userPrevoiusYearlyRecord:
          Map<String, dynamic>.from(json['userPrevoiusYearlyRecord']),
      momento: json['momento'],
      nomineeName: json['nomineeName'],
      nomineeRelation: json['nomineeRelation'],
      obSubs: json['obSubs'],
      obShares: json['obShares'],
      obLt: json['obLt'],
      obSt: json['obSt'],
    );
  }

  // Convert this instance to a map (JSON format)
  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'createdAt': createdAt.toIso8601String(),
      'name': name,
      'cpfNo': cpfNo,
      'districtoffice': districtoffice,
      'email': email,
      'phonenumber': phoneNo,
      'settlement': settlement,
      'totalSubs': totalSubs,
      'totalSubsInt': totalSubsInt,
      'ltLoansDue': ltLoansDue,
      'stLoansDue': stLoansDue,
      'totalLtLoans': totalLtLoans,
      'totalStLoans': totalStLoans,
      'totalLtIntPaid': totalLtIntPaid,
      'totalStIntPaid': totalStIntPaid,
      'totalDivident': totalDivident,
      'totalShares': totalShares,
      'registrationDate': registrationDate,
      'employeeNo': employeeNo,
      'currentAddress': currentAddress,
      'permanentAddress': permanentAddress,
      'documents': documents,
      'approved': approved,
      'password': password,
      'bankAcName': bankAcName,
      'bankName': bankName,
      'ifscCode': ifscCode,
      'bankAcNo': bankAcNo,
      'archived': archived,
      'userPrevoiusMonthlyRecord': userPrevoiusMonthlyRecord,
      'userPrevoiusYearlyRecord': userPrevoiusYearlyRecord,
      'subsIntPayoutStatus': subsIntPayoutStatus,
      'dividendPayoutStatus': dividendPayoutStatus,
      'momento': momento,
      'nomineeName': nomineeName,
      'nomineeRelation': nomineeRelation,
      'obSubs': obSubs,
      'obShares': obShares,
      'obLt': obLt,
      'obSt': obSt,
    };
  }

  // Convert a Firestore DocumentSnapshot to a UserModel
  factory UserModel.fromSnap(DocumentSnapshot snap) {
    var data = snap.data() as Map<String, dynamic>;
    return UserModel(
      docId: snap.id,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      name: data['name'],
      cpfNo: data['cpfNo'],
      districtoffice: data['districtoffice'],
      email: data['email'],
      phoneNo: data['phonenumber'],
      settlement: data['settlement'],
      totalSubs: data['totalSubs'],
      totalSubsInt: data['totalSubsInt'],
      ltLoansDue: data['ltLoansDue'],
      stLoansDue: data['stLoansDue'],
      totalLtLoans: data['totalLtLoans'],
      totalStLoans: data['totalStLoans'],
      totalLtIntPaid: data['totalLtIntPaid'],
      totalStIntPaid: data['totalStIntPaid'],
      totalDivident: data['totalDivident'],
      totalShares: data['totalShares'],
      registrationDate: data['registrationDate'] != null
          ? (data['registrationDate'] as Timestamp).toDate()
          : null,
      employeeNo: data['employeeNo'],
      currentAddress: data['currentAddress'],
      permanentAddress: data['permanentAddress'],
      documents: data['documents'],
      approved: data['approved'],
      password: data['password'],
      bankAcName: data['bankAcName'],
      bankName: data['bankName'],
      ifscCode: data['ifscCode'],
      bankAcNo: data['bankAcNo'],
      archived: data['archived'],
      userPrevoiusMonthlyRecord:
          Map<String, dynamic>.from(data['userPrevoiusMonthlyRecord'] ?? {}),
      userPrevoiusYearlyRecord:
          Map<String, dynamic>.from(data['userPrevoiusYearlyRecord'] ?? {}),
      subsIntPayoutStatus: data.containsKey('subsIntPayoutStatus')
          ? data['subsIntPayoutStatus']
          : false,
      dividendPayoutStatus: data.containsKey('dividendPayoutStatus')
          ? data['dividendPayoutStatus']
          : false,
      momento: data['momento'],
      nomineeName: data['nomineeName'],
      nomineeRelation: data['nomineeRelation'],
      obSubs: data['obSubs'],
      obShares: data['obShares'],
      obLt: data['obLt'],
      obSt: data['obSt'],
    );
  }

  // Convert this UserModel to a Firestore-compatible map
  Map<String, dynamic> toSnap() {
    return {
      'createdAt': Timestamp.fromDate(createdAt),
      'name': name,
      'cpfNo': cpfNo,
      'districtoffice': districtoffice,
      'email': email,
      'phonenumber': phoneNo,
      'settlement': settlement,
      'totalSubs': totalSubs,
      'totalSubsInt': totalSubsInt,
      'ltLoansDue': ltLoansDue,
      'stLoansDue': stLoansDue,
      'totalLtLoans': totalLtLoans,
      'totalStLoans': totalStLoans,
      'totalLtIntPaid': totalLtIntPaid,
      'totalStIntPaid': totalStIntPaid,
      'totalDivident': totalDivident,
      'totalShares': totalShares,
      'registrationDate': registrationDate != null
          ? Timestamp.fromDate(registrationDate!)
          : null,
      'employeeNo': employeeNo,
      'currentAddress': currentAddress,
      'permanentAddress': permanentAddress,
      'documents': documents,
      'approved': approved,
      'password': password,
      'bankAcName': bankAcName,
      'bankName': bankName,
      'ifscCode': ifscCode,
      'bankAcNo': bankAcNo,
      'archived': archived,
      'userPrevoiusMonthlyRecord': userPrevoiusMonthlyRecord,
      'userPrevoiusYearlyRecord': userPrevoiusYearlyRecord,
      'subsIntPayoutStatus': subsIntPayoutStatus,
      'dividendPayoutStatus': dividendPayoutStatus,
      'momento': momento,
      'nomineeName': nomineeName,
      'nomineeRelation': nomineeRelation,
      'obSubs': obSubs,
      'obShares': obShares,
      'obLt': obLt,
      'obSt': obSt,
    };
  }
}
