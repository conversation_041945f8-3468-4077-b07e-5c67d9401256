class Recovery {
  final num subscriptionAmt;
  final num longTermInstalmentAmt;
  final num shortTermInstalmentAmt;
  final num longTermInterestAmt;
  final num shortTermInterestAmt;
  final num totalAmt;

  Recovery({
    required this.subscriptionAmt,
    required this.longTermInstalmentAmt,
    required this.shortTermInstalmentAmt,
    required this.longTermInterestAmt,
    required this.shortTermInterestAmt,
    required this.totalAmt,
  });

  factory Recovery.fromJson(Map<String, dynamic> json) {
    return Recovery(
      subscriptionAmt: json['subscriptionAmt'],
      longTermInstalmentAmt: json['longTermInstalmentAmt'],
      shortTermInstalmentAmt: json['shortTermInstalmentAmt'],
      longTermInterestAmt: json['longTermInterestAmt'],
      shortTermInterestAmt: json['shortTermInterestAmt'],
      totalAmt: json['totalAmt'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'subscriptionAmt': subscriptionAmt,
      'longTermInstalmentAmt': longTermInstalmentAmt,
      'shortTermInstalmentAmt': shortTermInstalmentAmt,
      'longTermInterestAmt': longTermInterestAmt,
      'shortTermInterestAmt': shortTermInterestAmt,
      'totalAmt': totalAmt,
    };
  }

  // Factory constructor for 'from snap' (assumed to be deserialization from a snapshot, e.g., Firebase)
  factory Recovery.fromSnap(Map<String, dynamic> snap) {
    return Recovery(
      subscriptionAmt: snap['subscriptionAmt'],
      longTermInstalmentAmt: snap['longTermInstalmentAmt'],
      shortTermInstalmentAmt: snap['shortTermInstalmentAmt'],
      longTermInterestAmt: snap['longTermInterestAmt'],
      shortTermInterestAmt: snap['shortTermInterestAmt'],
      totalAmt: snap['totalAmt'],
    );
  }

  // Method for 'to snap' (assumed to be serialization for a snapshot, e.g., saving to Firebase)
  Map<String, dynamic> toSnap() {
    return {
      'subscriptionAmt': subscriptionAmt,
      'longTermInstalmentAmt': longTermInstalmentAmt,
      'shortTermInstalmentAmt': shortTermInstalmentAmt,
      'longTermInterestAmt': longTermInterestAmt,
      'shortTermInterestAmt': shortTermInterestAmt,
      'totalAmt': totalAmt,
    };
  }
}
