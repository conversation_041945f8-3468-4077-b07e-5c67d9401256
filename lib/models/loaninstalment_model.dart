// import 'package:cloud_firestore/cloud_firestore.dart';

// class LoanInstallmentModel {
//   final String docId;
//   final num installmentAmt; // Amount to be paid in this installment
//   final num paidAmt; // Amount that has been paid
//   final num interestPaid; // Amount of interest paid for this installment
//   final DateTime? paidOn; // Nullable date when the installment was paid

//   LoanInstallmentModel({
//     required this.docId,
//     required this.installmentAmt,
//     required this.paidAmt,
//     required this.interestPaid,
//     this.paidOn, // Nullable
//   });

//   factory LoanInstallmentModel.fromJson(Map<String, dynamic> json) {
//     return LoanInstallmentModel(
//       docId: json['docId'],
//       installmentAmt: json['installmentAmt'],
//       paidAmt: json['paidAmt'],
//       interestPaid: json['interestPaid'],
//       paidOn: json['paidOn'] != null
//           ? DateTime.parse(json['paidOn'])
//           : null, // Nullable
//     );
//   }

//   Map<String, dynamic> toJson() {
//     return {
//       'docId': docId,
//       'installmentAmt': installmentAmt,
//       'paidAmt': paidAmt,
//       'interestPaid': interestPaid,
//       'paidOn': paidOn?.toIso8601String(), // Nullable
//     };
//   }

//   // Convert from Firestore snapshot to LoanInstallmentModel object
//   factory LoanInstallmentModel.fromSnap(DocumentSnapshot snap) {
//     final data = snap.data() as Map<String, dynamic>;

//     return LoanInstallmentModel(
//       docId: snap.id, // Firestore document ID
//       installmentAmt: data['installmentAmt'],
//       paidAmt: data['paidAmt'],
//       interestPaid: data['interestPaid'],
//       paidOn: data['paidOn'] != null
//           ? (data['paidOn'] as Timestamp).toDate()
//           : null, // Nullable
//     );
//   }

//   // Convert LoanInstallmentModel object to Firestore document format
//   Map<String, dynamic> toSnap() {
//     return {
//       'installmentAmt': installmentAmt,
//       'paidAmt': paidAmt,
//       'interestPaid': interestPaid,
//       'paidOn': paidOn != null ? Timestamp.fromDate(paidOn!) : null, // Nullable
//     };
//   }
// }
