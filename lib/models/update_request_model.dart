import 'package:cloud_firestore/cloud_firestore.dart';

class UpdateRequestModel {
  final String docId;
  final String uId;
  final String requestType;
  final String remarks;
  final String docName;
  final String? supportiveDoc;
  final bool? accepted;
  final DateTime createdAt;

  UpdateRequestModel({
    required this.docId,
    required this.uId,
    required this.requestType,
    required this.remarks,
    required this.docName,
    required this.supportiveDoc,
    required this.accepted,
    required this.createdAt,
  });

  // From Map (JSON)
  factory UpdateRequestModel.fromJson(Map<String, dynamic> json) {
    return UpdateRequestModel(
      docId: json['docId'] ?? '',
      uId: json['uId'] ?? '',
      requestType: json['requestType'] ?? '',
      remarks: json['remarks'] ?? '',
      docName: json['docName'] ?? '',
      supportiveDoc: json['supportiveDoc'],
      accepted: json['accepted'],
      createdAt: json['createdAt'],
    );
  }

  // To Map (JSON)
  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'uId': uId,
      'requestType': requestType,
      'remarks': remarks,
      'docName': docName,
      'supportiveDoc': supportiveDoc,
      'accepted': accepted,
      'createdAt': createdAt,
    };
  }

  // From Firestore DocumentSnapshot
  factory UpdateRequestModel.fromSnap(DocumentSnapshot snap) {
    var data = snap.data() as Map<String, dynamic>;
    return UpdateRequestModel(
      docId: snap.id,
      uId: data['uId'] ?? '',
      requestType: data['requestType'] ?? '',
      remarks: data['remarks'] ?? '',
      docName: data['docName'] ?? '',
      supportiveDoc: data['supportiveDoc'],
      accepted: data['accepted'],
      createdAt: data['createdAt'],
    );
  }

  // To Firestore Map (same as toJson, can be used in set/add)
  Map<String, dynamic> toSnap() => toJson();
}
