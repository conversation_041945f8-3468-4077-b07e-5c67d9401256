import 'package:cloud_firestore/cloud_firestore.dart';

class NotificationsModel {
  final String docId;
  final String uId;
  final String title;
  final String desc;
  final String type;
  final String districtOffice;
  final DateTime createdAt;
  final String? attachment;

  NotificationsModel({
    required this.docId,
    required this.uId,
    required this.title,
    required this.desc,
    required this.type,
    required this.districtOffice,
    required this.createdAt,
    required this.attachment,
  });

  factory NotificationsModel.fromJson(Map<String, dynamic> json) {
    return NotificationsModel(
      docId: json['docId'],
      uId: json['uId'],
      title: json['title'],
      desc: json['desc'],
      type: json['type'],
      districtOffice: json['districtOffice'],
      createdAt: (json['createdAt'] as Timestamp).toDate(),
      attachment: json['attachment'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'uId': uId,
      'title': title,
      'desc': desc,
      'type': type,
      'districtOffice': districtOffice,
      'createdAt': Timestamp.fromDate(createdAt),
      'attachment': attachment,
    };
  }

  factory NotificationsModel.fromSnap(DocumentSnapshot snap) {
    var data = snap.data() as Map<String, dynamic>;
    return NotificationsModel(
      docId: snap.id,
      uId: data['uId'],
      title: data['title'],
      desc: data['desc'],
      type: data['type'],
      districtOffice: data['districtOffice'],
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      attachment: data['attachment'],
    );
  }

  Map<String, dynamic> toSnap() {
    return {
      'docId': docId,
      'uId': uId,
      'title': title,
      'desc': desc,
      'type': type,
      'districtOffice': districtOffice,
      'createdAt': Timestamp.fromDate(createdAt),
      'attachment': attachment,
    };
  }
}
