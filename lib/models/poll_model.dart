import 'package:cloud_firestore/cloud_firestore.dart';

class PollModel {
  final String id; // Unique poll ID (Firestore doc ID or backend ID)
  final String question; // Poll question
  final List<String> options; // List of poll options
  final Map<String, int>? votes; // Vote counts per option
  final Map<String, dynamic>? userVotes; // userId -> String or List<String>
  final bool isActive; // Is poll currently active
  final bool allowsMultipleAnswers; // Allow multiple answers per user
  final DateTime? expiryDate; // Optional expiry date

  PollModel({
    required this.id,
    required this.question,
    required this.options,
    required this.votes,
    required this.userVotes,
    required this.isActive,
    required this.allowsMultipleAnswers,
    required this.expiryDate,
  });

  // Create PollModel from a JSON map
  factory PollModel.fromJson(Map<String, dynamic> json, String id) {
    final options = List<String>.from(json['options'] ?? []);
    final votesMap =
        json['votes'] != null ? Map<String, dynamic>.from(json['votes']) : null;
    final userVotesMap = json['userVotes'] != null
        ? Map<String, dynamic>.from(json['userVotes'])
        : null;

    // Cast votes map to Map<String, int>? if not null
    final Map<String, int>? votes =
        votesMap?.map((k, v) => MapEntry(k, v as int));

    return PollModel(
      id: id,
      question: json['question'] ?? '',
      options: options,
      votes: votes,
      userVotes: userVotesMap,
      isActive: json['isActive'] ?? true,
      allowsMultipleAnswers: json['allowsMultipleAnswers'] ?? false,
      expiryDate: json['expiryDate'] != null
          ? DateTime.parse(json['expiryDate'])
          : null,
    );
  }

  // Convert PollModel to JSON map
  Map<String, dynamic> toJson() {
    return {
      'question': question,
      'options': options,
      if (votes != null) 'votes': votes,
      if (userVotes != null) 'userVotes': userVotes,
      'isActive': isActive,
      'allowsMultipleAnswers': allowsMultipleAnswers,
      if (expiryDate != null) 'expiryDate': expiryDate!.toIso8601String(),
    };
  }

  // Create PollModel from Firestore DocumentSnapshot
  factory PollModel.fromSnap(DocumentSnapshot snap) {
    final data = snap.data() as Map<String, dynamic>;
    return PollModel.fromJson(data, snap.id);
  }

  // Convert PollModel to map to save in Firestore
  Map<String, dynamic> toSnap() {
    return toJson();
  }
}
