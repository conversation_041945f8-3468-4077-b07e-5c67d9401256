import 'package:cloud_firestore/cloud_firestore.dart';

class DistrictOfficeModel {
  final String docId;
  final String name;
  final String email;
  final String location;

  DistrictOfficeModel(
      {required this.docId,
      required this.name,
      required this.email,
      required this.location});

  factory DistrictOfficeModel.fromJson(Map<String, dynamic> json) {
    return DistrictOfficeModel(
      docId: json['docId'],
      name: json['name'],
      email: json['email'],
      location: json['location'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'name': name,
      'email': email,
      'location': location,
    };
  }

  // Factory method to create an instance from a Firestore snapshot
  factory DistrictOfficeModel.fromSnap(
      DocumentSnapshot<Map<String, dynamic>> snap) {
    final data = snap.data();
    if (data == null) {
      throw Exception(
          'Document does not exist'); // Handle missing document case
    }

    return DistrictOfficeModel(
      docId: snap.id, // Use the document ID from the snapshot
      name: data['name'],
      email: data['email'],
      location: data['location'],
    );
  }

  // Method to convert the instance to a Firestore document format
  Map<String, dynamic> toSnap() {
    return {
      'name': name,
      'email': email,
      'location': location,
    };
  }
}
