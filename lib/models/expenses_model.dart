import 'package:cloud_firestore/cloud_firestore.dart';

class Expense {
  final String docID;
  final DateTime createdAt;
  final DateTime expenseDate;
  final String? comment;
  final String expenseBy;
  final String name;
  final bool isIncome;
  final num amount;
  final bool cash;

  Expense({
    required this.docID,
    required this.createdAt,
    required this.expenseDate,
    this.comment,
    required this.expenseBy,
    required this.name,
    required this.isIncome,
    required this.amount,
    required this.cash,
  });

  factory Expense.fromJson(Map<String, dynamic> json) {
    return Expense(
      docID: json['docID'],
      createdAt: DateTime.parse(json['createdAt']),
      expenseDate: DateTime.parse(json['expenseDate']),
      comment: json['comment'],
      expenseBy: json['expenseBy'],
      name: json['name'],
      isIncome: json['isIncome'],
      amount: json['amount'],
      cash: json['cash'],
    );
  }

  factory Expense.fromSnap(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    return Expense(
      docID: snapshot.id,
      createdAt: data['createdAt'].toDate(),
      expenseDate: data['expenseDate'].toDate(),
      comment: data['comment'],
      expenseBy: data['expenseBy'],
      name: data['name'],
      isIncome: data['isIncome'] as bool? ?? false,
      amount: data['amount'],
      cash: data['cash'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'docID': docID,
      'createdAt': createdAt.toIso8601String(),
      'expenseDate': expenseDate.toIso8601String(),
      'comment': comment,
      'expenseBy': expenseBy,
      'name': name,
      'isIncome': isIncome,
      'amount': amount,
      'cash': cash,
    };
  }
}
