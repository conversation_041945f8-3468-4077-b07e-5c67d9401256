import 'package:cloud_firestore/cloud_firestore.dart';

class CustomNotificationsModel {
  final String docId;
  final String title;
  final String desc;
  final String topic;
  final DateTime createdAt;
  final bool test;
  final String districtOffice;
  final String? attachment;

  CustomNotificationsModel({
    required this.docId,
    required this.title,
    required this.desc,
    required this.topic,
    required this.createdAt,
    required this.test,
    required this.districtOffice,
    required this.attachment,
  });

  // From JSON
  factory CustomNotificationsModel.fromJson(Map<String, dynamic> json) {
    return CustomNotificationsModel(
      docId: json['docId'] ?? '',
      title: json['title'] ?? '',
      desc: json['desc'] ?? '',
      topic: json['topic'] ?? '',
      createdAt: (json['createdAt'] as Timestamp).toDate(),
      test: json['test'] ?? false,
      districtOffice: json['districtOffice'] ?? '',
      attachment: json['attachment'],
    );
  }

  // To JSON
  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'title': title,
      'desc': desc,
      'topic': topic,
      'createdAt': Timestamp.fromDate(createdAt),
      'test': test,
      'districtOffice': districtOffice,
      'attachment': attachment,
    };
  }

  // From Firestore DocumentSnapshot
  factory CustomNotificationsModel.fromSnap(DocumentSnapshot snap) {
    var data = snap.data() as Map<String, dynamic>;
    return CustomNotificationsModel(
      docId: snap.id,
      title: data['title'] ?? '',
      desc: data['desc'] ?? '',
      topic: data['topic'] ?? '',
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      test: data['test'] ?? false,
      districtOffice: data['districtOffice'] ?? '',
      attachment: data['attachment'],
    );
  }

  // To Firestore format (without docId)
  Map<String, dynamic> toSnap() {
    return {
      'title': title,
      'desc': desc,
      'topic': topic,
      'createdAt': Timestamp.fromDate(createdAt),
      'test': test,
      'districtOffice': districtOffice,
      'attachment': attachment,
    };
  }
}
