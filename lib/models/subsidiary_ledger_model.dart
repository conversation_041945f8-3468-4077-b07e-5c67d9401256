import 'package:cloud_firestore/cloud_firestore.dart';

class SubsidiaryLedgerModel {
  final DateTime? updatedAt;
  final num year;

  SubsidiaryLedgerModel({
    required this.updatedAt,
    required this.year,
  });

  factory SubsidiaryLedgerModel.fromJson(Map<String, dynamic> json) {
    return SubsidiaryLedgerModel(
      updatedAt: DateTime.parse(json['updatedAt']),
      year: json['year'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'updatedAt': updatedAt?.toIso8601String(),
      'year': year,
    };
  }

  factory SubsidiaryLedgerModel.fromSnap(
      DocumentSnapshot<Map<String, dynamic>> snap) {
    final data = snap.data()!;
    return SubsidiaryLedgerModel(
      updatedAt: data['updatedAt'] != null
          ? (data['updatedAt'] as Timestamp?)?.toDate()
          : null,
      year: data['year'],
    );
  }

  Map<String, dynamic> toSnap() => toJson();
}

class UserDataModel {
  final String uId;
  final num cpfNo;
  final String name;
  final String districtOffice;
  final List<MonthlyLedgerModel> monthlyEntries;

  UserDataModel({
    required this.uId,
    required this.cpfNo,
    required this.name,
    required this.districtOffice,
    required this.monthlyEntries,
  });

  factory UserDataModel.fromJson(Map<String, dynamic> json) {
    return UserDataModel(
      uId: json['uId'] ?? '',
      cpfNo: json['cpfNo'] ?? 0,
      name: json['name'] ?? '',
      districtOffice: json['districtOffice'] ?? '',
      monthlyEntries: (json['monthlyEntries'] as List<dynamic>? ?? [])
          .map((e) => MonthlyLedgerModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'uId': uId,
      'cpfNo': cpfNo,
      'name': name,
      'districtOffice': districtOffice,
      'monthlyEntries': monthlyEntries.map((e) => e.toJson()).toList(),
    };
  }

  factory UserDataModel.fromSnap(DocumentSnapshot snap) {
    final data = snap.data() as Map<String, dynamic>;

    return UserDataModel(
      uId: data['uId'] ?? '',
      cpfNo: data['cpfNo'] ?? 0,
      name: data['name'] ?? '',
      districtOffice: data['districtOffice'] ?? '',
      monthlyEntries: (data['monthlyEntries'] as List<dynamic>? ?? [])
          .map((e) => MonthlyLedgerModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'uId': uId,
      'cpfNo': cpfNo,
      'name': name,
      'districtOffice': districtOffice,
      'monthlyEntries': monthlyEntries.map((e) => e.toJson()).toList(),
    };
  }
}

class MonthlyLedgerModel {
  final String month;
  final num subscription;
  final num longTermInstallment;
  final num shortTermInstallment;
  final num interest;
  final num totalinterest;
  final num totalsubscription;
  final num totallongTermInstallment;
  final num totalshortTermInstallment;

  MonthlyLedgerModel({
    required this.month,
    required this.subscription,
    required this.longTermInstallment,
    required this.shortTermInstallment,
    required this.interest,
    required this.totalinterest,
    required this.totallongTermInstallment,
    required this.totalshortTermInstallment,
    required this.totalsubscription,
  });

  factory MonthlyLedgerModel.fromJson(Map<String, dynamic> json) {
    return MonthlyLedgerModel(
      month: json['month'] ?? '',
      subscription: json['subscription'] ?? 0,
      longTermInstallment: json['longTermInstallment'] ?? 0,
      shortTermInstallment: json['shortTermInstallment'] ?? 0,
      interest: json['interest'] ?? 0,
      totalinterest: json['totalinterest'] ?? 0,
      totallongTermInstallment: json['totallongTermInstallment'] ?? 0,
      totalshortTermInstallment: json['totalshortTermInstallment'] ?? 0,
      totalsubscription: json['totalsubscription'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'month': month,
      'subscription': subscription,
      'longTermInstallment': longTermInstallment,
      'shortTermInstallment': shortTermInstallment,
      'interest': interest,
      'totalinterest': totalinterest,
      'totallongTermInstallment': totallongTermInstallment,
      'totalshortTermInstallment': totalshortTermInstallment,
      'totalsubscription': totalsubscription,
    };
  }

  factory MonthlyLedgerModel.fromSnap(DocumentSnapshot snap) {
    var data = snap.data() as Map<String, dynamic>;
    return MonthlyLedgerModel(
      month: data['month'] ?? '',
      subscription: data['subscription'] ?? 0,
      longTermInstallment: data['longTermInstallment'] ?? 0,
      shortTermInstallment: data['shortTermInstallment'] ?? 0,
      interest: data['interest'] ?? 0,
      totalinterest: data['totalinterest'] ?? 0,
      totallongTermInstallment: data['totallongTermInstallment'] ?? 0,
      totalshortTermInstallment: data['totalshortTermInstallment'] ?? 0,
      totalsubscription: data['totalsubscription'] ?? 0,
    );
  }

  Map<String, dynamic> toSnap() {
    return {
      'month': month,
      'subscription': subscription,
      'longTermInstallment': longTermInstallment,
      'shortTermInstallment': shortTermInstallment,
      'interest': interest,
      'totalinterest': totalinterest,
      'totallongTermInstallment': totallongTermInstallment,
      'totalshortTermInstallment': totalshortTermInstallment,
      'totalsubscription': totalsubscription,
    };
  }
}
