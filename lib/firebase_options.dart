// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCul81IM_AwbYg9WW6QTh2z2SUVBKj4y30',
    appId: '1:63752906252:web:1920522ad17d1b70713029',
    messagingSenderId: '63752906252',
    projectId: 'food-corp-india',
    authDomain: 'food-corp-india.firebaseapp.com',
    storageBucket: 'food-corp-india.appspot.com',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyA4LOZVC_Jl7qLBOX20Ohvxp3jqrNKX0ZI',
    appId: '1:63752906252:android:812aae6b1de0da14713029',
    messagingSenderId: '63752906252',
    projectId: 'food-corp-india',
    storageBucket: 'food-corp-india.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCttGdHo4_D1eOhwdGbAkDw7783Ri3jmuk',
    appId: '1:63752906252:ios:66cdfdcd8987594f713029',
    messagingSenderId: '63752906252',
    projectId: 'food-corp-india',
    storageBucket: 'food-corp-india.appspot.com',
    iosBundleId: 'com.diwizon.foodcorp',
  );
}
