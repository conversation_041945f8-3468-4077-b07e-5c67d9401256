import 'dart:async';
import 'dart:math';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:foodcorp/shared/methods.dart';
import 'package:get/get.dart';
import '../shared/firebase.dart';

class Authctrl extends GetxController {
  TextEditingController emailctrl = TextEditingController();
  TextEditingController otpctrl = TextEditingController();

  bool otpsent = false;
  bool reotpsent = false;
  bool lploader = false;
  bool resendloader = false;
  bool sendingOtp = false;

  String retryOtpId = "RetryOtpId";
  int otpRetryTimeInSeconds = 40;

  int? newUserEmailOtp;
  DateTime? newUserEmailOtpSentOn;
  // Stopwatch retryOtpStopWatch = Stopwatch();
  Timer? timer;
  var isLoggedIn = false.obs;

  void resetFields() {
    emailctrl.clear();
    otpctrl.clear();
    otpsent = false;
    reotpsent = false;
    lploader = false;
    resendloader = false;
    sendingOtp = false;
  }

  @override
  void onInit() {
    super.onInit();
    FirebaseAuth.instance.authStateChanges().listen((user) {
      isLoggedIn.value = user != null;
    });
  }

  requestResendOTPEmail({required BuildContext context}) async {
    sendingOtp = true;
    update();
    try {
      final email = emailctrl.text.trim().toLowerCase();

      if (email == "<EMAIL>") {
        // Skip sending OTP
        sendingOtp = false;
        otpsent = true; // Show OTP field directly
        reotpsent = true;
        update();
        return;
      }

      final otp = Random().nextInt(999999);

      QuerySnapshot<Map<String, dynamic>> usersCollection = await FBFireStore
          .users
          .where('email', isEqualTo: emailctrl.text.trim().toLowerCase())
          .get();
      if (usersCollection.docs.isNotEmpty) {
        // // Document Exist
        debugPrint("USER ALREADY EXIST!!!!!!!");
        // Save Otp
        newUserEmailOtp = otp;
        newUserEmailOtpSentOn = DateTime.now();
        await FBFireStore.users.doc(usersCollection.docs.first.id).update({
          'otp': otp,
          'otpTime': FieldValue.serverTimestamp(),
        });
        // Send OTP Email
        await FBFunctions.ff.httpsCallable('sendOtpEmail').call(
          {
            'email': emailctrl.text.trim().toLowerCase(),
            'otp': otp,
          },
        );
        sendingOtp = false;
        otpsent = true;
        reotpsent = true;
        // retryOtpStopWatch.start();
        update();
        // timer = Timer.periodic(const Duration(seconds: 1), (_) {
        //   if ((timer?.tick ?? 0) > otpRetryTimeInSeconds) {
        //     timer?.cancel();
        //     update();
        //   } else {
        //     update([retryOtpId]);
        //   }
        // });
      } else {
        otpsent = false;
        reotpsent = false;
        sendingOtp = false;
        showAppSnackBar("User Not Registered");
        update();
      }
    } catch (e) {
      sendingOtp = false;
      otpsent = false;
      reotpsent = false;
      debugPrint(e.toString());
      update();
    }
  }

  requestOTPEmail({required BuildContext context}) async {
    sendingOtp = true;
    update();
    try {
      final otp = Random().nextInt(999999);

      QuerySnapshot<Map<String, dynamic>> usersCollection = await FBFireStore
          .users
          .where('email', isEqualTo: emailctrl.text.trim().toLowerCase())
          .get();
      if (usersCollection.docs.isNotEmpty) {
        if (usersCollection.docs.first.data()['archived']) {
          sendingOtp = false;
          update();
          return showAppSnackBar("User no longer Exist. Contact Admin.");
        }
        // // Document Exist
        debugPrint("USER ALREADY EXIST!!!!!!!");
        // Save Otp
        newUserEmailOtp = otp;
        newUserEmailOtpSentOn = DateTime.now();
        await FBFireStore.users.doc(usersCollection.docs.first.id).update({
          'otp': otp,
          'otpTime': FieldValue.serverTimestamp(),
        });
        // Send OTP Email
        await FBFunctions.ff.httpsCallable('sendOtpEmail').call(
          {
            'email': emailctrl.text.trim().toLowerCase(),
            'otp': otp,
          },
        );
        sendingOtp = false;
        otpsent = true;
        reotpsent = true;
        // retryOtpStopWatch.start();
        update();
        // timer = Timer.periodic(const Duration(seconds: 1), (_) {
        //   if ((timer?.tick ?? 0) > otpRetryTimeInSeconds) {
        //     timer?.cancel();
        //     update();
        //   } else {
        //     update([retryOtpId]);
        //   }
        // });
      } else {
        otpsent = false;
        reotpsent = false;
        sendingOtp = false;
        showAppSnackBar("User Not Found");
        update();
      }
    } catch (e) {
      sendingOtp = false;
      otpsent = false;
      reotpsent = false;
      debugPrint(e.toString());
      update();
    }
  }

  Future<String?> validateEmail({required BuildContext context}) async {
    QuerySnapshot<Map<String, dynamic>> franchisesSnapshot = await FBFireStore
        .users
        .where("email", isEqualTo: emailctrl.text.trim().toLowerCase())
        .get();
    if (franchisesSnapshot.docs.isNotEmpty) {
      debugPrint("user exist");

      Map<String, dynamic> mapData = franchisesSnapshot.docs[0].data();

      if (mapData["notavailable"] != null && mapData["notavailable"] == false) {
        return null;
      }

      return "Your account is disabled by admin.";
    }
    debugPrint("user not exist");

    return "Workshop doesn't exist";
  }

  confirmEmailOTP() async {
    // lploader = true;
    resendloader = true;
    update();

    try {
      final res = await FBFireStore.users
          .where('email', isEqualTo: emailctrl.text.trim().toLowerCase())
          .get();

      if (res.docs.isNotEmpty) {
        // // Doc Found
        debugPrint("Old User");
        final userDocData = res.docs.first;

        if (emailctrl.text == "<EMAIL>") {
          if (otpctrl.text.trim() == "000000") {
            try {
              await FirebaseAuth.instance.signInWithEmailAndPassword(
                email: "<EMAIL>",
                password: userDocData['password'],
              );
            } on FirebaseAuthException catch (e) {
              debugPrint(e.toString());
              resendloader = false;
              update();
              return;
            }
          } else {
            resendloader = false;
            update();
            return;
          }

          resendloader = false;
          update();
          return;
        } else if (userDocData['otp'].toString() ==
                otpctrl.text.toLowerCase().trim() &&
            (userDocData['otpTime']
                    ?.toDate()
                    .add(const Duration(minutes: 5))
                    .isAfter(DateTime.now()) ??
                false)) {
          await FirebaseAuth.instance.signInWithEmailAndPassword(
            email: emailctrl.text.trim().toLowerCase(),
            password: userDocData['password'],
          );
        }
      }
      // lploader = false;
      resendloader = false;
      update();
    } on FirebaseAuthException catch (e) {
      // lploader = false;
      resendloader = false;
      update();
      debugPrint(e.toString());
      debugPrint(e.code.toString());
      if (e.code == 'invalid-verification-code') {
        showAppSnackBar("Invalid OTP or OTP Expired!");
      } else {
        showAppSnackBar(e.message?.toString() ?? "Something went wrong!");
      }
    } catch (e) {
      // lploader = false;
      resendloader = false;
      update();
      debugPrint(e.toString());
      showAppSnackBar("Something went wrong. Please try again.");
    }
  }
}
