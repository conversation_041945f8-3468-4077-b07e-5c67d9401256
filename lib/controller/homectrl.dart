import 'dart:async';
import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:foodcorp/models/custom_notifications_model.dart';
import 'package:foodcorp/models/loan_model.dart';
import 'package:foodcorp/models/notifications_model.dart';
import 'package:foodcorp/models/subsidiary_ledger_model.dart';
import 'package:foodcorp/models/transaction_model.dart';
import 'package:foodcorp/models/user_monthlyrecord_model.dart';
import 'package:get/get.dart';
import 'package:foodcorp/models/user_model.dart';
import 'package:package_info_plus/package_info_plus.dart';
import '../models/districtoffice_model.dart';
import '../models/expenses_model.dart';
import '../models/settings_model.dart';
import '../shared/firebase.dart';

class HomeCtrl extends GetxController {
  bool dataloaded = false;
  bool fcmTokenChecked = false;
  bool versionSupported = true;
  SettingsModel? settings;
  UserModel? users;
  SubsidiaryLedgerModel? subsidiaryLedger;
  UserDataModel? subsidiaryUserData;
  List<MonthlyLedgerModel>? monthlyLedgerData;
  List<LoanModel> allLoans = <LoanModel>[];

  List<DistrictOfficeModel> districtoffice = <DistrictOfficeModel>[];
  List<Expense> expenses = <Expense>[];
  List<LoanModel> activeLoans = <LoanModel>[];
  List<LoanModel> completedLoans = <LoanModel>[];
  List<LoanModel> appliedLoans = <LoanModel>[];
  List<TransactionModel> transactions = <TransactionModel>[];
  List<NotificationsModel> notifications = <NotificationsModel>[];
  List<CustomNotificationsModel> customnotifications =
      <CustomNotificationsModel>[];
  List<CustomNotificationsModel> globalcustomnotificationsdata =
      <CustomNotificationsModel>[];
  List<UserMonthlyRecordModel> userMonthlyRecord = [];
  StreamSubscription<DocumentSnapshot<Map<String, dynamic>>>? setsStream;
  StreamSubscription<DocumentSnapshot<Map<String, dynamic>>>? expenseStream;
  StreamSubscription<DocumentSnapshot<Map<String, dynamic>>>?
      districtofficeStream;
  StreamSubscription<DocumentSnapshot<Map<String, dynamic>>>? usersStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? loansStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? activeLoansStream;

  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? bannersStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? transactionsStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>?
      userMonthlyRecordStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>?
      subsidiaryLedgerRecordStream;
  StreamSubscription<DocumentSnapshot<Map<String, dynamic>>>?
      monthlyLedgerRecordStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>?
      subsidiaryUserDataStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>?
      notificationsDataStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>?
      customnotificationsDataStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>?
      globalcustomnotificationsdataStream;
  List<String> topics = [];
  @override
  void onInit() {
    super.onInit();
    authStatus();
    getdistrictofficedata();
    getsettingsdata();
  }

  authStatus() async {
    try {
      FBAuth.auth.authStateChanges().listen(
        (event) {
          if (event != null) getusersdata();
        },
      );
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  // getloandata() async {
  //   try {
  //     loansStream?.cancel();
  //     loansStream = FBFireStore.loan
  //         .where('uid', isEqualTo: FBAuth.auth.currentUser?.uid)
  //         .where('rejectionDate', isEqualTo: null)
  //         .where('rejectionReason', isEqualTo: null)
  //         .snapshots()
  //         .listen((event) {
  //       allLoans = event.docs.map((e) => LoanModel.fromSnap(e)).toList();
  //       update();
  //       print("allLoans : ${allLoans.length}");
  //     });
  //   } catch (e) {
  //     debugPrint(e.toString());
  //   }
  // }

  getactiveloans() async {
    try {
      activeLoansStream?.cancel();
      activeLoansStream = FBFireStore.loan
          .where('uid', isEqualTo: FBAuth.auth.currentUser?.uid)
          .where('approvedOn', isNotEqualTo: null)
          .where('rejectionDate', isEqualTo: null)
          .where('rejectionReason', isEqualTo: null)
          .where('isSettled', isEqualTo: false)
          .where('settledOn', isEqualTo: null)
          .where('isNew', isEqualTo: false)
          .snapshots()
          .listen(
        (event) {
          activeLoans = event.docs.map((e) => LoanModel.fromSnap(e)).toList();
          update();
          // print("active loans : $activeLoans");
          // print(
          //     "acloan : ${activeLoans.first.applicationNo} ${activeLoans.first.loanType}");
        },
      );
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  getcompletedloans() async {
    try {
      FBFireStore.loan
          .where('isSettled', isEqualTo: true)
          .where('uid', isEqualTo: FBAuth.auth.currentUser?.uid)
          .snapshots()
          .listen(
        (event) {
          completedLoans =
              event.docs.map((e) => LoanModel.fromSnap(e)).toList();

          update();
        },
      );
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  getappliedloans() async {
    try {
      FBFireStore.loan
          .where('isSettled', isEqualTo: false)
          .where('isNew', isEqualTo: true)
          .where('approvedOn', isEqualTo: null)
          .where('settledOn', isEqualTo: null)
          .where('uid', isEqualTo: FBAuth.auth.currentUser?.uid)
          .snapshots()
          .listen(
        (event) {
          appliedLoans = event.docs.map((e) => LoanModel.fromSnap(e)).toList();

          update();
        },
      );
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  getusersdata() async {
    try {
      // print("FBAuth.auth.currentUser?.uid : ${FBAuth.auth.currentUser?.uid}");
      usersStream?.cancel();
      usersStream = FBFireStore.users
          .doc(FBAuth.auth.currentUser?.uid)
          .snapshots()
          .listen((event) {
        // print("event : ${event.exists}");
        if (event.exists) {
          // print("eventttttt --- user");
          users = UserModel.fromSnap(event);
          // print("User data loaded: ${users?.toJson()}");
          topics.clear();
          // print(
          // "--------------------------------------${(event.data()?['topics']) is List}");
          topics.addAll(((event.data()?['topics']) as List?)
                  ?.map((e) => e.toString())
                  .toList() ??
              <String>[]);
          getusermonthlyrecorddata();
          getsubsidiaryledgerdata();
          // getdistrictofficedata();
          getdexpensedata();
          // getloandata();
          getactiveloans();
          getcompletedloans();
          getappliedloans();
          gettransactionsdata();
          getnotificationsdata();
          getcustomnotificationsdata();
          getglobalcustomnotificationsdata();
          setFcmToken(event.data()?['tokens'] ?? <String>[]);
          // print("User topics: $topics");
        } else {
          users = null;
        }
        dataloaded = true;

        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  getdistrictofficedata() async {
    try {
      FBFireStore.districtoffice.snapshots().listen((event) {
        districtoffice =
            event.docs.map((e) => DistrictOfficeModel.fromSnap(e)).toList();

        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  getdexpensedata() async {
    try {
      FBFireStore.expense.snapshots().listen((event) {
        expenses = event.docs.map((e) => Expense.fromSnap(e)).toList();
        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  getsettingsdata() async {
    try {
      setsStream?.cancel();
      setsStream = FBFireStore.settings.snapshots().listen((event) {
        if (event.exists) {
          settings = SettingsModel.fromSnap(event);
        } else {
          settings = null;
        }

        checkVersionSupported();
        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  gettransactionsdata() {
    try {
      transactionsStream?.cancel();
      transactionsStream = FBFireStore.transactions
          .where("uId", isEqualTo: FBAuth.auth.currentUser?.uid)
          .orderBy("createdAt", descending: true)
          .snapshots()
          .listen((event) {
        transactions =
            event.docs.map((e) => TransactionModel.fromSnap(e)).toList();
        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  getusermonthlyrecorddata() {
    try {
      userMonthlyRecordStream?.cancel();
      userMonthlyRecordStream = FBFireStore.usermonthly
          .where("selectedyear", isEqualTo: DateTime.now().year)
          // .where("selectedmonth", isEqualTo: DateTime.now().month)
          .where("cpfNo", isEqualTo: users?.cpfNo)
          .snapshots()
          .listen((event) {
        userMonthlyRecord =
            event.docs.map((e) => UserMonthlyRecordModel.fromSnap(e)).toList();

        userMonthlyRecord.sort((a, b) {
          int? monthA = a.selectedmonth;
          int? monthB = b.selectedmonth;

          int? adjustedMonthA = monthA! >= 4 ? monthA : monthA + 12;
          int? adjustedMonthB = monthB! >= 4 ? monthB : monthB + 12;

          return adjustedMonthA.compareTo(adjustedMonthB);
        });

        if (userMonthlyRecord.isEmpty) {
          return;
        }
        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  getsubsidiaryledgerdata() {
    try {
      subsidiaryLedgerRecordStream?.cancel();
      subsidiaryLedgerRecordStream = FBFireStore.subsidiaryledger
          .where("year", isEqualTo: DateTime.now().year)
          .snapshots()
          .listen((event) {
        if (event.size != 0) {
          subsidiaryLedger = SubsidiaryLedgerModel.fromSnap(event.docs.first);

          FBFireStore.subsidiaryledger
              .doc(event.docs.first.id)
              .collection("userData")
              .doc(FBAuth.auth.currentUser?.uid)
              .snapshots()
              .listen((querySnapshot) {
            subsidiaryUserDataStream?.cancel();
            subsidiaryUserData = UserDataModel.fromSnap(querySnapshot);

            final abc = subsidiaryUserData?.monthlyEntries
                .map((e) => monthlyLedgerData);
          });
        }
      });
    } catch (e) {
      debugPrint("Error in getSubsidiaryLedgerData in ctrl : $e");
    }
  }

  getnotificationsdata() {
    try {
      notificationsDataStream?.cancel();
      notificationsDataStream = FBFireStore.notifications
          .where('uId', isEqualTo: FBAuth.auth.currentUser?.uid)
          .orderBy('createdAt', descending: true)
          .limit(12)
          .snapshots()
          .listen(
        (event) {
          notifications =
              event.docs.map((e) => NotificationsModel.fromSnap(e)).toList();
          update();
        },
      );
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  getcustomnotificationsdata() {
    try {
      customnotificationsDataStream?.cancel();
      customnotificationsDataStream = FBFireStore.customnotifications
          .where("districtOffice", isEqualTo: users?.districtoffice)
          .orderBy('createdAt', descending: true)
          .limit(12)
          .snapshots()
          .listen(
        (event) {
          customnotifications = event.docs
              .map((e) => CustomNotificationsModel.fromSnap(e))
              .toList();
          update();
        },
      );
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  getglobalcustomnotificationsdata() {
    try {
      globalcustomnotificationsdataStream?.cancel();
      globalcustomnotificationsdataStream = FBFireStore.customnotifications
          .where("topic", isEqualTo: "global")
          .where("districtOffice", isEqualTo: null)
          .orderBy('createdAt', descending: true)
          .limit(12)
          .snapshots()
          .listen(
        (event) {
          globalcustomnotificationsdata = event.docs
              .map((e) => CustomNotificationsModel.fromSnap(e))
              .toList();
          update();
          // print(
          //     "globalcustomnotificationsdata : $globalcustomnotificationsdata");
        },
      );
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  setFcmToken(List tokens) async {
    try {
      if (fcmTokenChecked) return;
      fcmTokenChecked = true;

      NotificationSettings settings =
          await FirebaseMessaging.instance.requestPermission(
        alert: true,
        badge: true,
        sound: true,
      );

      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        String? fcmToken = await FirebaseMessaging.instance.getToken();
        // debugPrint('FCMTOKEN::::::: $fcmToken');

        if (fcmToken != null) {
          if (tokens.length > 7) {
            await FBFireStore.users.doc(FBAuth.auth.currentUser?.uid).update({
              'tokens': [fcmToken]
            });
          } else {
            await FBFireStore.users.doc(FBAuth.auth.currentUser?.uid).update({
              'tokens': FieldValue.arrayUnion([fcmToken])
            });
          }
        }
      } else {
        debugPrint("User declined notification permissions");
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  checkVersionSupported() async {
    if (settings == null) return;
    final packageInfo = await PackageInfo.fromPlatform();

    String currentBuildVersion = packageInfo.buildNumber;
    String firebaseBuildVersion = kIsWeb
        ? settings!.andbuildNumber // Fallback or separate Web version if needed
        : (Platform.isAndroid
            ? settings!.andbuildNumber
            : settings!.iosbuildNumber);

    versionSupported =
        int.parse(currentBuildVersion) >= int.parse(firebaseBuildVersion);
    update();
  }

  // setFcmToken(List tokens) async {
  //   try {
  //     if (fcmTokenChecked) return;
  //     fcmTokenChecked = true;
  //     String? fcmToken = await FirebaseMessaging.instance.getToken();
  //     debugPrint('FCMTOKEN::::::: $fcmToken');
  //     if (fcmToken != null) {
  //       if (tokens.length > 7) {
  //         await FBFireStore.users.doc(FBAuth.auth.currentUser?.uid).update({
  //           'tokens': [fcmToken]
  //         });
  //       } else {
  //         await FBFireStore.users.doc(FBAuth.auth.currentUser?.uid).update({
  //           'tokens': FieldValue.arrayUnion([fcmToken])
  //         });
  //       }
  //     }
  //   } catch (e) {
  //     debugPrint(e.toString());
  //   }
  // }

  @override
  void onClose() {
    setsStream?.cancel();
    expenseStream?.cancel();
    districtofficeStream?.cancel();
    usersStream?.cancel();
    loansStream?.cancel();
    activeLoansStream?.cancel();
    bannersStream?.cancel();
    transactionsStream?.cancel();
    userMonthlyRecordStream?.cancel();
    subsidiaryLedgerRecordStream?.cancel();
    monthlyLedgerRecordStream?.cancel();
    notificationsDataStream?.cancel();
    customnotificationsDataStream?.cancel();
    super.onClose();
  }
}
