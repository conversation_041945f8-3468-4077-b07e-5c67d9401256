import 'dart:io';
import 'dart:ui';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'controller/authctrl.dart';
import 'controller/homectrl.dart';
import 'shared/methods.dart';
import 'shared/router.dart';
import 'views/splash_screen.dart';
import 'firebase_options.dart';

// ---------------------- Background Message Handler ----------------------

@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  if (Platform.isIOS) {
    flutterLocalNotificationsPlugin.show(
      DateTime.now().microsecond,
      message.notification?.title ?? '',
      message.notification?.body ?? '',
      const NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iOSPlatformChannelSpecifics,
      ),
    );
  } else {
    debugPrint(
        "Received background message on web: ${message.notification?.title}");
  }
}

final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
    FlutterLocalNotificationsPlugin();

AndroidNotificationChannel? channel;

// ----------------------  WEB TOKEN FETCH FUNCTION ----------------------

void fetchWebToken() async {
  if (kIsWeb) {
    final token = await FirebaseMessaging.instance.getToken(
      vapidKey:
          "BN2C2QwaDDYgPwGD8U3Gp6_STaDpC9uGBD4EHhUrVh4e-7e_VLzcuWSQYtnUcMafyXAdpVHACCgD967RQIb3YaU",
    );
    debugPrint("WEB FCM TOKEN: $token");
  }
}

// ---------------------- Main ----------------------

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

  if (!kIsWeb) {
    channel = const AndroidNotificationChannel(
      'fci',
      'FoodCorpChannel',
      importance: Importance.high,
    );

    await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(channel!);
  }

  await FirebaseMessaging.instance.setForegroundNotificationPresentationOptions(
    alert: true,
    badge: true,
    sound: true,
  );

  await FirebaseMessaging.instance.requestPermission(
    alert: true,
    badge: true,
    sound: true,
  );

  // 🔑 Fetch Web Token
  fetchWebToken();

  const androidInit = AndroidInitializationSettings('foodcorpimage4_removebg');
  final iosInit = DarwinInitializationSettings(
    requestAlertPermission: true,
    requestBadgePermission: true,
    requestSoundPermission: true,
  );

  await flutterLocalNotificationsPlugin.initialize(
    InitializationSettings(android: androidInit, iOS: iosInit),
  );

  FirebaseMessaging.onMessage.listen((msg) {
    if (kIsWeb) {
      final nt = msg.notification;
      if (nt != null) {
        Get.snackbar(
          nt.title ?? 'Notification',
          nt.body ?? '',
          snackPosition: SnackPosition.TOP,
        );
      }
    } else {
      flutterLocalNotificationsPlugin.show(
        DateTime.now().microsecond,
        msg.notification?.title ?? '',
        msg.notification?.body ?? '',
        const NotificationDetails(
          android: androidPlatformChannelSpecifics,
          iOS: iOSPlatformChannelSpecifics,
        ),
      );
    }
  });

  runApp(const MyApp());
}

// ---------------------- App ----------------------

class MyApp extends StatefulWidget {
  const MyApp({super.key});
  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  bool splashVisible = true;

  @override
  void initState() {
    super.initState();
    Get.put(Authctrl());
    Get.put(HomeCtrl());

    Future.delayed(const Duration(seconds: 1), () {
      setState(() => splashVisible = false);
    });
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(statusBarColor: Colors.transparent),
    );

    return ScreenUtilInit(
      minTextAdapt: true,
      designSize: const Size(430, 932),
      builder: (_, child) => MaterialApp.router(
        scaffoldMessengerKey: snackbarKey,
        routerConfig: appRouter,
        title: 'Food-Corp-Admin',
        debugShowCheckedModeBanner: false,
        scrollBehavior: MyCustomScrollBehavior(),
        builder: (_, child) {
          return splashVisible
              ? const SplashScreen()
              : (child ?? const SizedBox());
        },
      ),
    );
  }
}

// ---------------------- Notification Details ----------------------

const androidPlatformChannelSpecifics = AndroidNotificationDetails(
  'fci',
  'fci',
  importance: Importance.max,
  showWhen: false,
  icon: 'foodcorpimage4_removebg',
  playSound: true,
);

const iOSPlatformChannelSpecifics = DarwinNotificationDetails(
  presentAlert: true,
  presentBadge: true,
  presentSound: true,
);

// ---------------------- Scroll Behavior ----------------------

class MyCustomScrollBehavior extends MaterialScrollBehavior {
  @override
  Set<PointerDeviceKind> get dragDevices => {
        PointerDeviceKind.touch,
        PointerDeviceKind.mouse,
        PointerDeviceKind.stylus,
        PointerDeviceKind.trackpad,
        PointerDeviceKind.unknown,
      };
}
