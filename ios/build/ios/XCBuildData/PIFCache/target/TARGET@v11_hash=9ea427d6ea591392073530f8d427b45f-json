{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988bf903e1b8ce4e9af6e165f3c167d639", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98c24be27895f40b4dd283e9350af1361e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9885686e8e6d742eb24f8cfc75fb760403", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e987369de025b47e1caff193a8dcccd539c", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9885686e8e6d742eb24f8cfc75fb760403", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98512cfc445cffe630d186e4c148005a06", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98372583c261c5ac0c699f3720ccba762d", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98438fd8016d06a3ef6a9862a6c3fd7767", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988d774bbf3fdf35229f2f6cbb1822b9af", "guid": "bfdfe7dc352907fc980b868725387e9809fb91db69cfb1fca95b8a751db75039"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878c01fc4a3d5871876f6157b2cdb2ff2", "guid": "bfdfe7dc352907fc980b868725387e987c7ff6480b04ed0f96f4278869a89a23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f6c049288550be18dcee2adfdd78727", "guid": "bfdfe7dc352907fc980b868725387e983abac5c187210f0a2704eb7bd901faa6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986287e586b72e9945a6209ee8396f7e11", "guid": "bfdfe7dc352907fc980b868725387e98539dd1337fe1e5dff8cc5876c2cf397e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986da10f9a01c8883739dcf27e0e6abcf4", "guid": "bfdfe7dc352907fc980b868725387e989ef02686b1f7ad67128feb3bb085a2a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98143160c19f514d265f8378ca0462e421", "guid": "bfdfe7dc352907fc980b868725387e9806fc709fc3c9d6c3f199b083318ba65c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98133193424b8b37d15db5d3605d3b566e", "guid": "bfdfe7dc352907fc980b868725387e98b5939b0b3d33f79e00c1e12ecf982d55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840c1ad42830b64a33cbb61fdcf22df2d", "guid": "bfdfe7dc352907fc980b868725387e98c76141ceb7bdc42c1fb3c11cc95d9d3a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c11734e7386a4d9a08fda036d8dabbab", "guid": "bfdfe7dc352907fc980b868725387e988c6d5a09c5b5a83013ca5f6447a53f24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982751d398f3fcd92c4134f72173e6382f", "guid": "bfdfe7dc352907fc980b868725387e983901da3e6a5e99659c264bc2c6a6d435"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c6593b509aae687c0273ab014b00fff", "guid": "bfdfe7dc352907fc980b868725387e982d3cdeb848bc2ef21e702edd384a6cea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa70ebf4f0b43d159225ec399ee0dfe4", "guid": "bfdfe7dc352907fc980b868725387e98bdb0f1e9f77e0adb49711b51d6334323"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98017b91f22bd4b985511afdc8206442c8", "guid": "bfdfe7dc352907fc980b868725387e985f43d020d8f53571fe968d9e6bba41e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987660f006efe41fd1d9bff1ed651f1d55", "guid": "bfdfe7dc352907fc980b868725387e98267fe5ecde9dff56387cb1ea030f3b1b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98570c66941d9d47517b7ba914e4e4ba68", "guid": "bfdfe7dc352907fc980b868725387e9847aa20d75028ccfdc1e436cc79dc6581"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b75f5c075b1054c2bfd746192162d04f", "guid": "bfdfe7dc352907fc980b868725387e98a163bcb544c8dc6ed95f263d7e0e4ef1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897e490d03358e46604f41f54e78f23d1", "guid": "bfdfe7dc352907fc980b868725387e9809970caef583401682d32df75d2c8366"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e53e8e2354238c769698067a610a3d7", "guid": "bfdfe7dc352907fc980b868725387e9825ce480ab7e6761932ac5dfe88f0e8c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981dac09cb2763aacc2b33d6c073a658ec", "guid": "bfdfe7dc352907fc980b868725387e98fb407f30796fe61612e4c3faf7335930"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c654c91ebceb3a62a4426a40e76b673", "guid": "bfdfe7dc352907fc980b868725387e9871e7bb2f2b3ab30d0c81356f61bcf661"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aaff5fe63a0b992893312f2e26713fef", "guid": "bfdfe7dc352907fc980b868725387e984a345be53c81e1845dd1afa1b3564090"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4446161d2a45a77c374b9e0b7976245", "guid": "bfdfe7dc352907fc980b868725387e980634c5b6e3a5fcf4df4e3dbc2ba76ca4"}], "guid": "bfdfe7dc352907fc980b868725387e98bc9ac27218ac2d011cf0981b9b0ccd6e", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3", "name": "DKImagePickerController-DKImagePickerController", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ab5e1f747dfe477b655528b07584898d", "name": "DKImagePickerController.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}