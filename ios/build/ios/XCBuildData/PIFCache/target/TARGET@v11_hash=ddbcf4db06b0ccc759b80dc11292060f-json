{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9823d3ca5d2e1d3ea28052bc21aff5dc07", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseDynamicLinks/FirebaseDynamicLinks-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseDynamicLinks/FirebaseDynamicLinks.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseDynamicLinks", "PRODUCT_NAME": "FirebaseDynamicLinks", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ed1a71a677696845f08c10c5e5649670", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98204e33bb39a55c43fc26b58a35203030", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseDynamicLinks/FirebaseDynamicLinks-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseDynamicLinks/FirebaseDynamicLinks.modulemap", "PRODUCT_MODULE_NAME": "FirebaseDynamicLinks", "PRODUCT_NAME": "FirebaseDynamicLinks", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d089312189ca5285ca6efe35c2c41074", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98204e33bb39a55c43fc26b58a35203030", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseDynamicLinks/FirebaseDynamicLinks-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseDynamicLinks/FirebaseDynamicLinks.modulemap", "PRODUCT_MODULE_NAME": "FirebaseDynamicLinks", "PRODUCT_NAME": "FirebaseDynamicLinks", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98edee90f6b944e198f2190e46670ebe26", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e801de54c3c4e24efe940666c370a5d0", "guid": "bfdfe7dc352907fc980b868725387e983043343779e629389b80d326470a6e12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98191d20ad79d0670d9415a60e06c9e296", "guid": "bfdfe7dc352907fc980b868725387e982a1910cd8b56055fc6c617e1c6108d8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d31704b7bcc22debb57d189b5d729b60", "guid": "bfdfe7dc352907fc980b868725387e98f966b21381f17385974d08ec3e0f1c41", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc2a947c3c1e44fd853d42d9d3e837a3", "guid": "bfdfe7dc352907fc980b868725387e98c952114302f26f24eb2740eadbd6a852"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984600ee69af3b59c19595c989b31ffff7", "guid": "bfdfe7dc352907fc980b868725387e98a9ee4ae08f61e8c2e3069a422708aa8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883dea5a6fedb412806f39e3bbf7f0e87", "guid": "bfdfe7dc352907fc980b868725387e9895c680ec16f13655c11707f1f6b342a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982845fb264d4736be5040fa41dff55947", "guid": "bfdfe7dc352907fc980b868725387e9820ecc918fe49b078e826214e9bdc007a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98714be27fe7ddc7ce6916e23a055ff806", "guid": "bfdfe7dc352907fc980b868725387e9860246af82c75c943d693c6918b23dc66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832a78f588c62ecdf98aa660edd9a4a09", "guid": "bfdfe7dc352907fc980b868725387e98c458c21112fe2fa87e6c08a86bf54f0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980edb87e6e1e0140dcde3bf02295e7abf", "guid": "bfdfe7dc352907fc980b868725387e98e89d282d1bdc6b41d98cfcd0508c82fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859a6cd85bd83713190d43fda8fad770e", "guid": "bfdfe7dc352907fc980b868725387e98e7d337e08f7234586849b55d5640b4e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfda054d6522d630a94990722ea4b73b", "guid": "bfdfe7dc352907fc980b868725387e98e85533d97f6087daee689daf06c96d31"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f893f59328dab71aac5acbe294b440c3", "guid": "bfdfe7dc352907fc980b868725387e98f2766b378052363da6fe6893f573c57c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984392d9c37cd046801c580f4ed6f54630", "guid": "bfdfe7dc352907fc980b868725387e988f4b41962c6f53a810bc1bee168f4d1e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6410a108c095d5d651c2cf0c58f963c", "guid": "bfdfe7dc352907fc980b868725387e98aabc13f844f75cdca5edd636f9b45adf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983912b17682611ddc197e8a654a131085", "guid": "bfdfe7dc352907fc980b868725387e985ad2cf350501fbdd13f9833b040ab5f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b2842fcd6486b6e62bc3d45b117f803", "guid": "bfdfe7dc352907fc980b868725387e980e07cc0ff81b38bf4715f726d375fbb3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c7c73214cf38ac60a1b690579b3cec4", "guid": "bfdfe7dc352907fc980b868725387e987d8d5556b7f2700c330ed5b841d8dd78"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc8ae66c4a41e72b2bc3df356a398f5d", "guid": "bfdfe7dc352907fc980b868725387e9814f2eb4bcfeca1bd23e0ce5f1361e4f6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985472fd80c0165c5370a9f043d70de8c6", "guid": "bfdfe7dc352907fc980b868725387e98c4c5e7f3a184364695042858886609d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822b5243701d5e2c71c75bb7e957d9da7", "guid": "bfdfe7dc352907fc980b868725387e9814c6154645e9dbe3c0693f111ee176f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1504db8a3bbe574525ebf38c9e2fe20", "guid": "bfdfe7dc352907fc980b868725387e9803fc79b30c0d2bd8bbc0df2c1efbbfa1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882faffc6177da7c7e8edce76d6630206", "guid": "bfdfe7dc352907fc980b868725387e988274d290f1e773d7ff711450edf938b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ad4fe1ca64593525e03ad3fdf6b96d4", "guid": "bfdfe7dc352907fc980b868725387e98b0dfd8fb9f5896dcf19257baa3fd06ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bfcd7bb1d3c99a20a530d264fbcf3eb6", "guid": "bfdfe7dc352907fc980b868725387e98bca3b98e3d2080ad500e517f4f8bf9ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bbb4b4fb59fab885fee921750e336d3", "guid": "bfdfe7dc352907fc980b868725387e981317e960b7ca2289e56aa12fd7a574d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98476d9382c0890ff6da68a969b0411777", "guid": "bfdfe7dc352907fc980b868725387e986f7e56811bda7ecec2e17d32b4e8a7cc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881b52c5266c15f1661de6f43350cd13b", "guid": "bfdfe7dc352907fc980b868725387e9805be9ba0440fda6ec7e8a9d47c4611fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7d9d7b5a4d9f8cde34990d7c8c0547a", "guid": "bfdfe7dc352907fc980b868725387e98148f9ff14a6e3deeb2c9699c81e63e8e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e3f31f32147994604c6894f0766bad2", "guid": "bfdfe7dc352907fc980b868725387e98aeeb4df78d5058af7a3995fb2da1cc35", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a8cd02104568655416acdae6b2f5917", "guid": "bfdfe7dc352907fc980b868725387e9861cce35f25154aacd76e4be624633dfb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980fa1aa5eaa7abc8e40be945d0e4e1cda", "guid": "bfdfe7dc352907fc980b868725387e98c5c5f759af005a3c7a59ce37a7c62de6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cdf2aa0845954ee725830852be6fdf66", "guid": "bfdfe7dc352907fc980b868725387e9807a3ecd0d320ac58ca2081bfac2b9941"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879958da7ad46cfc931cd296b46dfd993", "guid": "bfdfe7dc352907fc980b868725387e98e301e2748ccae647251cc8ba48155682"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aad235307b616993d4273cb8527a1da6", "guid": "bfdfe7dc352907fc980b868725387e98e0490201f5c54c0eb235c0d6ed8bd6b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987401951026f70d4a0c1d56c40b4ae936", "guid": "bfdfe7dc352907fc980b868725387e9806dfa9d3b1fefefecd02ffc0a3de5fb0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f11acbaeb98921a98ec3172976215fab", "guid": "bfdfe7dc352907fc980b868725387e98ee70c4e8f486e61c8a093da14cd02c33"}], "guid": "bfdfe7dc352907fc980b868725387e984e595df22734a267c90a7d8690fe35aa", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984bced4e815fe0412f56bb8a9bdafa44f", "guid": "bfdfe7dc352907fc980b868725387e9834df8ded1b19142ba83c5947a75afc51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b15bc0222f2cc2ffec119a040616a9ce", "guid": "bfdfe7dc352907fc980b868725387e98fd7315a1bf7d59d16a1be8c3a72c4c31"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989817c9bc28bf436f62a36580138bd000", "guid": "bfdfe7dc352907fc980b868725387e98929386f22787fe59d7693552755c3f39"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4917843137eaea14297974aa34470c3", "guid": "bfdfe7dc352907fc980b868725387e985a69b0932ad92e0fba15d0badb3e7d17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f05706202338a7c4416a0124a65068a", "guid": "bfdfe7dc352907fc980b868725387e98874d22fc51d8a2fbeaac89b75e96dc7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe1a77e57d88b8680cbf5c88abf3e77f", "guid": "bfdfe7dc352907fc980b868725387e983bebe953f462c1919ad81908133a9d91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a2df632a881592c8077f0d086b246b7", "guid": "bfdfe7dc352907fc980b868725387e988d14337adbbd808d5e6dfa011fd2def0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a29f20cd83dbf3745861ac5461d5f34", "guid": "bfdfe7dc352907fc980b868725387e982eadefce529ce0e0ae9723d446e18400"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc0380a73a2f8db762f1c0d3c747818f", "guid": "bfdfe7dc352907fc980b868725387e98693f6738718d5232c42f09b3c1a6401a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eab8394359851007196c882a0bf4deb1", "guid": "bfdfe7dc352907fc980b868725387e980227e38ae1440c2405857501a287cfa5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2f236c8441e1dc18d5d19233540d559", "guid": "bfdfe7dc352907fc980b868725387e98ad292e819bbc497955c4905adf0c24f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e4e05ec5ad449c59f3693a8bff06aa3", "guid": "bfdfe7dc352907fc980b868725387e98a7f949dad37d65630030f9cecf2e82b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5e0b65a95071eee56ce2cd2155b47d2", "guid": "bfdfe7dc352907fc980b868725387e98734a2681e583c546ef60d422d03c0af5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98312b84a3425a1f72209b3305d00bf3d9", "guid": "bfdfe7dc352907fc980b868725387e983e264d4d70037428bdcd2e2f1102ebca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980081e034fc65e1e1b9a841968926b54b", "guid": "bfdfe7dc352907fc980b868725387e98a10f7bd5c268b26a3119cf49dcbd71c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e50e0701fa11d15c214e73bd25c218b6", "guid": "bfdfe7dc352907fc980b868725387e985c72ad16a9abc<PERSON>badb4a3f8c8b17e26"}], "guid": "bfdfe7dc352907fc980b868725387e9851e0ca21015dc112b82a59758b911423", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989cc950460cd45fefc4c01dc41a9ce577", "guid": "bfdfe7dc352907fc980b868725387e98dad5ce510a163fcf3d881605457f0347"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e594fe67cbab8fb8a8a167b3f045d8ac", "guid": "bfdfe7dc352907fc980b868725387e9878852597d6fb25168f45bfa29dbb444b"}], "guid": "bfdfe7dc352907fc980b868725387e9813f5b6998cdd9a53e5d8cabeb87078ef", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c5c2c57a1eb6b2c6b7e9deae6eaeda91", "targetReference": "bfdfe7dc352907fc980b868725387e98e58827b88c0abca8fc7e8667fef9326e"}], "guid": "bfdfe7dc352907fc980b868725387e982c656d2d548c30b7d4614329216fd86b", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98e58827b88c0abca8fc7e8667fef9326e", "name": "FirebaseDynamicLinks-FirebaseDynamicLinks_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98b623f22c0d3d037d0450c736133d3c3e", "name": "FirebaseDynamicLinks", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98834460d3052fa1f4117c6696cee22bc3", "name": "FirebaseDynamicLinks.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}