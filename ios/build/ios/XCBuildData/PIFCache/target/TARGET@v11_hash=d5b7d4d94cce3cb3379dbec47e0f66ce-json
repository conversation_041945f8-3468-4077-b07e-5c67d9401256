{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982fce316f06bf508306616c840049ccab", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984fabd86db186fc8b205597ea845e668b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e88099431a6d7c60ac977174fda41b5c", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e96bec86c99a8f86dd3b52f9e8e24f58", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e88099431a6d7c60ac977174fda41b5c", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9831607cb10a1d6f99bdbee1f85bc1ae19", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986abc5e222cb29d77138c5e5169df52b2", "guid": "bfdfe7dc352907fc980b868725387e98918e11d7b8c95acf63a4d555d3df34ca", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9850a7db59a11272fa2738e921e96c4eb4", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985856713e38341d1fa4e093faf9c8801d", "guid": "bfdfe7dc352907fc980b868725387e98c0cc69832dd2bb501b3005257fc86923"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1d8858ca5f30a4011db1567cc25082f", "guid": "bfdfe7dc352907fc980b868725387e98b7c670f82da0fdcd98ba4741e1c2e29a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef0b63c3b2fc19b70695d725701ee549", "guid": "bfdfe7dc352907fc980b868725387e98fe2a98c0b09a92d0fac562b919285a04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811f9cc91c4ee4f09e92a8b9c9d9cae89", "guid": "bfdfe7dc352907fc980b868725387e98773042205d22a42ae80348348fd736dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f13a633be4e554f01e85fc9c20ae34f", "guid": "bfdfe7dc352907fc980b868725387e985aafedd0268c0700d48c4890ccce5bf2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff167d791fed26b06b0402e1a04d20dc", "guid": "bfdfe7dc352907fc980b868725387e984df182c5abed733c3d698cbf433e40ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae35d9045a7f4dd2ace1fc8431d9737c", "guid": "bfdfe7dc352907fc980b868725387e9867f5b46fe9c21dbe4dfbe23ab15bbf22"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859096f446ecb35a198aede67257887e4", "guid": "bfdfe7dc352907fc980b868725387e98928cdcdabe910599b135757d16e18056"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dac503aa6062a7f621a860ea11926abf", "guid": "bfdfe7dc352907fc980b868725387e98f03de15beb987339cc5f2e7867c6d48d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892a7063b65d35aedda5b6dcf3f9e54da", "guid": "bfdfe7dc352907fc980b868725387e9802685b898c94f478f66d4ae6c3d2ddf2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98229e9b7a347add80cccb3bd69cd4398f", "guid": "bfdfe7dc352907fc980b868725387e9882b14adc061dc7602b0e107969de935d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98635bf3296cb19601657cbc635534fffa", "guid": "bfdfe7dc352907fc980b868725387e98deaddfe042fa639ad2cdbf52c9f4e94b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b35a8d0122b0719de87accfd4324a5e7", "guid": "bfdfe7dc352907fc980b868725387e98a9d4d4f50f1c6587c3027351a78c2eb4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894d635bcb8bc45a11d2c918c5b06b6e4", "guid": "bfdfe7dc352907fc980b868725387e9858a108043851190dec0a9c8989be6391"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98168bcead83f06a1d4439bbbd7bd0c896", "guid": "bfdfe7dc352907fc980b868725387e98b4762378ce947796dedf4ec578a503cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c58a92a379bd4419f3e318486906d9e", "guid": "bfdfe7dc352907fc980b868725387e98511fa1702f6ae68c5b78d7061b888700"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f87840ceb7ec57c97183b668c30d5b17", "guid": "bfdfe7dc352907fc980b868725387e98bba29f7100c0f44d8c7830cdb54a7a6e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef7597ab1de24c9765c9701ab27ffd01", "guid": "bfdfe7dc352907fc980b868725387e98eda9608c753d870211963adc4f31d27b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a337b8845ac7e08eef17bdc47e1a7b5", "guid": "bfdfe7dc352907fc980b868725387e98aa93e35adcf5a1d9a59d23b8b327fd78"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d01552c4273c4e06afe6fdc384f69ff", "guid": "bfdfe7dc352907fc980b868725387e98178289940d0390087febd03f93665cec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb6daa807115341054ba93d3fabb2777", "guid": "bfdfe7dc352907fc980b868725387e9879f52dcdaf3898228cb922c14d06ee56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c87a049654f9cb7da27a8f56f34873c0", "guid": "bfdfe7dc352907fc980b868725387e98f75aced0871de1ef15bfb344885d5fcc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6c4e34c21a3b5ed5584151d4900bd2f", "guid": "bfdfe7dc352907fc980b868725387e98b2e9873e9936a554b7f941a45ad0404f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b11b35654e6bdfe5c7758120804eb6de", "guid": "bfdfe7dc352907fc980b868725387e9876f18cc71a8088a7be01c457443b73ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e53d8344117aad544c3b499c27e4076", "guid": "bfdfe7dc352907fc980b868725387e980516638261d82dfac5026a1b36be3f66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d26bde092ca519b6c85d0677e5872a0d", "guid": "bfdfe7dc352907fc980b868725387e9878d7d809ff9d670f7be1788ee1f65267"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6a044365c4b5d6cdb063cc844dd830a", "guid": "bfdfe7dc352907fc980b868725387e984afa68905915e5a1cfd75cc3041c431c"}], "guid": "bfdfe7dc352907fc980b868725387e98f64deb42913cb611a26f0d93c1af352d", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98035df92283b3317d4864a86d0283079e", "guid": "bfdfe7dc352907fc980b868725387e98463d16bcfac5bcdf6d56b2acd2b71f93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868749b359e79273527079e401f9730e8", "guid": "bfdfe7dc352907fc980b868725387e98ff98a4bdad2ce1d5ce271cb9dbdfecbf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989cc950460cd45fefc4c01dc41a9ce577", "guid": "bfdfe7dc352907fc980b868725387e98c1656e97e875289b678c5b7091cdcd08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855d12f4285a2af770b97895b9dd747a5", "guid": "bfdfe7dc352907fc980b868725387e980dcf9d9958855e998897af19a69bb848"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850edfe48d26d56c49ac8c1037d2cf42d", "guid": "bfdfe7dc352907fc980b868725387e98e9f5714370ab73e1a2d6fda7a0da1e46"}], "guid": "bfdfe7dc352907fc980b868725387e98c5efd7d04053a7664c9df0b501d7c442", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98d1edcfe48ecf3eefdafe703a5af3df13", "targetReference": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937"}], "guid": "bfdfe7dc352907fc980b868725387e98d500e1ceb61548db68036c55388f7234", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937", "name": "DKPhotoGallery-DKPhotoGallery"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986e90c628ccd44af657bee5ff4af2f692", "name": "DKPhotoGallery.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}