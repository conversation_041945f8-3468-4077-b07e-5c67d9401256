{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b1e14fca33cc3bfa0479099015883b9b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985e8676352411e95a6fd5d1bc32c5805f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98af38b8c286c3e0cbb57cac6c82e8175d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9819d6788e9c8ea114428bf5e6b462908a", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98af38b8c286c3e0cbb57cac6c82e8175d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9804b09fa009da0d9418f5e2601925f32d", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987cff8dd8e6855a6422c218b7e13621fb", "guid": "bfdfe7dc352907fc980b868725387e9877f16cd90073bc8ad55920d674cf6706", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba8ca6de7e510b4fa69accbde3ae4c87", "guid": "bfdfe7dc352907fc980b868725387e98439ef1335b3c12baedd4b8d98c79b8a1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2df36ee4423b346201ba9062afa83d9", "guid": "bfdfe7dc352907fc980b868725387e98cd8f76f88b4e589c96cafd28532d4d2e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b0359ab227bb0e091f5fffddddec370", "guid": "bfdfe7dc352907fc980b868725387e98e68c0fd22e2bd12a29c37f3625471087", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98032961fcfe4a350583310b64ed17b95e", "guid": "bfdfe7dc352907fc980b868725387e9884ff48af6bdd1775c5f02bedd1b6076e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852a06f258bf245c667569ed8f7d5ba74", "guid": "bfdfe7dc352907fc980b868725387e9815f5d27fecefe45eae13935dc6ae3619", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9882b52f9f618c1ba0b6e58636c1807767", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a1806146e7e375f843c8a7ce18490c1d", "guid": "bfdfe7dc352907fc980b868725387e98ca020e512f95399c370bf9969087decf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982101c2373f0f790b4d1437b67eb7fe02", "guid": "bfdfe7dc352907fc980b868725387e98d78cbab5ec5e7fae7b0bbe0a18fa1e37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d32cd7d59c4c10dcb30f8def60f29de", "guid": "bfdfe7dc352907fc980b868725387e9845941a747781e21cb8d08b18034803a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f8263a13932710e19a4b13273a6cd94", "guid": "bfdfe7dc352907fc980b868725387e98cbe93dca5096112524bbdeb7722c9203"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5f72b74cf6a0953b93d0f3ecc123323", "guid": "bfdfe7dc352907fc980b868725387e98822deef01ea6dbfa1ae232ae791b1da3"}], "guid": "bfdfe7dc352907fc980b868725387e98322bf1ac210f7bb1c0085650c2989c1c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989cc950460cd45fefc4c01dc41a9ce577", "guid": "bfdfe7dc352907fc980b868725387e981884036d8cf103eab5ff3db5b88502f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987aa69c67fdaf8a691629a3c30715ea6b", "guid": "bfdfe7dc352907fc980b868725387e98ab21d5f826a66956331381160b34dfc2"}], "guid": "bfdfe7dc352907fc980b868725387e98d8409b569b1fb593aa6c471ece4c549b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e980eaf16c8aa6b03557508f2ac33051e96", "targetReference": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1"}], "guid": "bfdfe7dc352907fc980b868725387e986f6f11bc23f41360e876d07c1c9b96a1", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1", "name": "GTMSessionFetcher-GTMSessionFetcher_Core_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f65e88472d384b1ba0888326befb3a8e", "name": "GTMSessionFetcher.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}