{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986bb97ac3b1fcef04d7dedf13e964cd58", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c2c4394764c33e032c26aeeaa73bf9bd", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986107ff8a562f7bf04745e26b22d5a825", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9835ef79e5a5b1378e685fde18042d59af", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986107ff8a562f7bf04745e26b22d5a825", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988b527d5dfa260ae3429b4ad163d74aa2", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9860610d3834049bbf6fe35e79d97358ae", "guid": "bfdfe7dc352907fc980b868725387e986894d7302d771377f816c37da762696e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98771aa6d5ee0bbec0c6138ad998924f68", "guid": "bfdfe7dc352907fc980b868725387e986b7cb0dea8dee938e0831659817a1208", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ccecec1d0d64248bc996ec9ab705142f", "guid": "bfdfe7dc352907fc980b868725387e98a6149fc3052a844fc1d76bd19f831c6f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987126a330b68538b785fec72729a51c27", "guid": "bfdfe7dc352907fc980b868725387e986add7f97076b1a81ca287f6135cac1d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98650aefeefb6c7aabe779d605f535e600", "guid": "bfdfe7dc352907fc980b868725387e986bf516baea4e220fe0b70ee5bb5fd494"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9793a575d68e821700caf85d2cfe2f4", "guid": "bfdfe7dc352907fc980b868725387e98917881a5ed5f004b068077b5e874cafa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2904c1e5fe8971032b78c27c338e795", "guid": "bfdfe7dc352907fc980b868725387e984afe2b090f6b6213dc85191aac219a18"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980012269e0e758683b9573f483eb8b655", "guid": "bfdfe7dc352907fc980b868725387e989d9f3364956ce6c2ca8e32ce88465cf8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4ca9f04a7e41ba3ae87640a81a4517e", "guid": "bfdfe7dc352907fc980b868725387e988fb7cfbca3b9df42c216a5ce80b505fa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f5c6b5b3133bc8a1a088585fcac62fd", "guid": "bfdfe7dc352907fc980b868725387e98d60206567bef3518865655a57580ccda"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98200ff392548613255b7bba6c76b4dd4a", "guid": "bfdfe7dc352907fc980b868725387e987a90c99a9f258670ab258f3b948a1d85", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983fd5a4f083159bb728852150e463ee50", "guid": "bfdfe7dc352907fc980b868725387e985e3d9924b2577438039dbc805bb1b742", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983fc72dc59e5edbdaa7aa52a17bce1198", "guid": "bfdfe7dc352907fc980b868725387e98c520ec2ab90bd4dad2784ca2fc3b54c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a5f0020b05a37e24d6a237442bd5336", "guid": "bfdfe7dc352907fc980b868725387e98640eff0ff2ac4ade4817d9f4c8131cb4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9ae71d95404d2d5a96e531a6d797fd5", "guid": "bfdfe7dc352907fc980b868725387e987a4edda4c36749670defeb13f6666953"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9330dcdd2cea10b3c939811742833b4", "guid": "bfdfe7dc352907fc980b868725387e98214aceda13371f498cba6185821f5625"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe3362bb301aea94a254ed134eef1936", "guid": "bfdfe7dc352907fc980b868725387e9847ee348516659c9f59a274788c026022"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2de8d4a862ba1e31aff0f9f2dcd92ce", "guid": "bfdfe7dc352907fc980b868725387e98cc6cf1370d18bfbe523be150a93cf867", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98773b0a624cede59755c27b91f81d0fd8", "guid": "bfdfe7dc352907fc980b868725387e9838cd962b6da22a701befd930a980c3db", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d6cba5682320c2d38c741f9fb854f5d", "guid": "bfdfe7dc352907fc980b868725387e98d73546ab67b9ce45a7bc510bbcd68bec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989507c07b1516414e80bf5258794f9b1c", "guid": "bfdfe7dc352907fc980b868725387e9818da7998cb7d6cee02e62e322eb64d58", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98080a08190c17ceb2dd6d87c5e1022afa", "guid": "bfdfe7dc352907fc980b868725387e980230f3560365664cf73e5e8ecef5fc99"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c85f847cd97e67fce86cf7ec4a0b27e1", "guid": "bfdfe7dc352907fc980b868725387e987c823921826d7f828108e90e8b7ecf55", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e987fae5ab791e59ac7b5fdefc75a99a59e", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98abd74f73d22b65b84731e77e11063d08", "guid": "bfdfe7dc352907fc980b868725387e989a290fa636746fdc24cd2ff46af32c6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f2cc8e25eea62f3f703fe515bf241ed", "guid": "bfdfe7dc352907fc980b868725387e98db1409a84d872c22f24411859b9210f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a28096a9571f2c06e2bad116f1119bf", "guid": "bfdfe7dc352907fc980b868725387e98b782de1999bbf1094421436ee2d5fecf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a4a604105f9bdc21094f361a84d4ddf", "guid": "bfdfe7dc352907fc980b868725387e988fdc82dabbfe61d7a59bdeb49231b7c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899fe6a4879ae5c5c29f60ea19a7a2342", "guid": "bfdfe7dc352907fc980b868725387e98813cdc1f74f68a7a7f6197369e2008e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d80e4ec7145b981f1087af3072fc4ea9", "guid": "bfdfe7dc352907fc980b868725387e984ec804b6dda3eec15d25f15b82ffd85a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98274695b226f17688a8bae9f030c9ef95", "guid": "bfdfe7dc352907fc980b868725387e98b3aebc249cd9fb45aa8bbf33df85cd84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98273ad36183871f7d65111592f846fb9f", "guid": "bfdfe7dc352907fc980b868725387e988a0a9c33c751f5dfa4757728b1fbc329"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d35ed685b0a8f35bc8dfd9255c756bb", "guid": "bfdfe7dc352907fc980b868725387e98f4fb282b7d38cb8899e33d56fbb93673"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806b5f5e6a0dec94525f98a705725a75a", "guid": "bfdfe7dc352907fc980b868725387e988821eb4e486a6e33c9a80eb54e31bb9f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804d0d6b33712f04ef6d23c5967ce3993", "guid": "bfdfe7dc352907fc980b868725387e985acefa8ecbd9e4a1327885085f032f17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e214b715d33139b2aaa489bfebc8034b", "guid": "bfdfe7dc352907fc980b868725387e98ae6bf7d7d56bdbfe8ae2f28766b31186"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7e98f1880565eb8d9ac586c1aa1b21e", "guid": "bfdfe7dc352907fc980b868725387e98d397faf9bd197e25e5f38b89039fb785"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e035b958c08c9fde23f66556e9180d0e", "guid": "bfdfe7dc352907fc980b868725387e988639a3e019c585d37d5062ba061a7519"}], "guid": "bfdfe7dc352907fc980b868725387e9874a3cbee00f90174046fc265c2e7b89f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989cc950460cd45fefc4c01dc41a9ce577", "guid": "bfdfe7dc352907fc980b868725387e983b5deb6fa15b213f56f39f6013ddc277"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850edfe48d26d56c49ac8c1037d2cf42d", "guid": "bfdfe7dc352907fc980b868725387e9828337762fe38ff1aeb0fe3a9ef35e721"}], "guid": "bfdfe7dc352907fc980b868725387e98cbf704c2b1686c7aea0468b7c263920e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98d2ebc460606f548cd95b41635ad589a3", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e984ca36161d9d9a5f5e9f86650714bb439", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}