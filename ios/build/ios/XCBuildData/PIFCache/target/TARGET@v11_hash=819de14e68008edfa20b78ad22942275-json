{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98feb505df6b4c328d47cffbc2d2e7ea13", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982c7c2a712385f524b065ea032e6fba21", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ca2118f979fb250f5175e48f7e19d4e9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c39aa991e6226ff6b33cacec64a75ebe", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ca2118f979fb250f5175e48f7e19d4e9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98828032a7f20d1edc5c76d9a18c5046f3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9833d052c352cb46bc2fcacaa57c7995c9", "guid": "bfdfe7dc352907fc980b868725387e986b8ed9cfa888f5099e1a702764db1ffe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98184f1f14072f43bbbd2afb1e7f10c68c", "guid": "bfdfe7dc352907fc980b868725387e98bfc7a43124ee281a5b55ca4cfaa2a563", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849a687c053ce01cef7ccdf863a9dbc69", "guid": "bfdfe7dc352907fc980b868725387e988627bc233c1ab05a1965ecf39818253d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885cb14a2df80147de4c5bfdbb586287b", "guid": "bfdfe7dc352907fc980b868725387e984d6d936921a6a5ec1f162ba66ef3d572", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d96343a4d75610f78484f0e582ceee2", "guid": "bfdfe7dc352907fc980b868725387e988bbec360755e8115e1136ec106afbb64", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7d4bcb5b77b9bd11827cc9488c12c7d", "guid": "bfdfe7dc352907fc980b868725387e9818b56f235cb95ff69cca2c9d8381fa20", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d22e1b3bec73eb9c9c79b6f5414e707f", "guid": "bfdfe7dc352907fc980b868725387e987c0ba33e02873647a0fa3fe1c37f27a8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aac9f747f8103941b9b0730a573135ce", "guid": "bfdfe7dc352907fc980b868725387e98f8732c661f6335b6cbb272b4d4c6fd3e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981722e076b375ea05d24f639ae59febbd", "guid": "bfdfe7dc352907fc980b868725387e980b9f303f8c9064469c1bf584f0ec0610"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e56de220e9ceaec3e44fb76748111d07", "guid": "bfdfe7dc352907fc980b868725387e98282908915ad870cdb26d22dbea0869d2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ba4fc783cc7ea0dbcb82f47d0779ae5", "guid": "bfdfe7dc352907fc980b868725387e98c43ee78b212d6ce6a588797b67b1ff79", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98545cc8cb48b116a67ad12e241ac99f3b", "guid": "bfdfe7dc352907fc980b868725387e98ea127127aa8fa43b1d3604052f2032bc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989da313e62baf7fd9f80ae8bfcc8cfb21", "guid": "bfdfe7dc352907fc980b868725387e9848e4f4f3b80772612a03c2454846bf5b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d24bcd95855a27c417ae73b441e4ba9", "guid": "bfdfe7dc352907fc980b868725387e98e07c024c1258dda777f42cda5899ef80", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d09ee551dabb81be837368a4e87d5b24", "guid": "bfdfe7dc352907fc980b868725387e983a8264647a36a10fec709966fa7fa4ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1e175c59a27849c8a84d0f1045aab4c", "guid": "bfdfe7dc352907fc980b868725387e98fd8d5d4c0b9ced2c87a1c4a984d77b5f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b80edd10a473eaa9db3879f9d59fec9a", "guid": "bfdfe7dc352907fc980b868725387e98ba0a4b152cd236a00aff28821dbfc1a5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981818f9ffe1993e2f21440207fdf19840", "guid": "bfdfe7dc352907fc980b868725387e98ce0547c5928568ce57a88e92ecf3b141", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff07508fc80aace8c3531d4fe002349e", "guid": "bfdfe7dc352907fc980b868725387e98a8363cecf4d5c15ebe92e478a447dbae", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da22c9745c1a8c1b080603bce75ff68a", "guid": "bfdfe7dc352907fc980b868725387e98666501584837f84a38f5ab7a5ba26098", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98073a9c97b85ae0275eb7e415e8daebfd", "guid": "bfdfe7dc352907fc980b868725387e984f19bdf2252439d6641b95fe35a51358"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9fcf797f0ab631ef5ddbebd9bde3f1b", "guid": "bfdfe7dc352907fc980b868725387e9875d95e5391453ebc149874bd97101791"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcead0234bfc0d3da216c4ccf92a7d3a", "guid": "bfdfe7dc352907fc980b868725387e98e169af6ec029e7ceeb01c1aeaa00dc26", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980da32c2b54007b029aacd1e4a673ff4c", "guid": "bfdfe7dc352907fc980b868725387e980e3afc0c534deafddd29a3c5aeeeebd2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1e7b972b464a61f1fcf0616030a0b3d", "guid": "bfdfe7dc352907fc980b868725387e98664def3239527a92d0f012e9b37f4793"}], "guid": "bfdfe7dc352907fc980b868725387e98be7fdc02c0f6b1ca195c0fe270349b4e", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f0503498de6dc685b328634bedaf043b", "guid": "bfdfe7dc352907fc980b868725387e98ba0189adf1b34d5155783e8a7cc103e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986011f067be1597240fe9f7a069573435", "guid": "bfdfe7dc352907fc980b868725387e98f823a2959a2c0466b0ed93a2d5be8ce7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823d09662c9c7f2673aedc4b528f32853", "guid": "bfdfe7dc352907fc980b868725387e984b669f897f0cdcd60fd516d661a23f0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e67de7fa1ad37271b486502370c389a9", "guid": "bfdfe7dc352907fc980b868725387e98a9dd456c39e4c363b30e4cccc6aa3cc8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983aab3c419fb32b288443445ad9984272", "guid": "bfdfe7dc352907fc980b868725387e98effcf3b857892a9c049b4babc445d2a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad4e9564869c1ed97abc31e553551bb8", "guid": "bfdfe7dc352907fc980b868725387e9842f1af16bac9647bb13e6375e60d23cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b90e109fac3508f52817b9f6c0cf5969", "guid": "bfdfe7dc352907fc980b868725387e98ce04a4b433b555d741720d808a313088"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6d8a37148de213e79b4c31bb13fba47", "guid": "bfdfe7dc352907fc980b868725387e988bd7bea9b3def89d1c61a2148ace8230"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986675325c1bd6bbb5c916d7df3e3b5648", "guid": "bfdfe7dc352907fc980b868725387e98ac9dcd99e072092437579e48330ebd53"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98349e18077f7af2cae29853caf4f86788", "guid": "bfdfe7dc352907fc980b868725387e9899164d0f8cd496cad40512aebf282af0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988816f7250bb36302597bfbc98991263e", "guid": "bfdfe7dc352907fc980b868725387e989fb8d157a1b292bdb99a74bacbfb9d6d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842c488abb87ee65abe1d49247a40fc37", "guid": "bfdfe7dc352907fc980b868725387e9810f5646ac8fd820dcb3683e6da825e4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d75425674b0c3ba24a5c5407ea9d74be", "guid": "bfdfe7dc352907fc980b868725387e9844b415751d950cbebef277ce8e01a249"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844ed2000b50256755cdca066d42a448d", "guid": "bfdfe7dc352907fc980b868725387e98c801f12596814ec1a53938512fa06953"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f9d79d32d127ccbd199a6b60c7f000a", "guid": "bfdfe7dc352907fc980b868725387e9874a0c4067d4e62ab77fbf9e86bcef866"}], "guid": "bfdfe7dc352907fc980b868725387e98f476ebfb2326e3af0cc9681a385bfd3f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989cc950460cd45fefc4c01dc41a9ce577", "guid": "bfdfe7dc352907fc980b868725387e98e47d596bbcd4150adadd94f911202654"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987aa69c67fdaf8a691629a3c30715ea6b", "guid": "bfdfe7dc352907fc980b868725387e98d385f27f35570a5d12b2630f23ba1332"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a9f5934f3f4312fc977ab4f7e18cddb", "guid": "bfdfe7dc352907fc980b868725387e986ad1002f3fda1c9b93ca796dd8669305"}], "guid": "bfdfe7dc352907fc980b868725387e98382cb31882e80ee35f9d5684d86a4a53", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98920ed85c0f22087e329bfdf3d4a288a7", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e9866328316df2bd6f55c04966369721c7a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}