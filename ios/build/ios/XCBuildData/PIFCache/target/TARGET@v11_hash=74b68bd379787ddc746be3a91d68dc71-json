{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9873f9207c09026090a93030bb6a8c06f8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9854badb8186aaa798fe9da2cab4d6e24d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ac30085cca46c0a8b42f8c6f1932faa0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98afc51990e2d0b6d0c89b9dcbc170715b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ac30085cca46c0a8b42f8c6f1932faa0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b7511db856a116b39ecdd5955d6a3f94", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f72e2454448d1af94b6f7762fe3645df", "guid": "bfdfe7dc352907fc980b868725387e980581bab3be1111680d72404042fb8524", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987778205794d0b78e3324efc038ff93b4", "guid": "bfdfe7dc352907fc980b868725387e98191e9d7732575fb8c9fd6b243c6d607e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986dbb6c546e6a0b076eb6061d7bcc05bd", "guid": "bfdfe7dc352907fc980b868725387e985072f8c45de7a80a651b0e979b160a19", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a9a0b4a0e6f1535d29af7ddaa05c8c4", "guid": "bfdfe7dc352907fc980b868725387e9805bdb53457ca876c5d3707674f823f36", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5dd799a4383245e36f44d6ee2d4ced5", "guid": "bfdfe7dc352907fc980b868725387e98db71b03a7df2a886332eec3987af9d67", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f55153ecf5adb8504dcb65b6058dee9", "guid": "bfdfe7dc352907fc980b868725387e98cd42689bf390ebb647039d7cd3d43242", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e26077f6af92792fa042922a8292050", "guid": "bfdfe7dc352907fc980b868725387e98b6570ccd8830f5f3bb61b3233147bfdd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c582a76ff24821353e997a9f264493c", "guid": "bfdfe7dc352907fc980b868725387e9837e9d665bd0674208e1179e05b2fc253", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f247ef97140d31bcbaea356d6052029b", "guid": "bfdfe7dc352907fc980b868725387e982e8a0afb0eb6f718cad71d41aefc009e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7d99dd19d65cd1c517eefae1d9b97c4", "guid": "bfdfe7dc352907fc980b868725387e980d0c94a43c04834a60c725cab04f6c07", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c92a5bfbe248cad7009f484f87f438c8", "guid": "bfdfe7dc352907fc980b868725387e989cd4154437eae67c2385b65e0ea10c29", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98013c8d770f911b07d908a1fdf96f1112", "guid": "bfdfe7dc352907fc980b868725387e982516afbee09d3915f0ceb934bdbff2e9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd1e2e7e609465b5448bf2b00d12086d", "guid": "bfdfe7dc352907fc980b868725387e989afbc844e1d8f43472781b9836d60774", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a2ad7e97396433d973c3eef36d21050", "guid": "bfdfe7dc352907fc980b868725387e982d5c386a494b82629fe6696485ea59c0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890d56e74de3bda83cf2c70b7e148627a", "guid": "bfdfe7dc352907fc980b868725387e9841a44974a68203a07e20fac7df8abd59", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894e2d9797b34a392b7f03d66a446d59d", "guid": "bfdfe7dc352907fc980b868725387e981e5e7272236e2e901e2dc194161715f2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987de90a2847ef3d991ed0a533f93fff5c", "guid": "bfdfe7dc352907fc980b868725387e9875891c55b6d2dadd278b361f3dbf44d3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6c2aee05bfffc5b7ed3fdd4a152a3df", "guid": "bfdfe7dc352907fc980b868725387e98ed2fe786e3f35886e60f700ec5e812e3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ee2f51e51bd13a2fbdad228bac118ad", "guid": "bfdfe7dc352907fc980b868725387e982cda64d56dd12f4b3036d3d5bf5e2d96", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ec71480b0dcc149736bd520da84245c", "guid": "bfdfe7dc352907fc980b868725387e98d2eef05e920681215eb98f5f5c6eb89d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e34fe2883a1ab8785f0907d491c197b1", "guid": "bfdfe7dc352907fc980b868725387e98448c0d0e26e6f847dc662173e512e365", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985dbb148b4a635e4a877846dd3e48a017", "guid": "bfdfe7dc352907fc980b868725387e98dea27bb24bbc5619bff5e51ab5e6d8e4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f44ca922224872416dcabfe04e9d9f8a", "guid": "bfdfe7dc352907fc980b868725387e98ae0d6f935df170c4e13259eb6d5ae289", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e97c7505e5f1e996d57bd906f56d34a", "guid": "bfdfe7dc352907fc980b868725387e9822be2461c088eb9fd805c1905f776775", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802f7b55ff9c0da42076be49a91684851", "guid": "bfdfe7dc352907fc980b868725387e9869827072b67b12fd249a6ee6220478f8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c980976af5cf31f34515e82b266cf693", "guid": "bfdfe7dc352907fc980b868725387e98102cd0ed44ba0b0b84fada8609aa83e8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dfa67811d366affa8942424327a70feb", "guid": "bfdfe7dc352907fc980b868725387e98feab7154f09ec41f13c187801af06c9e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3cc427ea94ea0eeb896ab7287c09dc8", "guid": "bfdfe7dc352907fc980b868725387e9899029f2af52d6a7f9e20217263ddcde3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae122fcaf57a6160311694f168b06709", "guid": "bfdfe7dc352907fc980b868725387e98535c701d0c6fe6a9da48d2bc683d93b5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806ae4cbd6c43d320ae37a6734f1f7352", "guid": "bfdfe7dc352907fc980b868725387e98290913829cbd3b697ce237b8c846594f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9813944b048fe87668a569863888c291f0", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9828bdd8103a62801e2de14699d321da76", "guid": "bfdfe7dc352907fc980b868725387e98a7c3d9d04380692878f1a8b822f48ad3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc70d4f39e9f8af2666a94e94f9fa641", "guid": "bfdfe7dc352907fc980b868725387e98f8878d8a41f9ac22d52fbe3ae3310f64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805812a85c434ab61b7ffdb4a3e6c852f", "guid": "bfdfe7dc352907fc980b868725387e98ac924371fa4214e7e4ff1fce936a8d20"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b78bd351096c24a1275b4a44a4c797ac", "guid": "bfdfe7dc352907fc980b868725387e98a3cc1e2b05d36a5a11679d598926b47c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980aa3b3f457b2da29d2c70a0ea678fd6d", "guid": "bfdfe7dc352907fc980b868725387e9822cc45103d1800b4a5bd362a57281173"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e636b25ccf1590e053f06325c855cd00", "guid": "bfdfe7dc352907fc980b868725387e98962b4d7a699b4d064252db74c2c63c49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98638fe9af814ca3429da822c799e7f356", "guid": "bfdfe7dc352907fc980b868725387e98ae3c20000cb17f2d01f01e4187bfb8f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c0359611b0abb8bd184930b7ab1c49c", "guid": "bfdfe7dc352907fc980b868725387e989df2fb0d8b9cdcbefcac1be0c3df4d21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3dd3084f6d88b5e7b1df64eee63845f", "guid": "bfdfe7dc352907fc980b868725387e98572449b3daf5b660a2cdda751897f626"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f320ee4abe31c9b19c63b1a2c7f518e", "guid": "bfdfe7dc352907fc980b868725387e988ce3646d912a28fe56935c7ed1a2bf11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986349096abad52ede5d3d65ddb26f7aa9", "guid": "bfdfe7dc352907fc980b868725387e98c9edbb4366084e192bf895596dff0cba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9d665424674dd7176fa6542637596b8", "guid": "bfdfe7dc352907fc980b868725387e98af464b84827c37f10495fa5eeff3e4b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889b85e84db1532958722500106ce247b", "guid": "bfdfe7dc352907fc980b868725387e9835ccace90a380d02ac2a1e9f4a7ac538"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c707281f4210c615110ab0d367bb63ec", "guid": "bfdfe7dc352907fc980b868725387e986d8f54d49a3692eb545e1ee7b57ba194"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982709cd24c4d387e7415554677cde78aa", "guid": "bfdfe7dc352907fc980b868725387e986216e03620cbf6e9b9dc79a44a0e0d09"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e65ddabcdd84fc968045be2fdae2d42", "guid": "bfdfe7dc352907fc980b868725387e98db1ebbfffb7486da33b2180e70970944"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cebbaeea0f24fd44786b4355216230a8", "guid": "bfdfe7dc352907fc980b868725387e989677eaf181558ec7b096f47b709aa474"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a24cb714c62c362a5110a9a8c19a800", "guid": "bfdfe7dc352907fc980b868725387e98d052c20770a8fdc26dcde16bc55de216"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d63f1dd88fffe168f234112710f11314", "guid": "bfdfe7dc352907fc980b868725387e98bcb3c68157329a16c2ee9df90285a3e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f035ba9e61b04f987420aa5c21bd968", "guid": "bfdfe7dc352907fc980b868725387e98d79b7b899a8ea5dd26733f3b201acc25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889904f3f9834594608179ebfac17844a", "guid": "bfdfe7dc352907fc980b868725387e984fa6c191238cf67ccfb232cc5f195a76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e83489a2502e8eeacd056ec38cd3d0c", "guid": "bfdfe7dc352907fc980b868725387e981df0df5571576798f6aec0cdb0e7f7a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98436f050911fbc1debf4b0b39f21900bc", "guid": "bfdfe7dc352907fc980b868725387e98418a06de2aba884dbdb65d7ff029a688"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d764dd05557c33be3e6d4f6c8a9c768", "guid": "bfdfe7dc352907fc980b868725387e98ee6fa3194b8f6f1fba469a9e2ff800b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f883245a673ed4470784d58d2646109", "guid": "bfdfe7dc352907fc980b868725387e988f50e3aa6ce948afeb9bea479fbb9322"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a345e303f63d892a9acee25529de80fa", "guid": "bfdfe7dc352907fc980b868725387e98c0c5265cac5dd952ea8ecb806f75b03e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4abc0c1059590c73dbb4b728977397f", "guid": "bfdfe7dc352907fc980b868725387e98e5416928630a4305ed8fc9a9e230fddd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6b29f3218c7c002d1e49c1e4dc43a19", "guid": "bfdfe7dc352907fc980b868725387e985dba88333b5c55d736ebe144dfcc39cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864b5f20d834b096c1ea1e9a4e6681b22", "guid": "bfdfe7dc352907fc980b868725387e98a34e2a895aed712f84c0c85a66ceeceb"}], "guid": "bfdfe7dc352907fc980b868725387e981c2697f4f5e2977af2057e154080920e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989cc950460cd45fefc4c01dc41a9ce577", "guid": "bfdfe7dc352907fc980b868725387e989af3f61d410025668a05671088c11f60"}], "guid": "bfdfe7dc352907fc980b868725387e98d6a854c033f95f75c144844b8c6c9905", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98fdeb00320da3ca0994bcc92d9354d7fb", "targetReference": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340"}], "guid": "bfdfe7dc352907fc980b868725387e985d6dd232322bfd273c12752acbb0797a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340", "name": "FirebaseFirestore-FirebaseFirestore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98919212c22943df12241906dd601cdff4", "name": "FirebaseFirestoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}], "guid": "bfdfe7dc352907fc980b868725387e98c075cc473fa5680b867d51f1363214ff", "name": "FirebaseFirestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9809dfd848e2259e061e90089e1647f5b7", "name": "FirebaseFirestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}