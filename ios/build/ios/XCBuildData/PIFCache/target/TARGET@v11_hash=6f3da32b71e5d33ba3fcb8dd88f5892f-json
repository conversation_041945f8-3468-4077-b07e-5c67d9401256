{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b718f5f0b486866b8fc796297d0911b1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a31e2f7b3a1353955ff9a5291d8ec0ef", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b403fccc4a32717c26e51d42f40cfc4d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981d2bdb0dbdb4a2a5df852b922c4cea80", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b403fccc4a32717c26e51d42f40cfc4d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f89402eb4b9dfea06bba4e4c967c6d94", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a24fe1ff325a4d61d616380cf2eeac70", "guid": "bfdfe7dc352907fc980b868725387e980390adf9cbb3cffb2a0dc97a38fecdef", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983bd9313c7e3babe703f9b4e6d8ea831d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98975afc09bcc684df69916866786b8927", "guid": "bfdfe7dc352907fc980b868725387e98a54977e34d0d1f575c3fdc3df49f0ed0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98700775b8080a47a627b697d078d31870", "guid": "bfdfe7dc352907fc980b868725387e983e1be69c3042d07fb0462f6f675cf50d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dca9207f1e6888a6091ceab30bd13482", "guid": "bfdfe7dc352907fc980b868725387e98f4548844d9473d661e1c6d242ed30676"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f0ae56a6dee204d986ae43b13a9cc92", "guid": "bfdfe7dc352907fc980b868725387e9890ea1aa7fab9c2c46c09cc9f5f701f93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4819caa2446faaf31a40188c43e0292", "guid": "bfdfe7dc352907fc980b868725387e98c0613f05f2c55987a76cd1aec85bf20e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e83819c8afa77e34e7630350032c5089", "guid": "bfdfe7dc352907fc980b868725387e982445176957c5721e6533d1c19d26191a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da867d8be073826256438823ce15999d", "guid": "bfdfe7dc352907fc980b868725387e98514263630a0e4fcdd0fa94743fa4c7b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a15289b65ddbb01856cec1ade1a38587", "guid": "bfdfe7dc352907fc980b868725387e98354216874c5223e3a7b9f4b3c6b56a9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98732288a614dc99cfa46231ee20b903e4", "guid": "bfdfe7dc352907fc980b868725387e985004a2d160d3b20ad7efc842845b7274"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff1059028d445701cd04e79adc7d8c68", "guid": "bfdfe7dc352907fc980b868725387e98ab3e75707507979d6eb49bcc92e5d910"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98435923009d360fa8b3cc8d1d72437d5b", "guid": "bfdfe7dc352907fc980b868725387e987cdf04a8e089b752b5791bf0b8c0d3f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b6bdea4f3ad6795e21ed522b4a6aa57", "guid": "bfdfe7dc352907fc980b868725387e98ae9b0c55c58151b095d768717beab8b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98943b3820f401889a56e3893141457cb9", "guid": "bfdfe7dc352907fc980b868725387e98b778aa3138d744d07c2074eb38a3d903"}], "guid": "bfdfe7dc352907fc980b868725387e98b405e4bc185a592b447729ee463eb4b5", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989cc950460cd45fefc4c01dc41a9ce577", "guid": "bfdfe7dc352907fc980b868725387e9845cdd3212270497be95746f655d11f56"}], "guid": "bfdfe7dc352907fc980b868725387e98058d1c47914572a2ea1a4d3aea0ead05", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9827494cb1d5d5b91ba5ec2ec366361f3e", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e9835a3061a001247ac8e213f9b68d1c2dc", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}