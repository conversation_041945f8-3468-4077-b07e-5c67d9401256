{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9898786557ad40378b18e31b170ee324df", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d9383de35aed0f92050014dbe867bfb0", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9841e9675070a1b937cd94dad4f7c6e5fb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ab3acd882b95e600e846644af34aa22d", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9841e9675070a1b937cd94dad4f7c6e5fb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98835c472caf37462aea89dbbcf7f131fd", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98eddc20b21050d8d701891827dbc1d190", "guid": "bfdfe7dc352907fc980b868725387e98e1fc276e3bba0b5dbf6df33990fdb897", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0a8c1d7f586a9a1a3abc3e908ca8efa", "guid": "bfdfe7dc352907fc980b868725387e988200648fce8814c4f1f263508f8ef09c", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e981fec7cafc5946ebf76fd32b2142511a8", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d5ab369c077bf66365bf1bc717dde2e5", "guid": "bfdfe7dc352907fc980b868725387e989fed35fdba38dbdce378579a79e013e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986400cce0b96536ac6c42e9e018ebe8c7", "guid": "bfdfe7dc352907fc980b868725387e98c63dfaac86124b21b1a37fe08a79475f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988991535dcef36e0bf4a55195bb171c92", "guid": "bfdfe7dc352907fc980b868725387e9805ca993330afcc538ab4af3776c6013e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d5b3698f86b87dcd7ef8b1c125e48ab", "guid": "bfdfe7dc352907fc980b868725387e98fed10c704cd714f7483050f65eb38821"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f59d5233365db00ceb62e522e35838b", "guid": "bfdfe7dc352907fc980b868725387e98176b255f75173fba9c19105c6bb1bfcc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d95491a28f3bad78a00def45b8af6e3d", "guid": "bfdfe7dc352907fc980b868725387e988da906ec44e98d9a9302d43b6aeed573"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98449978004142dd47ff7c2aaf52196237", "guid": "bfdfe7dc352907fc980b868725387e981078400ba27505dff1c5fff5f9140198"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e6f065d3d161921878b4d794d186db1", "guid": "bfdfe7dc352907fc980b868725387e98679c909fb47c8cbfba1187415a4edd98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802a8561d60d6245086e70c68316218eb", "guid": "bfdfe7dc352907fc980b868725387e981e39926b5971aed06958ebfcdd0a9aee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f761e410ccf4734caec8b3ae965aa78", "guid": "bfdfe7dc352907fc980b868725387e9862d65063dd31abdd8e63c01786583dfd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988441f1be531b1fb695eac6e3385b56dc", "guid": "bfdfe7dc352907fc980b868725387e98f2a995d6390e3b3285d00bde4282ad5b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4e96147a4384a2e0349e18188937e95", "guid": "bfdfe7dc352907fc980b868725387e986443ac0992fb3917455416c116777328"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7936a1d898dee04b11461d2c4c8e09d", "guid": "bfdfe7dc352907fc980b868725387e98dc516754b48a4b1f0004286e66e9dce4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd0362b639a42cad90de719fe1bfbcd3", "guid": "bfdfe7dc352907fc980b868725387e98097c4c3301e9bc53b95632455c3bd404"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98824cabb48a99ba0e69269870b0b06726", "guid": "bfdfe7dc352907fc980b868725387e98a2b071769e984975126156c6bb528492"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983828b7d77b324f6fbc0624100cba28d2", "guid": "bfdfe7dc352907fc980b868725387e989133d9cf19eaa9e19f9dfda98a9dc8df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec1b5a256aa9108ec624b5245a89b8b2", "guid": "bfdfe7dc352907fc980b868725387e984020c0a8379c6ed6b14f561d73df0bbd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a4cf13046e7cc12c9b82653149faf82", "guid": "bfdfe7dc352907fc980b868725387e9862ccc79e7c20c41ccdd00806415877b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98962a77503378c5791e060b2b0bf931ad", "guid": "bfdfe7dc352907fc980b868725387e98d165b10153b8e44e50365ede2556ce76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e56e47ce03f79089b07f15575f436e76", "guid": "bfdfe7dc352907fc980b868725387e9824151ae2101fc9c8f6c3f0d05710096a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b07d00c01da95734015ba708a5b306a", "guid": "bfdfe7dc352907fc980b868725387e985018ca8533d1315bb63121ac5b722916"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e24c9c427c236e4478612bf16fdd1d39", "guid": "bfdfe7dc352907fc980b868725387e98131273b6049da6b3e0794a992f3c5eec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e63bd677a19c5abb1f397a6ca5ec35af", "guid": "bfdfe7dc352907fc980b868725387e9876a608d559f3f410d838f3cfc40db380"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d870b6e826996386bda69b14ba22974e", "guid": "bfdfe7dc352907fc980b868725387e98c8689a4253d7b4e0f7ea14cad9a5880b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981309602f1eaac0170c67ee0eed4b20c7", "guid": "bfdfe7dc352907fc980b868725387e98d7583089f887acfc156b24e1d2d475c4"}], "guid": "bfdfe7dc352907fc980b868725387e98791f1aa62a9842f68d63346d01255e3c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989cc950460cd45fefc4c01dc41a9ce577", "guid": "bfdfe7dc352907fc980b868725387e98b837e022aa07dea8e073ec35e11356a9"}], "guid": "bfdfe7dc352907fc980b868725387e98f9c4c61c45889d990dff072ad40b3fab", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98db53ecd63f2c05c2497c4997aaac5267", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981f0a8508efd61386103314ddbb82a530", "name": "FirebaseAppCheckInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e988e935c81efc4686179f554b8fe37864a", "name": "FirebaseAuthInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98f1e09b32067e7d86144abdaf0d62fddc", "name": "FirebaseStorage", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9861b2e033fd71c20add064527e8a82b5a", "name": "FirebaseStorage.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}