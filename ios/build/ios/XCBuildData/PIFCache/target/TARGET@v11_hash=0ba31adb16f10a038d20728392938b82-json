{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982d267df033717e9a100645925a6d0732", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/homebrew/Caskroom/flutter/3.29.1/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/homebrew/Caskroom/flutter/3.29.1/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988739fe12e2029ec33474d51e1c1de486", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9865a4a370b767a6c415e7d2d9093bb1f8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/homebrew/Caskroom/flutter/3.29.1/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/homebrew/Caskroom/flutter/3.29.1/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e7e194b3c7a0dcadb37a08a8959714bb", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9865a4a370b767a6c415e7d2d9093bb1f8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/homebrew/Caskroom/flutter/3.29.1/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/homebrew/Caskroom/flutter/3.29.1/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ad06093d4c8fed0ad01765d765863350", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ad30cb3475c86c4f8601849f753ec9aa", "guid": "bfdfe7dc352907fc980b868725387e982f7568700678161c9dd366d7739bf413", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98558eaeda283bae309911e4f4f8cfaef6", "guid": "bfdfe7dc352907fc980b868725387e9800a15bff833d173109d533aa8a6232dd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e5a47058a17bb32eba0aad3b2a79346", "guid": "bfdfe7dc352907fc980b868725387e9826db5b8fe18b171182f632c01bc540ee", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6c2bc85d2701811ab7a397c81b756fd", "guid": "bfdfe7dc352907fc980b868725387e9837d9f76afb7da00171d46deefaa4f547", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888c67f2833523479236af92678b2f4aa", "guid": "bfdfe7dc352907fc980b868725387e9876a6a812aa1f33c12b011f789bfe07a4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6f5b413b73bcf275243d2bc6accba09", "guid": "bfdfe7dc352907fc980b868725387e98aba112176185ed2ec6975c9d2ed344c1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f5b7018d4643735825ac3b82b7e167e", "guid": "bfdfe7dc352907fc980b868725387e986e0f408b20b33d37145b7a3c7dc810bc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841c1192d6888dd0f704208f3cf6e7230", "guid": "bfdfe7dc352907fc980b868725387e9873b365933cd6394cbc2b5dfd7477f3bc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f194e64b79f925ff3c263bdc681a91b3", "guid": "bfdfe7dc352907fc980b868725387e98a87c2ddd65037a185a858c5c94b4b74a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed8ca568c06bbae68b8b90863c74826e", "guid": "bfdfe7dc352907fc980b868725387e98689ec22dd92425c50cb82d4d1c0ed5f5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891877f21d0c3f62da847a1b34b754c46", "guid": "bfdfe7dc352907fc980b868725387e98bcbcdc0bde5708ba7ec61455025e2bc4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890363e735726c10088a3d44ec7dd5200", "guid": "bfdfe7dc352907fc980b868725387e985356332e9d3cf8a4aa123cd8d3f66c2a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980bd9974b7936e81ffe317bf247a2f3a4", "guid": "bfdfe7dc352907fc980b868725387e9846e7726e420c6c03201e64a5412f97cc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df03a080e1f5eda6596a3500caf3f3ff", "guid": "bfdfe7dc352907fc980b868725387e98140b631b5450087e29f4e84c92434e0c", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98527db3c40639cc6759f0e8015001fafc", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b18ecd1ad1fd459c2d787537445e9ac4", "guid": "bfdfe7dc352907fc980b868725387e98475fc8e8b3ec4b7dd01534402dce2e25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98395746df1b854ac8a771b401575ca336", "guid": "bfdfe7dc352907fc980b868725387e98b51d66491aa0a4db9dee4c76cecd7b9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f1114e782131a2c0e0d0d4dfdee9cbc", "guid": "bfdfe7dc352907fc980b868725387e988d3295a83087cccf2f0e75469fef4445"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98493ad3ca71a0952f77fd309b51734626", "guid": "bfdfe7dc352907fc980b868725387e98661b705a478967098f5a9112507b89fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98710779847f4067a1676e6844266f529b", "guid": "bfdfe7dc352907fc980b868725387e989be8047214e675bbe9a2f8d36857cf1c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4f862efa3d5f6c6216f5b43aa79f698", "guid": "bfdfe7dc352907fc980b868725387e98ec113775e325e5b7c78a4be55be461fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e8345903bd94e1965d07a114d0cab50", "guid": "bfdfe7dc352907fc980b868725387e98508b9c0ffb64629d64c1ae76817d767e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847aded3d0d29d59aab5986e4cab03d5d", "guid": "bfdfe7dc352907fc980b868725387e98314e302c7e4e6b3c02baa44f7c55ac2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b268989d987af3466846222fde96e94", "guid": "bfdfe7dc352907fc980b868725387e985ff014ad418470b42372f470a5acb214"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984029d6a4f00cf330a7129cfc5efa7952", "guid": "bfdfe7dc352907fc980b868725387e98d77576eebed71382a1d338c3a00ed794"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981dd3b64661512c07ba31784fe63eb4a4", "guid": "bfdfe7dc352907fc980b868725387e98412601fa12c727df324dc2cf1c8f6c64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98baeeb70d53fda439cbb031d6a12d0c11", "guid": "bfdfe7dc352907fc980b868725387e98e0389212cd483a7d80f688745b1b8b12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98595ef1a14fdc0a2488d7121674523ceb", "guid": "bfdfe7dc352907fc980b868725387e98320acb60974eef673b47d10cb4905d76"}], "guid": "bfdfe7dc352907fc980b868725387e98445c13b134f6717c552fb845dd07cf6c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989cc950460cd45fefc4c01dc41a9ce577", "guid": "bfdfe7dc352907fc980b868725387e987208fa6207d938fe537d0e8359bf5699"}], "guid": "bfdfe7dc352907fc980b868725387e98ee8cf58f2eaf1185d54859fdaf1a4231", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98e1b65cad46427b34676adbcb934284e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d57b8bce60a0f11113f4cff532db68d3", "name": "Firebase"}, {"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987f74324bfc5c78140e34d510e26e00c1", "name": "firebase_core"}], "guid": "bfdfe7dc352907fc980b868725387e989840e8244cb75f43b3efe8cd6dec5ec5", "name": "cloud_firestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98321793542cff8793cba84baa893d5044", "name": "cloud_firestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}